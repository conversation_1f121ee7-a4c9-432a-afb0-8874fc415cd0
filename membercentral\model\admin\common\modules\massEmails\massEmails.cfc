<cfcomponent>

	<cffunction name="prepMassEmails" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="recipientType" type="string" required="true">
		<cfargument name="strResourceTitle" type="struct" required="true">
		<cfargument name="strFilters" type="struct" required="true">
		<cfargument name="arrRecipientModes" type="array" required="true">
		<cfargument name="mergeCodeInstructionsLink" type="string" required="true">
		<cfargument name="emailTemplateTreeCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objET = CreateObject("component","model.admin.emailTemplates.emailTemplates")>
		<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cfset local.sendMassEmailsLink = "/?pg=admin&mode=stream&mca_ajaxlib=massEmails&mca_ajaxfunc=sendMassEmails">
		<cfset local.qryEmailTemplates = local.objET.getCategoriesAndTemplatesForTree(siteID=arguments.siteID, treeCode=arguments.emailTemplateTreeCode)>
		<cfset local.qryEmailTemplateCategories = local.objET.getCategoriesForTree(siteID=arguments.siteID, treeCode=arguments.emailTemplateTreeCode)>
		<cfset local.qryOrgEmailTags = application.objOrgInfo.getOrgEmailTagTypes(orgID=local.siteInfo.orgID)>
		<cfset local.showAddOptOutStep = checkShowAddOptOutStep(resourceType=arguments.resourceType, recipientType=arguments.recipientType)>
		<cfset local.qryOrgOptOutLists = createObject("component","model.admin.emailPreferences.emailPreferences").getConsentLists(orgID=local.siteInfo.orgID, mode="Opt-Out")>
		<cfset local.showOptOutStep = local.qryOrgOptOutLists.recordCount AND local.showAddOptOutStep>
			
		<cfset local.recipientsListLink = "/?pg=admin&mca_jsonlib=mcdatatable&com=massEmailsJSON&meth=getRecipients&mode=stream">
		<cfset local.manageListLink =  CreateObject('component','model.admin.admin').buildLinkToTool(toolType='EmailPreferencesAdmin',mca_ta='listConsentLists')>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_massEmails.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEmailTemplateContent" access="public" output="false" returntype="struct">
		<cfargument name="emailTemplateID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.qryTemplateData = getEmailTemplateData(emailTemplateID=arguments.emailTemplateID)>
		<cfset local.retStruct.emailcontent = local.qryTemplateData.rawContent>
		<cfset local.retStruct.subjectLine = local.qryTemplateData.subjectLine>
		<cfset local.retStruct.emailfromName = local.qryTemplateData.emailfromName>
		<cfset local.retStruct.emailfrom = local.qryTemplateData.emailFrom>
		<cfset local.retStruct.contentVersionID = local.qryTemplateData.contentVersionID>
		<cfset local.retStruct.success = true>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getEmailTemplateData" access="public" output="false" returntype="query">
		<cfargument name="emailTemplateID" type="numeric" required="true">

		<cfset var qryEmailTemplateData = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryEmailTemplateData">
			select etc.rawContent, et.subjectLine, et.emailFromName, et.emailFrom, etc.contentVersionID, et.contentID, etc.contentTitle
			from dbo.et_emailTemplates et
			cross apply dbo.fn_getContent(et.contentID,1) as etc
			where et.templateID = <cfqueryparam value="#arguments.emailTemplateID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn qryEmailTemplateData>
	</cffunction>

	<cffunction name="getPreviewEmailMessage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="itemID" type="string" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="recipientType" type="string" required="true">
		<cfargument name="recipientMode" type="string" required="true">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="subjectLine" type="string" required="true">
		<cfargument name="emailFrom" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>

		<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.templateContent, siteID=arguments.mcproxy_siteID)>
		<cfset local.strResourceEmailData = getResourceEmailDataForEmailing(siteID=arguments.mcproxy_siteID, orgID=local.mc_siteInfo.orgID, itemID=arguments.itemID, 
				resourceType=arguments.resourceType, recipientType=arguments.recipientType, recipientMode=arguments.recipientMode, mode='preview')>
		<cfset local.parseContent = parseContentWithMergeCodes(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, content=local.templateContent, resourceType=arguments.resourceType, 
				recipientType=arguments.recipientType, recipientMode=arguments.recipientMode, subjectLine=arguments.subjectLine, emailFrom=arguments.emailFrom, 
				strResourceEmailData=local.strResourceEmailData)>
	
		<cfset local.emailTitle = local.mc_siteInfo.orgName>
		<cfset local.retStruct.parsedContent = local.parseContent.content>
		<cfset local.retStruct.templateDisp = application.objEmailWrapper.wrapMessage(emailTitle=local.emailTitle, emailContent=local.parseContent.content, sitecode=arguments.mcproxy_siteCode)>
		<cfset local.retStruct.subjectline = urlEncodedFormat(local.parseContent.subjectLine)>
		<cfset local.retStruct.emailFrom = urlEncodedFormat(local.parseContent.emailFrom)>
		<cfif structKeyExists(local.strResourceEmailData,"mailAttach")>
			<cfset local.retStruct.mailAttach = local.strResourceEmailData.mailAttach>
		</cfif>
		<cfset local.retStruct.itemID = arguments.itemID>
		<cfset local.retStruct.success = true>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getResourceEmailDataForEmailing" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="itemID" type="string" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="recipientType" type="string" required="true">
		<cfargument name="recipientMode" type="string" required="true">
		<cfargument name="mode" type="string" required="true" hint="preview or testemail">

		<cfset var strResourceEmailData = structNew()>

		<cfswitch expression="#arguments.resourceType#">
			<cfcase value="Contributions">
				<cfset strResourceEmailData = CreateObject("component","model.admin.contributions.contributions").getContributorDataForEmailing(contributionID=val(arguments.itemID))>
			</cfcase>
			<cfcase value="Events">
				<cfset strResourceEmailData = CreateObject("component","model.admin.events.event").getRegistrantDataForEmailing(registrantID=val(arguments.itemID), recipientType=arguments.recipientType, 
													mode=arguments.mode)>
			</cfcase>
			<cfcase value="Groups">
				<cfset strResourceEmailData = CreateObject("component","model.admin.groups.groups").getGroupMemberDataForEmailing(orgID=arguments.orgID, groupID=val(ListFirst(arguments.itemID,'|')), memberID=val(ListLast(arguments.itemID,'|')))>
			</cfcase>
			<cfcase value="Invoices">
				<cfset strResourceEmailData = CreateObject("component","model.admin.transactions.invoiceAdmin").getInvoiceDataForEmailing(orgID=arguments.orgID, invoiceIDList=arguments.itemID)>
			</cfcase>
			<cfcase value="Notes,Relationships,Member History">
				<cfset strResourceEmailData = CreateObject("component","model.admin.memberHistory.memberHistory").getMemHistoryForEmailing(siteID=arguments.siteID, historyID=val(arguments.itemID))>
			</cfcase>
			<cfcase value="Project">
				<cfset strResourceEmailData = CreateObject("component","model.admin.projects.project").getProjectDataForEmailing(siteID=arguments.siteID, projectID=val(ListFirst(arguments.itemID,'|')), memberID=val(ListLast(arguments.itemID,'|')), recipientType=arguments.recipientType)>
			</cfcase>
			<cfcase value="SeminarWebLive">
				<cfset strResourceEmailData = CreateObject("component","model.admin.seminarWeb.seminarWebSWL").getEnrollmentDataForEmailing(enrollmentID=arguments.itemID)>
			</cfcase>
			<cfcase value="SeminarWebOnDemand">
				<cfset strResourceEmailData = CreateObject("component","model.admin.seminarWeb.seminarWebSWOD").getEnrollmentDataForEmailing(enrollmentID=arguments.itemID)>
			</cfcase>
			<cfcase value="Tasks">
				<cfset strResourceEmailData = CreateObject("component","model.admin.tasks.task").getTaskDataForEmailing(taskIDList=arguments.itemID, recipientMode=arguments.recipientMode)>
			</cfcase>
		</cfswitch>

		<cfif arguments.resourceType eq 'Tasks'>
			<cfset strResourceEmailData.recipientmemberID = val(strResourceEmailData.recipientID)>
		<cfelse>
			<cfset strResourceEmailData.recipientmemberID = val(strResourceEmailData.qryData.memberID)>
		</cfif>

		<cfreturn strResourceEmailData>
	</cffunction>

	<cffunction name="parseContentWithMergeCodes" access="private" output="no" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="content" type="string" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="recipientType" type="string" required="true">
		<cfargument name="recipientMode" type="string" required="true">
		<cfargument name="subjectLine" type="string" required="true">
		<cfargument name="emailFrom" type="string" required="true">	
		<cfargument name="strResourceEmailData" type="struct" required="true">

		<cfset var local = StructNew()>

		<cfset arguments.content = urlDecode(arguments.content)>
		<cfset arguments.subjectLine = urlDecode(arguments.subjectLine)>
		<cfset arguments.emailFrom = urlDecode(arguments.emailFrom)>

		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.mc_siteInfo.mainhostname>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=val(arguments.strResourceEmailData.recipientmemberID))>
		<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.mc_siteInfo.orgID, memberID=local.memberInfo.memberID, content="#arguments.content##arguments.subjectLine##arguments.emailFrom#")>	
		
		<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=local.memberInfo.FirstName, middleName=local.memberInfo.MiddleName,
							 			lastName=local.memberInfo.LastName, company=local.memberInfo.Company, suffix=local.memberInfo.Suffix, prefix=local.memberInfo.Prefix, 
							 			professionalSuffix=local.memberInfo.professionalSuffix, membernumber=local.memberInfo.membernumber, orgcode=local.memberInfo.orgcode,
							 			siteID=arguments.siteID, hostname=local.thisHostname, useRemoteLogin=local.mc_siteInfo.useRemoteLogin }>

		<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
			<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
				<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
				<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
			</cfif>
		</cfloop>

		<!--- resource specific merge codes --->
		<cfif structKeyExists(arguments.strResourceEmailData,"arrResTypeMergeCodes") and arrayLen(arguments.strResourceEmailData.arrResTypeMergeCodes)>
			<cfloop array="#arguments.strResourceEmailData.arrResTypeMergeCodes#" index="local.thisMergeCode">
				<cfif findNoCase("[[#local.thisMergeCode.mergeCode#]]", '#arguments.content##arguments.subjectLine##arguments.emailFrom#')>
					<cfset local.colValue = arguments.strResourceEmailData.qryData[local.thisMergeCode.columnName]>
					<cfif len(local.colValue)>
						<cfif local.thisMergeCode.isDateField>
							<cfset arguments.content = replaceNoCase(arguments.content,"[[#local.thisMergeCode.mergeCode#]]",dateFormat(local.colValue,'m/d/yyyy'),"all")>
							<cfset arguments.subjectLine = replaceNoCase(arguments.subjectLine,"[[#local.thisMergeCode.mergeCode#]]",dateFormat(local.colValue,'m/d/yyyy'),"all")>
							<cfset arguments.emailFrom = replaceNoCase(arguments.emailFrom,"[[#local.thisMergeCode.mergeCode#]]",dateFormat(local.colValue,'m/d/yyyy'),"all")>
						<cfelseif local.thisMergeCode.isAmountField>
							<cfset arguments.content = replaceNoCase(arguments.content,"[[#local.thisMergeCode.mergeCode#]]",dollarFormat(local.colValue),"all")>
							<cfset arguments.subjectLine = replaceNoCase(arguments.subjectLine,"[[#local.thisMergeCode.mergeCode#]]",dollarFormat(local.colValue),"all")>
							<cfset arguments.emailFrom = replaceNoCase(arguments.emailFrom,"[[#local.thisMergeCode.mergeCode#]]",dollarFormat(local.colValue),"all")>
						<cfelse>
							<cfset arguments.content = replaceNoCase(arguments.content,"[[#local.thisMergeCode.mergeCode#]]",local.colValue,"all")>
							<cfset arguments.subjectLine = replaceNoCase(arguments.subjectLine,"[[#local.thisMergeCode.mergeCode#]]",local.colValue,"all")>
							<cfset arguments.emailFrom = replaceNoCase(arguments.emailFrom,"[[#local.thisMergeCode.mergeCode#]]",local.colValue,"all")>
						</cfif>
					<cfelse>
						<cfset arguments.content = replaceNoCase(arguments.content,"[[#local.thisMergeCode.mergeCode#]]","","all")>
						<cfset arguments.subjectLine = replaceNoCase(arguments.subjectLine,"[[#local.thisMergeCode.mergeCode#]]","","all")>
						<cfset arguments.emailFrom = replaceNoCase(arguments.emailFrom,"[[#local.thisMergeCode.mergeCode#]]","","all")>
					</cfif>
				</cfif>
			</cfloop>
		</cfif>

		<cfset local.strContentArgs = { content=arguments.content, memberdata=local.tempMemberData, orgcode=local.mc_siteInfo.orgcode, sitecode=arguments.siteCode, outputShortCodeDataToBrowser=false }>
		<cfset local.strSubjectArgs = { content=arguments.subjectLine, memberdata=local.tempMemberData, orgcode=local.mc_siteInfo.orgcode, sitecode=arguments.siteCode, outputShortCodeDataToBrowser=false }>
		<cfset local.strEmailFromArgs = { content=arguments.emailFrom, memberdata=local.tempMemberData, orgcode=local.mc_siteInfo.orgcode, sitecode=arguments.siteCode, outputShortCodeDataToBrowser=false }>

		<!--- if resource type supports extended merge codes --->
		<cfif len(arguments.strResourceEmailData.extendedLinkedMergeCode)>
			<cfset local.linkedMemberInfo = application.objMember.getMemberInfo(memberID=val(arguments.strResourceEmailData.qryData.linkMemberID))>
			<cfset local.qryLinkedMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.mc_siteInfo.orgID, memberID=val(local.linkedMemberInfo.memberID), 
				content="#arguments.content##arguments.subjectLine##arguments.emailFrom#", codeprefix="#arguments.strResourceEmailData.extendedLinkedMergeCode#.")>

			<cfset local.excludeKeyList = "orgcode|siteID|hostname|useRemoteLogin">
			<cfset local.tempLinkedMemberData = structNew()>
			<cfloop list="#structKeyList(local.tempMemberData)#" index="local.thisKey">
				<cfif not listFindNoCase(local.excludeKeyList,local.thisKey,"|") and structKeyExists(local.linkedMemberInfo,local.thisKey)>
					<cfset local.tempLinkedMemberData["linked.#local.thisKey#"] = local.linkedMemberInfo[local.thisKey][1]>
				<cfelse>
					<cfset local.tempLinkedMemberData[local.thisKey] = local.tempMemberData[local.thisKey]>
				</cfif>
			</cfloop>

			<cfloop array="#getMetaData(local.qryLinkedMemberFields)#" index="local.thisColumn">
				<cfif NOT StructKeyExists(local.tempLinkedMemberData,local.thisColumn.Name)>
					<cfset local.thisTempVal = local.qryLinkedMemberFields[local.thisColumn.Name][1]>
					<cfset structInsert(local.tempLinkedMemberData,local.thisColumn.Name,local.thisTempVal,true)>
				</cfif>
			</cfloop>

			<cfset local.strContentArgs.linkedmemberdata = local.tempLinkedMemberData>
			<cfset local.strSubjectArgs.linkedmemberdata = local.tempLinkedMemberData>
			<cfset local.strEmailFromArgs.linkedmemberdata = local.tempLinkedMemberData>
		</cfif>

		<cfswitch expression="#arguments.resourceType#">
			<cfcase value="Events">
				<cfif structKeyExists(arguments.strResourceEmailData,"eventData")>
					<cfset local.strContentArgs.eventData = arguments.strResourceEmailData.eventData>
					<cfset local.strSubjectArgs.eventData = arguments.strResourceEmailData.eventData>
					<cfset local.strEmailFromArgs.eventData = arguments.strResourceEmailData.eventData>
				</cfif>
			</cfcase>
			<cfcase value="Invoices">
				<cfif structKeyExists(arguments.strResourceEmailData,"invoiceList")>
					<cfset local.strContentArgs.content = "<div>#arguments.content#</div><br/>#arguments.strResourceEmailData.invoiceList#">
				</cfif>
			</cfcase>
			<cfcase value="Project">
				<cfif arguments.recipientType eq 'Solicitors' and structKeyExists(arguments.strResourceEmailData,"taskData")>
					<cfset structInsert(arguments.strResourceEmailData.taskData,"hostname",local.thisHostname)>
					<cfset local.strContentArgs.taskData = arguments.strResourceEmailData.taskData>
					<cfset local.strSubjectArgs.taskData = arguments.strResourceEmailData.taskData>
					<cfset local.strEmailFromArgs.taskData = arguments.strResourceEmailData.taskData>
				</cfif>
			</cfcase>
			<cfcase value="Tasks">
				<cfif structKeyExists(arguments.strResourceEmailData,"taskData")>
					<cfset structInsert(arguments.strResourceEmailData.taskData,"hostname",local.thisHostname)>
					<cfset local.strContentArgs.taskData = arguments.strResourceEmailData.taskData>
					<cfset local.strSubjectArgs.taskData = arguments.strResourceEmailData.taskData>
					<cfset local.strEmailFromArgs.taskData = arguments.strResourceEmailData.taskData>
				</cfif>

				<cfset local.strContentArgs.sendermemberdata = getMemberInfoStruct(memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID, siteCode=arguments.siteCode, 
					content=arguments.content, subjectLine=arguments.subjectLine, emailFrom=arguments.emailFrom, codeprefix="sender")>

				<cfif structKeyExists(arguments.strResourceEmailData,"taskList")>
					<cfset local.strContentArgs.content = "<div>#arguments.content#</div><br/>#arguments.strResourceEmailData.taskList#">
				</cfif>
			</cfcase>
		</cfswitch>

		<cfset local.strMergedContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strContentArgs)>
		<cfset local.strMergedSubjectContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strSubjectArgs)>
		<cfset local.strMergedEmailFromContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strEmailFromArgs)>
		

		<cfset local.strReturn = { content=local.strMergedContent.content, subjectLine=local.strMergedSubjectContent.content, EmailFrom=local.strMergedEmailFromContent.content }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="sendTestEmailMessage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="itemID" type="string" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="recipientType" type="string" required="true">
		<cfargument name="recipientMode" type="string" required="true">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="subjectLine" type="string" required="true">
		<cfargument name="emailFromName" type="string" required="true">
		<cfargument name="emailFrom" type="string" required="true">
		<cfargument name="consentListIDs" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.retStruct = { success = false, outputMessage = "" }>

		<cfif not len(session.cfcuser.memberData.email) or arguments.itemID is 0>
			<cfset local.retStruct.success = false>
			<cfreturn local.retStruct>
		</cfif>

		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>
		
		<!--- message prep --->
		<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.templateContent, siteID=arguments.mcproxy_siteID)>
		<cfset local.strResourceEmailData = getResourceEmailDataForEmailing(siteID=arguments.mcproxy_siteID, orgID=local.mc_siteInfo.orgID, itemID=arguments.itemID, 
					resourceType=arguments.resourceType, recipientType=arguments.recipientType, recipientMode=arguments.recipientMode, 
					mode='testemail')>
		<cfset local.parseContent = parseContentWithMergeCodes(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, content=local.templateContent, resourceType=arguments.resourceType, 
					recipientType=arguments.recipientType, recipientMode=arguments.recipientMode, subjectLine=arguments.subjectLine, emailFrom=arguments.emailFrom, 
					strResourceEmailData=local.strResourceEmailData)>
					
		<cfif structKeyExists(local.strResourceEmailData,"mailAttach") and arrayLen(local.strResourceEmailData.mailAttach)>
			<cfset local.mailAttach = local.strResourceEmailData.mailAttach>
		<cfelse>
			<cfset local.mailAttach = arrayNew(1)>
		</cfif>

		<cfquery name="local.qryInConsentList" datasource="#application.dsn.membercentral.dsn#">
			SELECT 1, cl.consentListName
			FROM platformMail.dbo.email_consentLists cl
			INNER JOIN platformMail.dbo.email_consentListMembers cm
			ON cl.consentListID = cm.consentListID
			WHERE cl.consentListID IN (
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListIDs#" list="true">
			)
			AND cm.email = <cfqueryparam cfsqltype="CF_SQL_VARCHAR"
										value="#session.cfcuser.memberData.email#">
		</cfquery>
		<cfif local.qryInConsentList.recordCount>
			<cfset local.retStruct.outputmessage =
			"The test message to email #session.cfcuser.memberData.email#" & " will not be sent as it is in the opt-out list: '#local.qryInConsentList.consentListName#'">
		</cfif>
		<cfset local.messageTypeCode = getMessageTypeCode(resourceType=arguments.resourceType, recipientType=arguments.recipientType)>
		<cfif len(session.cfcuser.memberData.email)>			
			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=arguments.emailFromName, email=local.mc_siteinfo.networkEmailFrom },
				emailto=[ { name:"", email:session.cfcuser.memberData.email } ],
				emailreplyto=arguments.emailFrom,
				emailsubject="TEST: #local.parseContent.subjectLine#",
				emailtitle=local.mc_siteInfo.orgName,
				emailhtmlcontent=local.parseContent.content,
				emailAttachments=local.mailAttach,
				siteID=local.mc_siteinfo.siteid,
				memberID=local.mc_siteinfo.sysmemberid,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode=local.messageTypeCode),
				sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID,
				isTestMessage=1,
				consentListID=arguments.consentListIDs)>

			<cfset local.retStruct.success = local.responseStruct.success>
		<cfelse>
			<cfset local.retStruct.success = false>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="sendMassEmails" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "">

		<cfsetting requestTimeOut = "600">

		<cfset local.resourceType = arguments.event.getValue('resourceType','')>
		<cfset local.recipientType = arguments.event.getValue('recipientType','')>
		<cfset local.recipientMode = arguments.event.getValue('recipientMode','')>
		<cfscript>
			local.qryOrgOptOutLists = createObject("component","model.admin.emailPreferences.emailPreferences").getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-Out");
			local.showOptOutsPerm = local.qryOrgOptOutLists.recordCount;
			local.showAddOptOutStep = checkShowAddOptOutStep(resourceType=local.resourceType,recipientType=local.recipientType);
			local.consentListIDs = "";
			local.chkIsPrimary = "";
		 	if(local.showAddOptOutStep AND local.showOptOutsPerm and arguments.event.getValue('excludeOptOuts',0)){
				local.consentListIDs = arguments.event.getValue('consentListID','');
				local.chkIsPrimary = arguments.event.getValue('chkIsPrimary','');
				if( local.chkIsPrimary.len() EQ 0 and local.consentListIDs.ListLen()) {
					local.chkIsPrimary = local.consentListIDs.listfirst(',');
				}
				local.consentListIDs = consentListIDs.listFilter(
				function(elem,ind){
					if(elem != chkIsPrimary){
						return true;
					}
					return false;
				});
				local.consentListIDs = local.consentListIDs.listinsertat(1,local.chkIsPrimary);
			}			
		</cfscript>
		
		<cfswitch expression="#local.resourceType#">
			<cfcase value="Contributions">
				<cfset local.strResourceEmailData = CreateObject("component","model.admin.contributions.contributions").getFilteredContributorsForEmailing(event=arguments.event)>
			</cfcase>
			<cfcase value="Events">
				<cfswitch expression="#local.recipientType#">
					<cfcase value="Registrants">
						<cfset local.strResourceEmailData = CreateObject("component","model.admin.events.event").getFilteredRegistrantsForEmailing(event=arguments.event)>
					</cfcase>
					<cfcase value="AttendeeCertificates">
						<cfset local.strResourceEmailData = CreateObject("component","model.admin.events.event").getFilteredAttendeesForEmailing(event=arguments.event)>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="Groups">
				<cfset local.strResourceEmailData = CreateObject("component","model.admin.groups.groups").getFilteredGroupMembersForEmailing(event=arguments.event)>
			</cfcase>
			<cfcase value="Invoices">
				<cfset local.strResourceEmailData = CreateObject("component","model.admin.transactions.invoiceAdmin").getFilteredInvoicesForEmailing(event=arguments.event)>
			</cfcase>
			<cfcase value="Notes,Relationships,Member History">
				<cfset local.strResourceEmailData = CreateObject("component","model.admin.memberHistory.memberHistory").getFilteredMemHistoryForEmailing(event=arguments.event, recipientMode=local.recipientMode)>
			</cfcase>
			<cfcase value="Project">
				<cfswitch expression="#local.recipientType#">
					<cfcase value="Solicitors">
						<cfset local.strResourceEmailData = CreateObject("component","model.admin.projects.project").getFilteredSolicitorsForEmailing(event=arguments.event)>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="SeminarWebLive">
				<cfset local.strResourceEmailData = CreateObject("component","model.admin.seminarWeb.seminarWebSWL").getFilteredRegistrantsForEmailing(event=arguments.event)>
			</cfcase>
			<cfcase value="SeminarWebOnDemand">
				<cfset local.strResourceEmailData = CreateObject("component","model.admin.seminarWeb.seminarWebSWOD").getFilteredRegistrantsForEmailing(event=arguments.event)>
			</cfcase>
			<cfcase value="Tasks">
				<cfset local.strResourceEmailData = CreateObject("component","model.admin.tasks.task").getFilteredTasksForEmailing(event=arguments.event, recipientMode=local.recipientMode)>
			</cfcase>
		</cfswitch>

		<cfif len(local.strResourceEmailData.errorCode)>
			<cfreturn showMessage(errorCode=local.strResourceEmailData.errorCode)>
		</cfif>

		<!--- message prep --->
		<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.event.getTrimValue('templateContent',''), siteid=arguments.event.getValue('mc_siteInfo.siteid'))>	
		<cfset local.emailTitle = "#arguments.event.getValue('mc_siteinfo.orgName')#">
		<cfset local.emailSubject = arguments.event.getTrimValue('emailSubject','')>
		<cfset local.emailFrom = arguments.event.getTrimValue('emailFromName','')>
		<cfset local.emailTemplateCategoryID = arguments.event.getTrimValue('selCategory',0)>
	
		<cfswitch expression="#local.resourceType#">
			<cfcase value="Contributions,Events,Groups,Notes,Relationships,Member History,Project,SeminarWebLive,SeminarWebOnDemand">
				<cfset local.templateAndSubjectContentToParse = "#local.emailFrom##local.emailSubject##local.templateContent#">
				<cfset local.templateContentToParse = local.templateContent>
			</cfcase>
			<cfcase value="Invoices">
				<cfset local.templateAndSubjectContentToParse = "#local.emailFrom##local.emailSubject##local.templateContent#[[invoiceList]]">
				<cfset local.templateContentToParse = "<div>#local.templateContent#</div><br/>[[invoiceList]]">
			</cfcase>
			<cfcase value="Tasks">
				<cfif local.recipientMode eq 'solicitor'>
					<cfset local.templateAndSubjectContentToParse = "#local.emailFrom##local.emailSubject##local.templateContent#[[taskList]]">
					<cfset local.templateContentToParse = "<div>#local.templateContent#</div><br/>[[taskList]]">
				<cfelse>
					<cfset local.templateAndSubjectContentToParse = "#local.emailFrom##local.emailSubject##local.templateContent#">
					<cfset local.templateContentToParse = local.templateContent>
				</cfif>
			</cfcase>
		</cfswitch>

		<cfset local.emailContentWrapper = application.objEmailWrapper.wrapMessage(emailTitle=local.emailTitle, emailContent=local.templateContentToParse, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>

		<cfif application.MCEnvironment neq "production"> 
			<cfset local.deliveryReportEmail = "<EMAIL>">
		<cfelseif len(session.cfcuser.memberData.email)>
			<cfset local.deliveryReportEmail = session.cfcuser.memberData.email>
		<cfelse>
			<cfset local.deliveryReportEmail = ''>
		</cfif>

		<!--- if including merge codes that require coldfusion, we need to mark recipient as not ready so we can insert the field below --->
		<cfset local.markRecipientAsReady = 1>
		<cfset local.strRecipientExtMergeTags = application.objMergeCodes.detectExtendedMergeCodes(siteID=arguments.event.getValue('mc_siteinfo.siteid'), rawContent=local.templateAndSubjectContentToParse, extraMergeTagList=local.strResourceEmailData.extraMergeTagList)>
		<cfif local.strRecipientExtMergeTags.contentHasMergeCodes>
			<cfset local.markRecipientAsReady = 0>
		</cfif>

		<cfif len(local.strResourceEmailData.extendedLinkedMergeCode)>
			<cfset local.strRecipientExtLinkedMergeTags = application.objMergeCodes.detectExtendedLinkedMergeCodes(siteID=arguments.event.getValue('mc_siteinfo.siteid'), rawContent=local.templateAndSubjectContentToParse, extraMergeTagList="", extCode=local.strResourceEmailData.extendedLinkedMergeCode)>
			<cfif local.strRecipientExtLinkedMergeTags.contentHasMergeCodes>
				<cfset local.markRecipientAsReady = 0>
			</cfif>
		<cfelse>
			<cfset local.strRecipientExtLinkedMergeTags = structNew()>
		</cfif>

		<cfif structKeyExists(local.strResourceEmailData,"operationMode")>
			<cfset local.operationMode = local.strResourceEmailData.operationMode>
		<cfelse>
			<cfset local.operationMode = "">
		</cfif>

		<!--- create message and return recipients --->
		<cftry>
			<cfif arguments.event.getValue('saveTemplateOption',0) is 1 AND local.emailTemplateCategoryID is 0>
				<cfset local.EmailTemplateAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailTemplateAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
			</cfif>

			<cfquery name="local.qryRecipients" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @resourceTypeID int, @parentSiteResourceID int, @emailSubject varchar(200),  
						@templateName varchar(300), @rawContent varchar(max), @contentID int, @siteResourceID int, @toolType varchar(200), 
						@controllingSiteResourceID int, @categoryTreeID int, @categoryTreeCode varchar(20), @itemIDList varchar(max), 
						@categoryTreeName varchar(100), @emailTemplateCategoryID int, @emailTemplateID int,
						@emailContentWrapper varchar(max), @deliveryReportEmail varchar(200), @categoryName varchar(200), 
						@emailTagTypeID int, @sendOnDate datetime, @contentVersionID int, @markRecipientAsReady bit, @enteredByMemberID int, 
						@emailFromName varchar(200), @emailFrom varchar(200), @operationMode varchar(30), @recipientMode varchar(10), @consentListIDs varchar(max);

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
					SET @emailSubject = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.emailSubject#">;
					SET @rawContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.templateContent#">;
					SET @emailTagTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('emailTagType',0)#">;
					SET @emailFromName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.emailFrom#">;
					SET @emailFrom = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('emailReplyTo','')#">;
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @emailContentWrapper = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.emailContentWrapper#">;
					SET @toolType = <cfqueryparam value="#local.strResourceEmailData.toolType#" cfsqltype="CF_SQL_VARCHAR">;
					SET @consentListIDs = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.consentListIDs#">;

					<cfif len(local.deliveryReportEmail)>
						SET @deliveryReportEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.deliveryReportEmail#">;
					</cfif>

					<cfif arguments.event.getValue('massEmailScheduling','') eq 'later' and len(arguments.event.getValue('emailDateScheduled',''))>
						SET @sendOnDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.event.getValue('emailDateScheduled'),' - ',' '))#">;
					<cfelse>
						SET @sendOnDate = getDate();
					</cfif>

					SET @markRecipientAsReady = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.markRecipientAsReady#">;
					SET @operationMode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.operationMode#">;
					SET @recipientMode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.recipientMode#">;
					SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
				
					SELECT @parentSiteResourceID = st.siteResourceID 
					FROM dbo.admin_tooltypes tt
					INNER JOIN dbo.admin_siteTools st ON st.toolTypeID = tt.toolTypeID
						AND st.siteID = @siteID
						AND tt.toolType = @toolType
					INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = st.siteResourceID
						AND sr.siteResourceStatusID = 1;

					BEGIN TRAN;
						<!--- mass-email without using an email-template --->
						<cfif arguments.event.getValue('saveTemplateOption',0) is 0>
							EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, @parentSiteResourceID=@parentSiteResourceID, 
								@siteResourceStatusID=1, @isHTML=1, @languageID=1, @isActive=1, @contentTitle=@emailSubject, @contentDesc='', 
								@rawContent=@rawContent, @memberID=@enteredByMemberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.cms_content as c 
							inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
							inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
							where c.contentID = @contentID;
						
						<!--- create a new email template --->
						<cfelseif arguments.event.getValue('saveTemplateOption',0) is 1>
							<cfif local.emailTemplateCategoryID is 0>
								set @categoryTreeCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strResourceEmailData.catTreeCode#">;
								set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EmailTemplateAdminSRID#">;
								set @categoryName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('newCategoryName','')#">;

								select @categoryTreeName = categoryTreeName 
								from dbo.cms_categoryTrees 
								where controllingSiteResourceID = @controllingSiteResourceID 
								and categoryTreeCode = @categoryTreeCode;

								select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@controllingSiteResourceID,@categoryTreeName);

								EXEC dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName=@categoryName, @categoryDesc='', @categoryCode='', 
									@parentCategoryID=NULL, @contributorMemberID=@enteredByMemberID, @categoryID=@emailTemplateCategoryID OUTPUT;
							<cfelse>
								set @emailTemplateCategoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.emailTemplateCategoryID#">;
							</cfif>

							SET @templateName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('templateName','')#">;
							
							EXEC dbo.et_createEmailTemplate @templateTypeCode='ckeditor', @templateName=@templateName, @templateDescription='', 
								@categoryID=@emailTemplateCategoryID, @rawContent=@rawContent, @subjectLine=@emailSubject, 
								@emailFromName=@emailFromName, @emailFrom=@emailFrom, @createdByMemberID=@enteredByMemberID, 
								@siteID=@siteID, @templateID=@emailTemplateID OUTPUT;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.et_emailTemplates as et
							inner join dbo.cms_content as c on c.contentID = et.contentID
							inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
							inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
							where et.templateID = @emailTemplateID;

						<!--- update existing email template --->
						<cfelseif arguments.event.getValue('saveTemplateOption',0) is 2>
							set @emailTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fEmailTemplateID',0)#">;

							update dbo.et_emailTemplates
							set subjectLine = @emailSubject,
								emailFromName = @emailFromName,
								emailFrom = @emailFrom
							where templateID = @emailTemplateID;

							select @contentID = contentID 
							from dbo.et_emailTemplates
							where templateID = @emailTemplateID;

							EXEC dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isHTML=1, @contentTitle=@emailSubject, 
								@contentDesc='', @rawcontent=@rawcontent, @memberID=@enteredByMemberID;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.cms_content as c 
							inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
							inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
							where c.contentID = @contentID;
						</cfif>
					COMMIT TRAN;

						-- insert and return email recipients
						<cfswitch expression="#local.resourceType#">
							<cfcase value="Contributions">
								SET @itemIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strResourceEmailData.itemIDList#">;

								EXEC dbo.cp_massEmailContributors @siteID=@siteID, @contributionIDList=@itemIDList, @messageToParse=@rawcontent, 
									@messageWrapper=@emailContentWrapper, @emailTagTypeID=@emailTagTypeID, @emailFromName=@emailFromName, 
									@emailReplyTo=@emailFrom, @emailSubject=@emailSubject, @contentVersionID=@contentVersionID, 
									@recordedByMemberID=@enteredByMemberID, @deliveryReportEmail=@deliveryReportEmail, @sendOnDate=@sendOnDate, 
									@markRecipientAsReady=@markRecipientAsReady, @operationMode=@operationMode, @consentListIDs=@consentListIDs;
							</cfcase>
							<cfcase value="Events">
								<cfswitch expression="#local.recipientType#">
									<cfcase value="Registrants">
										SET @itemIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strResourceEmailData.itemIDList#">;

										EXEC dbo.ev_emailRegistrants @siteID=@siteID, @registrantIDList=@itemIDList, @messageToParse=@rawcontent, 
											@messageWrapper=@emailContentWrapper, @emailTagTypeID=@emailTagTypeID, @emailFromName=@emailFromName, 
											@emailReplyTo=@emailFrom, @emailSubject=@emailSubject, @contentVersionID=@contentVersionID, 
											@recordedByMemberID=@enteredByMemberID, @deliveryReportEmail=@deliveryReportEmail, @sendOnDate=@sendOnDate, 
											@markRecipientAsReady=@markRecipientAsReady, @operationMode=@operationMode, @consentListIDs=@consentListIDs;
									</cfcase>
									<cfcase value="AttendeeCertificates">
										DECLARE @eventID int;
										SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#listFirst(local.strResourceEmailData.itemIDList,'|')#">;
										SET @itemIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#listLast(local.strResourceEmailData.itemIDList,'|')#">;

										EXEC dbo.ev_emailAttendees @siteID=@siteID, @eventID=@eventID, @registrantIDList=@itemIDList, @messageToParse=@rawcontent, 
											@messageWrapper=@emailContentWrapper, @emailTagTypeID=@emailTagTypeID, @emailFromName=@emailFromName, 
											@emailReplyTo=@emailFrom, @emailSubject=@emailSubject, @contentVersionID=@contentVersionID, 
											@recordedByMemberID=@enteredByMemberID, @deliveryReportEmail=@deliveryReportEmail, @sendOnDate=@sendOnDate;
									</cfcase>
								</cfswitch>
							</cfcase>
							<cfcase value="Groups">
								DECLARE @groupID int;
								SET @groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#listFirst(local.strResourceEmailData.itemIDList,'|')#">;
								SET @itemIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#listLast(local.strResourceEmailData.itemIDList,'|')#">;

								EXEC dbo.ams_emailGroupMembers @siteID=@siteID, @groupID=@groupID, @memberIDList=@itemIDList, @messageToParse=@rawcontent,
									@messageWrapper=@emailContentWrapper, @emailTagTypeID=@emailTagTypeID, @emailFromName=@emailFromName, @emailReplyTo=@emailFrom, 
									@emailSubject=@emailSubject, @contentVersionID=@contentVersionID, @recordedByMemberID=@enteredByMemberID, 
									@deliveryReportEmail=@deliveryReportEmail, @sendOnDate=@sendOnDate, @markRecipientAsReady=@markRecipientAsReady, @consentListIDs=@consentListIDs;
							</cfcase>
							<cfcase value="Invoices">
								DECLARE @memberIDList varchar(max);
								SET @memberIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#listFirst(local.strResourceEmailData.itemIDList,'|')#">;
								SET @itemIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#listLast(local.strResourceEmailData.itemIDList,'|')#">;

								EXEC dbo.ams_emailInvoices @siteID=@siteID, @memberIDList=@memberIDList, @invoiceIDList=@itemIDList, @messageToParse=@rawContent, 
									@messageWrapper=@emailContentWrapper, @emailTagTypeID=@emailTagTypeID, @emailFromName=@emailFromName, @emailReplyTo=@emailFrom, 
									@emailSubject=@emailSubject, @contentVersionID=@contentVersionID, @recordedByMemberID=@enteredByMemberID, 
									@deliveryReportEmail=@deliveryReportEmail, @overrideEmailList=NULL, @sendOnDate=@sendOnDate, @markRecipientAsReady=@markRecipientAsReady, @consentListIDs=@consentListIDs;
							</cfcase>
							<cfcase value="Notes,Relationships,Member History">
								SET @itemIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strResourceEmailData.itemIDList#">;

								EXEC dbo.ams_emailMemberHistory @siteID=@siteID, @historyIDList=@itemIDList, @recipientMode=@recipientMode,
									@messageToParse=@rawContent, @messageWrapper=@emailContentWrapper, @emailTagTypeID=@emailTagTypeID, 
									@emailFromName=@emailFromName, @emailReplyTo=@emailFrom, @emailSubject=@emailSubject, @contentVersionID=@contentVersionID, 
									@recordedByMemberID=@enteredByMemberID, @deliveryReportEmail=@deliveryReportEmail, @sendOnDate=@sendOnDate, 
									@markRecipientAsReady=@markRecipientAsReady, @consentListIDs=@consentListIDs;
							</cfcase>
							<cfcase value="Project">
								DECLARE @projectID int;
								SET @projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#listFirst(local.strResourceEmailData.itemIDList,'|')#">;
								SET @itemIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#listLast(local.strResourceEmailData.itemIDList,'|')#">;

								<cfswitch expression="#local.recipientType#">
									<cfcase value="Solicitors">
										EXEC dbo.tasks_emailSolicitors @siteID=@siteID, @projectID=@projectID, @memberIDList=@itemIDList, @messageToParse=@rawcontent,
											@messageWrapper=@emailContentWrapper, @emailTagTypeID=@emailTagTypeID, @emailFromName=@emailFromName, @emailReplyTo=@emailFrom, 
											@emailSubject=@emailSubject, @contentVersionID=@contentVersionID,@recordedByMemberID=@enteredByMemberID, 
											@deliveryReportEmail=@deliveryReportEmail, @sendOnDate=@sendOnDate, @markRecipientAsReady=@markRecipientAsReady, @consentListIDs=@consentListIDs;
									</cfcase>
								</cfswitch>
							</cfcase>
							<cfcase value="SeminarWebLive,SeminarWebOnDemand">
								DECLARE @swType varchar(5);
								SET @itemIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strResourceEmailData.itemIDList#">;
								<cfif local.resourceType EQ "SeminarWebLive">
									SET @swType = 'SWL';
								<cfelseif local.resourceType EQ "SeminarWebOnDemand">
									SET @swType = 'SWOD';
								</cfif>

								EXEC dbo.sw_emailEnrollments @siteID=@siteID, @swType=@swType, @enrollmentIDList=@itemIDList, @messageToParse=@rawcontent,
									@messageWrapper=@emailContentWrapper, @emailTagTypeID=@emailTagTypeID, @emailFromName=@emailFromName, @emailReplyTo=@emailFrom, 
									@emailSubject=@emailSubject, @contentVersionID=@contentVersionID, @recordedByMemberID=@enteredByMemberID, 
									@deliveryReportEmail=@deliveryReportEmail, @sendOnDate=@sendOnDate, @markRecipientAsReady=@markRecipientAsReady, @consentListIDs=@consentListIDs;
							</cfcase>
							<cfcase value="Tasks">
								SET @itemIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strResourceEmailData.itemIDList#">;

								EXEC dbo.ams_emailTasks @siteID=@siteID, @taskIDList=@itemIDList, @recipientMode=@recipientMode, @messageToParse=@rawContent, 
									@messageWrapper=@emailContentWrapper, @emailTagTypeID=@emailTagTypeID, @emailFromName=@emailFromName, @emailReplyTo=@emailFrom, 
									@emailSubject=@emailSubject, @contentVersionID=@contentVersionID, @recordedByMemberID=@enteredByMemberID, 
									@deliveryReportEmail=@deliveryReportEmail, @sendOnDate=@sendOnDate, @markRecipientAsReady=@markRecipientAsReady, 
									@operationMode=@operationMode, @consentListIDs=@consentListIDs;
							</cfcase>
						</cfswitch>

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("No recipients for message", cfcatch.detail)>
				<cfset local.errorCode = 'noemailrecipient'>
			<cfelse>
				<cfset local.errorCode = ''>
			</cfif>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfreturn showMessage(errorCode=local.errorCode)>
		</cfcatch>
		</cftry>
		
		<!--- if including merge codes that require coldfusion, we insert the field here --->
		<cfif not local.markRecipientAsReady>
			<cftry>
				<cfset replaceDetectedExtendedMergeCodes(event=arguments.event, resourceType=local.resourceType, recipientType=local.recipientType, strResourceEmailData=local.strResourceEmailData, 
							qryRecipients=local.qryRecipients, strRecipientExtMergeTags=local.strRecipientExtMergeTags, strRecipientExtLinkedMergeTags=local.strRecipientExtLinkedMergeTags)>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfreturn showMessage(errorCode='')>
			</cfcatch>
			</cftry>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<div class="alert alert-success py-2">E-mails Scheduled Successfully.</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="replaceDetectedExtendedMergeCodes" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="recipientType" type="string" required="true">
		<cfargument name="strResourceEmailData" type="struct" required="true">
		<cfargument name="qryRecipients" type="query" required="true">
		<cfargument name="strRecipientExtMergeTags" type="struct" required="true">
		<cfargument name="strRecipientExtLinkedMergeTags" type="struct" required="true">

		<cfset var local = structNew()>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = arguments.event.getValue('mc_siteinfo.mainHostName')>
			<cfset local.thisScheme = arguments.event.getValue('mc_siteinfo.scheme')>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<cfswitch expression="#arguments.resourceType#">
			<cfcase value="Contributions,Groups,Project,SeminarWebLive,SeminarWebOnDemand">
				<cfloop query="arguments.qryRecipients">
					<cfset local.strMergeTagArgs = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgcode'), 
													recipientID=arguments.qryRecipients.recipientID, messageID=arguments.qryRecipients.messageID, recipientMemberID=arguments.qryRecipients.memberID, 
													recipientEmail=arguments.qryRecipients.recipientEmail, hostname=local.thisHostname, useRemoteLogin=arguments.event.getValue('mc_siteinfo.useRemoteLogin') }>
					
					<cfif arguments.strRecipientExtMergeTags.contentHasMergeCodes>
						<cfset local.strMergeTagArgs.memberID = arguments.qryRecipients.memberID>
						<cfset local.strMergeTagArgs.membernumber = arguments.qryRecipients.membernumber>
						<cfset local.strMergeTagArgs.strRecipientExtMergeTags = duplicate(arguments.strRecipientExtMergeTags)>
						<cfset application.objMergeCodes.replaceExtendedMergeCodes(argumentCollection=local.strMergeTagArgs)>
					</cfif>

					<cfset setRecipientAsReady(siteID=arguments.event.getValue('mc_siteinfo.siteid'),messageID=arguments.qryRecipients.messageID,recipientID=arguments.qryRecipients.recipientID)>
				</cfloop>
			</cfcase>
			<cfcase value="Events">
				<!--- create struct of event data so we do it only once per event --->
				<cfset local.objEvent = createObject("component","model.events.events")>
				<cfset local.strEventData = {}>

				<cfloop query="arguments.qryRecipients">
					<cfquery name="local.qryThisRecipient" dbtype="query">
						select eventID, membernumber, applicationInstanceID
						from arguments.strResourceEmailData.qryRegistrants
						where registrantID = #val(arguments.qryRecipients.registrantID)#
					</cfquery>

					<cfset local.strMergeTagArgs = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgcode'), 
													recipientID=arguments.qryRecipients.recipientID, messageID=arguments.qryRecipients.messageID, recipientMemberID=arguments.qryRecipients.memberID, 
													memberID=arguments.qryRecipients.memberID, membernumber=local.qryThisRecipient.membernumber, recipientEmail=arguments.qryRecipients.recipientEmail, 
													hostname=local.thisHostname, useRemoteLogin=arguments.event.getValue('mc_siteinfo.useRemoteLogin'), strRecipientExtMergeTags=arguments.strRecipientExtMergeTags }>					
					<cfset application.objMergeCodes.replaceExtendedMergeCodes(argumentCollection=local.strMergeTagArgs)>

					<!--- get event data once per event --->
					<cfif arguments.strRecipientExtMergeTags.strBuildMergeCode['evEventTime'] is 1 or arguments.strRecipientExtMergeTags.strBuildMergeCode['evEventURL'] is 1>
						<cfif NOT structKeyExists(local.strEventData,local.qryThisRecipient.eventID)>
							<cfset local.strEventInfo = { eventTime="", eventURL="" }>
							<cfset structInsert(local.strEventData,local.qryThisRecipient.eventID,local.strEventInfo)>

							<cfif arguments.strRecipientExtMergeTags.strBuildMergeCode['evEventTime'] is 1>
								<cfset local.strEvent = local.objEvent.getEvent(eventID=local.qryThisRecipient.eventID, siteID=arguments.event.getValue('mc_siteinfo.siteid'), languageID=1)>
								<cfset local.showTimeZone = true>	
								<cfif arguments.event.getValue('mc_siteinfo.defaultTimeZoneID') eq local.strEvent.qryEventTimes_selected.timezoneID and local.strEvent.qryEventMeta.alwaysShowEventTimezone eq 0>
									<cfset local.showTimeZone = false>
								</cfif>
								<cfset local.strEventData[local.qryThisRecipient.eventID].eventTime = local.objEvent.generateEventDateString(mode='eventEmailRegistrants', 
									startTime=local.strEvent.qryEventTimes_selected.startTime, endTime=local.strEvent.qryEventTimes_selected.endTime, 
									isAllDayEvent=local.strEvent.qryEventMeta.isAllDayEvent, showTimeZone=local.showTimeZone, 
									timeZoneAbbr=local.strEvent.qryEventTimes_selected.timeZoneAbbr)>
							</cfif>

							<cfif arguments.strRecipientExtMergeTags.strBuildMergeCode['evEventURL'] is 1>
								<cfset local.baseProgramLink = application.objApplications.getAppBaseLink(applicationInstanceID=local.qryThisRecipient.applicationInstanceID, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
								<cfset local.strEventData[local.qryThisRecipient.eventID].eventURL = "#local.thisScheme#://#local.thisHostname#/?#local.baseProgramLink#&evAction=showDetail&eid=#local.qryThisRecipient.eventID#">
							</cfif>
						</cfif>

						<cfset local.tmpArgsStr = { messageID=arguments.qryRecipients.messageID, memberID=arguments.qryRecipients.memberID, recipientID=arguments.qryRecipients.recipientID, fieldID=0, fieldValue='', fieldTextToReplace='' }>
					</cfif>
					<cfif arguments.strRecipientExtMergeTags.strBuildMergeCode['evEventTime'] is 1>
						<cfset local.tmpArgsStr['fieldID'] = arguments.strRecipientExtMergeTags.strMergeCodeFieldID['evEventTime']>
						<cfset local.tmpArgsStr['fieldValue'] = local.strEventData[local.qryThisRecipient.eventID].eventTime>
						<cfset local.tmpArgsStr['fieldTextToReplace'] = "">
						<cfset local.messageFieldID = application.objMergeCodes.insertMessageMetadataField(argumentcollection=local.tmpArgsStr)>
					</cfif>
					<cfif arguments.strRecipientExtMergeTags.strBuildMergeCode['evEventURL'] is 1>
						<cfset local.tmpArgsStr['fieldID'] = arguments.strRecipientExtMergeTags.strMergeCodeFieldID['evEventURL']>
						<cfset local.tmpArgsStr['fieldValue'] = local.strEventData[local.qryThisRecipient.eventID].eventURL>
						<cfset local.tmpArgsStr['fieldTextToReplace'] = "">
						<cfset local.messageFieldID = application.objMergeCodes.insertMessageMetadataField(argumentcollection=local.tmpArgsStr)>
					</cfif>

					<!--- certificate recipients are marked as ready in processEventCertificates schedule task that handle cert attachments --->
					<cfif arguments.recipientType neq 'AttendeeCertificates'>
						<cfset setRecipientAsReady(siteID=arguments.event.getValue('mc_siteinfo.siteid'),messageID=arguments.qryRecipients.messageID,recipientID=arguments.qryRecipients.recipientID)>
					</cfif>
				</cfloop>
			</cfcase>
			<cfcase value="Invoices">
				<cfset local.objInvoice = CreateObject("component","model.admin.transactions.invoiceAdmin")>

				<cfloop query="arguments.qryRecipients">
					<cfquery name="local.qryMemberInvoiceDetails" dbtype="query">
						select *
						from arguments.strResourceEmailData.qryInvoiceDetails
						where memberID = #val(arguments.qryRecipients.memberID)#
					</cfquery>

					<cfset local.strMergeTagArgs = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgcode'), 
													recipientID=arguments.qryRecipients.recipientID, messageID=arguments.qryRecipients.messageID, recipientMemberID=arguments.qryRecipients.memberID, 
													memberID=arguments.qryRecipients.memberID, membernumber=arguments.qryRecipients.membernumber, recipientEmail=arguments.qryRecipients.recipientEmail, 
													hostname=local.thisHostname, useRemoteLogin=arguments.event.getValue('mc_siteinfo.useRemoteLogin'), strRecipientExtMergeTags=arguments.strRecipientExtMergeTags }>					
					<cfset application.objMergeCodes.replaceExtendedMergeCodes(argumentCollection=local.strMergeTagArgs)>

					<!--- invoicelist --->
					<cfset local.invoiceListForMember = local.objInvoice.getMemberInvoiceListDetails(qryMemberInvoiceDetails=local.qryMemberInvoiceDetails)>
					<cfset local.tmpArgsStr = { messageID=arguments.qryRecipients.messageID, memberID=arguments.qryRecipients.memberID, recipientID=arguments.qryRecipients.recipientID, 
						fieldID=arguments.strRecipientExtMergeTags.strMergeCodeFieldID['invoiceList'], fieldValue=trim(local.invoiceListForMember), fieldTextToReplace='' }>
					<cfset local.messageFieldID = application.objMergeCodes.insertMessageMetadataField(argumentcollection=local.tmpArgsStr)>

					<cfset setRecipientAsReady(siteID=arguments.event.getValue('mc_siteinfo.siteid'),messageID=arguments.qryRecipients.messageID,recipientID=arguments.qryRecipients.recipientID)>
				</cfloop>
			</cfcase>
			<cfcase value="Notes,Relationships,Member History">
				<cfloop query="arguments.qryRecipients">
					<cfset local.strMergeTagArgs = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgcode'), 
													recipientID=arguments.qryRecipients.recipientID, messageID=arguments.qryRecipients.messageID, recipientMemberID=arguments.qryRecipients.recipientMemberID, 
													recipientEmail=arguments.qryRecipients.recipientEmail, hostname=local.thisHostname, useRemoteLogin=arguments.event.getValue('mc_siteinfo.useRemoteLogin') }>
					
					<cfif arguments.strRecipientExtMergeTags.contentHasMergeCodes>
						<cfset local.strMergeTagArgs.memberID = arguments.qryRecipients[arguments.strResourceEmailData.memberIDLabel][arguments.qryRecipients.currentRow]>
						<cfset local.strMergeTagArgs.membernumber = arguments.qryRecipients[arguments.strResourceEmailData.memberNumberLabel][arguments.qryRecipients.currentRow]>
						<cfset local.strMergeTagArgs.strRecipientExtMergeTags = duplicate(arguments.strRecipientExtMergeTags)>
						<cfset application.objMergeCodes.replaceExtendedMergeCodes(argumentCollection=local.strMergeTagArgs)>
					</cfif>
					<cfif len(arguments.strResourceEmailData.extendedLinkedMergeCode) and arguments.strRecipientExtLinkedMergeTags.contentHasMergeCodes>
						<cfset local.strMergeTagArgs.memberID = val(arguments.qryRecipients[arguments.strResourceEmailData.linkedMemberIDLabel][arguments.qryRecipients.currentRow])>
						<cfset local.strMergeTagArgs.membernumber = arguments.qryRecipients[arguments.strResourceEmailData.linkedMemberNumberLabel][arguments.qryRecipients.currentRow]>
						<cfset local.strMergeTagArgs.strRecipientExtMergeTags = duplicate(arguments.strRecipientExtLinkedMergeTags)>
						<cfset local.strMergeTagArgs.extCode = arguments.strResourceEmailData.extendedLinkedMergeCode>
						<cfset application.objMergeCodes.replaceExtendedLinkedMergeCodes(argumentCollection=local.strMergeTagArgs)>
					</cfif>

					<cfset setRecipientAsReady(siteID=arguments.event.getValue('mc_siteinfo.siteid'),messageID=arguments.qryRecipients.messageID,recipientID=arguments.qryRecipients.recipientID)>
				</cfloop>
			</cfcase>
			<cfcase value="Tasks">
				<cfset local.objTasks = CreateObject("component","model.admin.tasks.task")>

				<cfloop query="arguments.qryRecipients">
					<cfset local.strMergeTagArgs = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgcode'), 
													recipientID=arguments.qryRecipients.recipientID, messageID=arguments.qryRecipients.messageID, recipientMemberID=arguments.qryRecipients.recipientMemberID, 
													recipientEmail=arguments.qryRecipients.recipientEmail, hostname=local.thisHostname, useRemoteLogin=arguments.event.getValue('mc_siteinfo.useRemoteLogin') }>
					
					<cfif arguments.strRecipientExtMergeTags.contentHasMergeCodes>
						<cfset local.strMergeTagArgs.memberID = arguments.qryRecipients.taskMemberID>
						<cfset local.strMergeTagArgs.memberNumber = arguments.qryRecipients.taskMemberNumber>
						<cfset local.strMergeTagArgs.strRecipientExtMergeTags = duplicate(arguments.strRecipientExtMergeTags)>
						<cfset application.objMergeCodes.replaceExtendedMergeCodes(argumentCollection=local.strMergeTagArgs)>
					</cfif>
					
					<!--- tasklist --->
					<cfif arguments.event.getValue('recipientMode','') eq 'solicitor'>
						<cfset local.qryTaskRecipientReferences = local.objTasks.getTaskRecipientReferences(recipientID=arguments.qryRecipients.recipientID)>

						<cfquery name="local.qryMemberTasks" dbtype="query">
							select applicationInstanceID, projectID, taskID, memberFirstname, memberLastname, membercompany, statusName, 
								projectTitle, dateDue, solicitorMemberNumber, solicitorMemberFirstname, solicitorMemberLastName, solicitormemberCompany
							from arguments.strResourceEmailData.qryTaskDetails
							where memberID in (<cfqueryparam value="0#valueList(local.qryTaskRecipientReferences.memberID)#" list="yes" cfsqltype="CF_SQL_INTEGER">);
						</cfquery>

						<cfset local.taskListForMember = local.objTasks.getTaskListDetails(qryTasks=local.qryMemberTasks)>
						
						<cfset local.tmpArgsStr = { messageID=arguments.qryRecipients.messageID, memberID=arguments.qryRecipients.recipientMemberID, recipientID=arguments.qryRecipients.recipientID, 
													fieldID=arguments.strRecipientExtMergeTags.strMergeCodeFieldID['taskList'], fieldValue=trim(local.taskListForMember), fieldTextToReplace='' }>
						<cfset local.messageFieldID = application.objMergeCodes.insertMessageMetadataField(argumentcollection=local.tmpArgsStr)>
					<cfelseif arguments.event.getValue('recipientMode','') eq 'prospect' and arguments.strRecipientExtMergeTags.strBuildMergeCode['taskSolicitors'] is 1>
						<cfset local.qryTaskRecipientReferences = local.objTasks.getTaskRecipientReferences(recipientID=arguments.qryRecipients.recipientID)>
						<cfset local.taskSolicitorList = local.objTasks.getTaskSolicitorsList(taskID=val(local.qryTaskRecipientReferences.referenceID), projectID=local.qryTaskRecipientReferences.projectID)>
						<cfset local.tmpArgsStr = { messageID=arguments.qryRecipients.messageID, memberID=arguments.qryRecipients.recipientMemberID, recipientID=arguments.qryRecipients.recipientID, 
													fieldID=arguments.strRecipientExtMergeTags.strMergeCodeFieldID['taskSolicitors'], fieldValue=trim(local.taskSolicitorList), fieldTextToReplace='' }>
						<cfset local.messageFieldID = application.objMergeCodes.insertMessageMetadataField(argumentcollection=local.tmpArgsStr)>
					</cfif>

					<cfset setRecipientAsReady(siteID=arguments.event.getValue('mc_siteinfo.siteid'),messageID=arguments.qryRecipients.messageID,recipientID=arguments.qryRecipients.recipientID)>
				</cfloop>
			</cfcase>
		</cfswitch>
	</cffunction>

	<cffunction name="setRecipientAsReady" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="messageID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">
		<cfset var local = {}>

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryChangeStatus">
			exec dbo.email_setMessageRecipientHistoryStatus
				@siteID= <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">,
				@messageID= <cfqueryparam value="#arguments.messageID#" cfsqltype="CF_SQL_INTEGER">,
				@recipientID= <cfqueryparam value="#arguments.recipientID#" cfsqltype="CF_SQL_INTEGER">,
				@statusCode= <cfqueryparam value="Q" cfsqltype="CF_SQL_VARCHAR">,
				@updateDate= <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
		</cfquery>

	</cffunction>

	<cffunction name="getMemberInfoStruct" access="private" output="false" returntype="struct">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="content" type="string" required="true">
		<cfargument name="subjectLine" type="string" required="true">
		<cfargument name="emailFrom" type="string" required="true">
		<cfargument name="codeprefix" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.mc_siteInfo.mainhostname>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=arguments.memberID)>
		<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.mc_siteInfo.orgID, memberID=val(local.memberInfo.memberID), 
			content="#arguments.content##arguments.subjectLine##arguments.emailFrom#", codeprefix="#arguments.codeprefix#.")>

		<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=local.memberInfo.FirstName, middleName=local.memberInfo.MiddleName,
										lastName=local.memberInfo.LastName, company=local.memberInfo.Company, suffix=local.memberInfo.Suffix, prefix=local.memberInfo.Prefix, 
										professionalSuffix=local.memberInfo.professionalSuffix, membernumber=local.memberInfo.membernumber, orgcode=local.memberInfo.orgcode,
										siteID=arguments.siteID, hostname=local.thisHostname, useRemoteLogin=local.mc_siteInfo.useRemoteLogin }>

		<cfset local.excludeKeyList = "orgcode|siteID|hostname|useRemoteLogin">
		<cfloop list="#structKeyList(local.tempMemberData)#" index="local.thisKey">
			<cfif not listFindNoCase(local.excludeKeyList,local.thisKey,"|") and structKeyExists(local.memberInfo,local.thisKey)>
				<cfset local.tempMemberData["#arguments.codeprefix#.#local.thisKey#"] = local.memberInfo[local.thisKey][1]>
			</cfif>
		</cfloop>

		<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
			<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
				<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
				<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
			</cfif>
		</cfloop>

		<cfreturn local.tempMemberData>
	</cffunction>

	<cffunction name="showMessage" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.errorCode#">
			<cfcase value="norecipient">
				<cfset local.message = '<h4>No Recipients Found</h4><div>Please check the filtered recipients.</div>'>
			</cfcase>
			<cfcase value="noemailrecipient">
				<cfset local.message = '<h4>No Recipients with Defined Emails</h4><div>No filtered recipients had email addresses defined, so we were not able to send this message.</div>'>
			</cfcase>
			<cfdefaultcase>
				<cfset local.message = "<b>An error occurred. Try again or contact support for assistance.</b">
			</cfdefaultcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<div class="alert alert-danger py-2">#local.message#</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="checkShowAddOptOutStep" access="public" output="false" returntype="boolean">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="recipientType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.showAddOptOutStep = 0>
		<cfset local.messageTypeCode = getMessageTypeCode(resourceType=arguments.resourceType, recipientType=arguments.recipientType)>
	
		<cfquery name="local.qryGetMailStreamCode" datasource="#application.dsn.platformMail.dsn#">
			SELECT mt.messageTypeCode, ms.mailStreamCode
			FROM platformmail.dbo.email_messageTypes mt 
			INNER JOIN  platformmail.dbo.email_mailstreams ms 
				ON mt.mailStreamID = ms.mailStreamID
			WHERE messageTypeCode =  <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.messageTypeCode#">
		</cfquery>

		<cfif local.qryGetMailStreamCode.recordCount AND local.qryGetMailStreamCode.mailStreamCode EQ 'MKT'>
			<cfset local.showAddOptOutStep = 1>
		</cfif>

		<cfreturn local.showAddOptOutStep>
	</cffunction>

	<cffunction name="getMessageTypeCode" access="private" output="false" returntype="string">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="recipientType" type="string" required="true">

		<cfset var messageTypeCode = "">

		<cfswitch expression="#arguments.resourceType#">
			<cfcase value="Contributions">
				<cfset messageTypeCode = 'EMAILCP'>
			</cfcase>
			<cfcase value="Events">
				<cfswitch expression="#arguments.recipientType#">
					<cfcase value="Registrants">
						<cfset messageTypeCode = 'EMAILREG'>
					</cfcase>
					<cfcase value="AttendeeCertificates">
						<cfset messageTypeCode = 'EMAILCERTS'>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="Groups">
				<cfset messageTypeCode = 'EMAILGRP'>
			</cfcase>
			<cfcase value="Invoices">
				<cfset messageTypeCode = 'EMAILINV'>
			</cfcase>
			<cfcase value="Notes,Relationships,Member History">
				<cfset messageTypeCode = 'EMAILHISTORY'>
			</cfcase>
			<cfcase value="Project">
				<cfswitch expression="#arguments.recipientType#">
					<cfcase value="Solicitors">
						<cfset messageTypeCode = 'EMAILPROJSOL'>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="SeminarWebLive,SeminarWebOnDemand">
				<cfset messageTypeCode = 'EMAILSWREG'>
			</cfcase>
			<cfcase value="Tasks">
				<cfset messageTypeCode = 'EMAILTASK'>
			</cfcase>
		</cfswitch>

		<cfreturn messageTypeCode>
	</cffunction>

</cfcomponent>