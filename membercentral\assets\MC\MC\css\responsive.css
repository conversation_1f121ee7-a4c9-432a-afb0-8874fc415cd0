@charset "utf-8";

@media print {
	* {
		-webkit-print-color-adjust: exact;
		-moz-print-color-adjust: exact;
		-o-print-color-adjust: exact;
		visibility: visible;
	}

	video,
	audio,
	object,
	embed {
		display: none;
	}

	img {
		max-width: 500px;
	}

	body {
		margin: 1cm auto;
	}

	.printHeader {
		text-align: center;
		padding: 15px;
		display: block;
		width: 100%;
	}

	.printHeader a {
		display: inline-block;
		width: 100%;
		text-align: center;
		margin-top: 10px;
	}

	.printHeader p {
		margin-top: 15px;
	}

	.headerSpace {
		display: none;
	}

	.header,
	.navbar,
	.footer,
	.bgBorder {
		display: none;
	}

	.copyrightText {
		display: block;
		text-align: center;
	}

	.copyrightText ul li {
		color: #000;
	}

	.printFooter {
		width: 100%;
		text-align: center;
		display: block;
		font-size: 13px;
		color: #fff;
		font-weight: 400;
	}

	.container.containerCustom {
		width: 100% !important;
	}

	.span4 {
		width: 33%;
	}

	.captionFrame ul li,
	.captionFrame ul li,
	.testimonial-block * {
		color: white;
	}

	.captionFrame {
		margin-left: 0;
		max-width: 100%;
		padding: 0 15px;
	}

	.owl-carousel .owl-stage {
		display: block !important;
		transform: none !important;
		width: 100% !important;
		padding: 15px !important;
	}

	.slider .owl-carousel .item:before,
	.slider .owl-carousel .item img {
		display: none;
	}

	.captionFrame ul li {
		word-break: break-all;
	}

	.captionBtnBox {
		position: static;
		max-width: 100%;
	}

	.captionBtnFrame {
		position: static;
		background-color: white;
		max-width: 100%;
	}

	.captionBtnBox ul li a {
		background: transparent;
		border: 1px solid #BA0C2F;
		display: block;
		height: 130px;
	}

	.captionBtnBox ul li a .iconBox img.default,
	.arrow img {
		filter: invert(1);
	}

	.captionBtnBox ul li a .textBox h2 {
		color: #2d2d2d;
	}

	.inner-page-content {
		position: relative;
	}

	.inner-page-content .sidebar {
		width: 280px;
		border-right: 1px solid #717171;
	}

	.inner-page-content .inner-content-area {
		padding: 20px;
	}

	.sponsors-box,
	.sponsors-boxthree {
		border: 1px solid #717171;
	}

	.event-slider .owl-stage {
		width: 100% !important;
	}

	.event-slider .owl-stage .owl-item {
		width: 100% !important;
		margin: 0 0 35px 0 !important;
	}

	.eventBoxFrame {
		margin: 0;
	}

	.owl-carousel .owl-item.cloned {
		display: none !important;
	}

	.owl-carousel .owl-stage {
		display: block;
		width: 100%;
		transform: none;
		height: auto;
		max-width: 100%;
	}

	.owl-carousel .owl-stage .owl-item {
		width: 100% !important;
	}

	.for-mobile .events {
		display: none;
	}

	.anouncebanner,
	.friendsLogoBox.for-mobile {
		display: none;
	}

	.captionBtnBox ul li a .iconBox img {
		filter: none;
		margin-top: 10px;
	}

	.upcoming-event-sec .flex-row>div {
		width: 33%;
	}

	.testimonials-sec .below-blue-img {
		display: none;
	}

	.reduces-costs-sec .boxcontent.flex-sm-reverse .img-holder.img-with-shap {
		margin-right: 0;
	}

	.reduces-costs-sec .boxcontent:not(.flex-sm-reverse) .img-holder {
		margin-left: 0;
	}

	.inner-banner-sec.slider .item:before {
		display: none;
	}

	.slider .item>img {
		display: none;
	}

	.bg-blue {
		background: #ffffff !important;
	}
}



@media screen and (min-width:1200px) {
	.container.containerCustom {
		max-width: 1010px;
		width: 100%;
	}

	.container.containerCustom.w-1200 {
		max-width: 1250px;
	}

	.lg-px-40 {
		padding-left: 40px !important;
		padding-right: 40px !important;
	}

	.lg-pr-25 {
		padding-right: 25px !important;
	}

	.reduces-costs-sec .row.d-flex-wrap>.col-6:nth-child(2) p {
		margin-right: -8px;
	}

	.reduces-costs-sec .boxcontent.flex-sm-reverse .img-holder.img-with-shap {
		padding-left: 8px;
	}
	.sec-topnotch .span6 p {
		padding-right: 10%;
	}
}

@media screen and (min-width:1400px) {
	.testimonials-sec .right-wrap .img-holder {
		position: relative;
	}

	.testimonials-sec .right-wrap .img-holder img {
		width: 100%;
	}


	.slider .img-holder {
	}

	.slider .img-holder img {
	}

}

@media screen and (min-width:1800px) {
	.navbar .container.containerCustom {
		max-width: 1240px;
		width: 100%;
	}
}

@media screen and (min-width: 980px) {

	.nav-collapse.collapse {
		margin: 0 -15px 0;
	}

	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu {
		position: absolute;
		width: 100%;
		max-width: 1250px;
		/* visibility: visible; */
		background: none !important; box-shadow: none !important;
		opacity: 0.96;
		transform: translateX(-50%);
		left: 50%;
		top: 71px;
		min-width: 220px;
		border: none;
		display: block;
		-moz-transition: top 0.5s ease 0s, visibility 0s ease 0s;
		-ms-transition: top 0.5s ease 0s, visibility 0s ease 0s;
		-o-transition: top 0.5s ease 0s, visibility 0s ease 0s;
		-webkit-transition: top 0.5s ease 0s, visibility 0s ease 0s;
		transition: top 0.5s ease 0s, visibility 0s ease 0s, z-index 0s ease 0.1s;
		z-index: 9;
		padding: 0;
		overflow: hidden;
		padding: 0;
	}

	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection {
		display: table;
		width: 100%;
		margin: 0;
		max-width: 50%;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.textUl ul {
		padding-left: 0;
		display: block;
		vertical-align: middle;
		position: relative;
		list-style: none;
		margin-left: 0;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta ul li:first-child {
		float: left;
		margin-right: 45px;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta ul li p {
		margin-bottom: 15px;
		font-size: 16px;
	}

	.header .navbar .nav>li.dropdown.megamenu>.dropdown-menu {
		z-index: 9;
		opacity: 1 !important;
		margin: 0;
		list-style: none;
		box-shadow: 4px 4px 65px 0px #4862842E;
		border-radius: 0px;
		overflow: hidden;
		padding-left: 415px;
		/* display: none; */
	}
	.header .navbar .nav>li.dropdown.megamenu:hover>.dropdown-menu {
		/* display: block !important; */
		/* visibility: visible !important; */
	}

	.navbar .nav li.dropdown>.dropdown-toggle .caret {
		border-top-color: #eeeeee;
		border-bottom-color: #eeeeee;
	}

	.navbar .nav li.dropdown:hover>.dropdown-toggle .caret {
		border-top-color: #006eb3;
		border-bottom-color: #006eb3;
	}

	.header .nav li .dropdown-menu>li.dropdown-submenu a:hover .caret {
		border-top: 4px solid #000;
	}

	.navbar .nav li.dropdown .dropdown-menu .dropdown-submenu .dropdown-menu:before {
		display: none;
	}

	.header .dropdown-submenu li {
		padding: 0 20px;
	}

	.dropdown-submenu .dropdown-menu {
		padding: 20px 0;
	}

	.header .dropdown-submenu .dropdown-menu {
		background: #44687d;
	}

	.dropdown-submenu>.dropdown-menu {
		display: block !important;
		margin-left: -1px;
		left: 70%;
		opacity: 0;
		visibility: hidden;
		border-radius: 0;
		overflow: hidden;
	}

	.dropdown-submenu:hover>.dropdown-menu {
		display: block !important;
		left: 100%;
		visibility: visible;
		-moz-transition: all 0.3s ease 0s;
		-ms-transition: all 0.3s ease 0s;
		-o-transition: all 0.3s ease 0s;
		-webkit-transition: all 0.3s ease 0s;
		transition: all 0.3s ease 0s;
		opacity: 1;
	}

	.dropdown:not(.megamenu) .dropdown-menu {
		padding: 0;
		min-width: 220px;
		white-space: nowrap;
		margin: 0;
		border-style: none;
		box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.3);
	}
	.dropdown:not(.megamenu):hover .dropdown-menu {
		display: block;
	}

	.blockquote-row>div:first-child {
		padding-right: 25px;
	}

	.blockquote-row>div:last-child {
		padding-left: 25px;
	}
	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection>.mainMenu {
		position: absolute;
		left: 0%;
		top: 0;
		width: 100%;
		margin: 0;
		list-style: none;
		padding: 0;
		flex-wrap: nowrap;
		z-index: 2;
		display: none;
	}
	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection:not(:first-child)>.mainMenu {
	}
	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection:not(.show-menu)>.mainMenu {
		z-index: -999;
		opacity: 0;
		visibility: hidden;
		pointer-events: none;
	}
	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection>.mainMenu {
		display: flex;
		flex-direction: row-reverse;
		z-index: -1;
		justify-content: space-between;
		min-height: 100%;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection>.mainMenu>li:first-child {
		flex: 0 0 34.4%;
		max-width: 33.4%;
		background: #0e55a4;
		padding: 10px 10px 10px 0;
		-webkit-transform: translateX(0%);
		transform: translateX(0%);
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection>.mainMenu>li:nth-child(2) {
		flex: 0 0 33.33%;
		max-width: 33.33%;
		padding: 20px 20px;
		border-right: 1px solid #DEE5ED;
	}

	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection>a {
		display: block;
		width: 100%;
		padding: 12px 20px 12px 75px;
		color: #333;
		background-color: white;
	}
	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection>a,
	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection>a>.nav_icon,
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul li a,
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul li a .link-icon {
		transition: all 0.3s ease;
	}
	
	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection.show-menu>a {
		color: #ffffff;
		background: #0E55A4;
	}
	
	.menubox + .menubox {
    margin-top: 10px;
}
	
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox h3, .header .navbar .nav li.dropdown .megaMenuSection li  .menubox h3>a {
		color: #ffffff;
		font-size: 18px;
		font-weight: 700;
	}
	
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox h3 {
}
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox h3:hover, .header .navbar .nav li.dropdown .megaMenuSection li .menubox h3:hover>a {
        color: #ffffff;
}
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox {
    padding: 15px;
}
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox p {
		color: #ffffff;
		font-size: 16px;
		margin-bottom: 0;
		font-weight: 400;
	}
	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection:first-child {
	}
	
	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection:last-child {
		margin-bottom: 15px;
	}
	
	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection {
		padding-left: 0;
	}
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox {
		padding: 15px 0 15px 30px;
	}
	.header .navbar .nav li.dropdown .megaMenuSection li .menuImgBox h3, .header .navbar .nav li.dropdown .megaMenuSection li .menuImgBox h3>a {
		font-size: 32px;
		color: #151515;
		font-weight: 700;
	}
	
	.header .navbar .nav li.dropdown .megaMenuSection li .menuImgBox h3 {
		margin: 0;
	}
	.header .navbar .nav li.dropdown .megaMenuSection li .menuImgBox a.imgwrap {
		padding: 0;
		margin: 0 0 15px;
		display: block;
		overflow: hidden;
	}
	
	.header .navbar .nav li.dropdown .megaMenuSection li .menuImgBox a.imgwrap img {
		width: 100%;
		transform: scale(1);
		transition: all 0.5s linear;
	}
	.header .navbar .nav li.dropdown .megaMenuSection li .menuImgBox:hover a.imgwrap img {
    transform: scale(1.2);
}
	.header .navbar .nav li.dropdown:not(.megamenu) li a {
		white-space: nowrap;
	}
	.header .navbar .nav li.dropdown:not(.megamenu) li:hover a {
		color: #2D55A3;
		background: #F2F2F2;
	}
	
}
/* 980px */

@media screen and (max-width:1600px) {
	.header .navbar-brand {
		width: 300px;
	}

	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
		width: calc(100% - 504px);
	}

	.navbar .container.containerCustom {}

	.dropdown-menu {
	}

	.header .navbar .nav>li:last-child>a {}

	.header .navbar .nav li.dropdown .megaMenuSection .searchHeading {
		max-width: 280px;
	}

	.sliderFrame .item ul li {
		padding-right: 60px;
	}

	.footer .row-fluid {
		padding: 0;
	}

	.sliderFrame {
		padding: 0;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection:last-child {
		padding-right: 0px;
	}

	blockquote,
	blockquote.pull-right {}
}

@media screen and (max-width:1500px) {
	.header .navbar-brand {
		/* max-width: 250px; */
	}

	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
		width: calc(100% - 428px);
		right: 180px;
	}

	.header .navbar-brand {
		/* padding-top: 32px; */
	}

	.header .navbar .nav li.dropdown .megaMenuSection .HeaderText {
		width: 300px;
	}

	.TitleText {}

	.captionFrame {}

	.header .navbar .nav li a {}

	header .navbar .nav li.headerlogin {
		width: 180px;
	}

	.header .navbar .nav li:nth-last-child(3) {}

	header .top-strip {
		padding-right: 280px;
	}

	img.footlogo {
		/* width: 250px; */
	}

	.footer .row.d-flex-wrap>div.col1 {
		-webkit-flex: 0 0 46%;
		flex: 0 0 46%;
		max-width: 46%;
		margin-right: 4%;
	}

	.footer .row.d-flex-wrap>div.col2,
	.footer .row.d-flex-wrap>div.col3 {
		-webkit-flex: 0 0 25%;
		flex: 0 0 25%;
		max-width: 25%;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxone {
		width: 300px;
	}

	.header .nav-collapse .nav .dropdown.headerlogin>ul.memberSection {
		padding: 80px 40px 30px 350px;
	}

	.header .navbar .nav li .memberSection a.toggle-form {
		font-size: 15px;
		top: 15px;
		right: 15px;
	}
}



@media screen and (max-width:1399px) {

	.captionFrame h1 {
		font-size: 36px;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .searchHeading {
		max-width: 250px;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu {}

	.header .navbar .nav>li.searchBtnFn a img {
		height: auto;
		width: 22px;
	}

	.header .navbar .nav li.dropdown .memberSection li form a.WhiteBorder {
		min-width: 110px;
	}

	header .top-strip {
		padding-right: 250px;
	}
}

@media screen and (max-width:1330px) {
	.header .navbar .nav>li>a {
		padding: 30px 9px;
	}

	.header .navbar .nav>li.searchBtnFn>a {
		padding: 28px 15px 28px 10px;
	}

/* 	.header .nav-collapse .nav .dropdown .dropdown-menu {
		padding: 40px 30px;
	} */

	.header .navbar .nav li.dropdown .megaMenuSection .heading {
		left: 0;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .HeaderText {
		width: 360px;
	}

	.header .navbar .nav>li {}

	.slider .owl-carousel .item img {}

	.searchnav-logo {
		max-width: 200px;
		padding: 35px 0 35px 20px;
		height: 105px;
	}

	.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection {
		width: 200px;
	}

	.nav-member-center {
		width: 180px;
		padding: 10px;
	}

	.nav-member-center p {
		font-size: 14px;
	}

	.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.member-center-wrap:last-child {
		max-width: 180px;
	}

	.TitleText {
		font-size: 40px;
	}

	.slider .owl-carousel .item {
		padding: 30px 0;
	}

	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
		height: 115px;
	}
}

@media screen and (max-width: 1199px) {
	header .top-strip {
		padding-right: 215px;
	}

	.inner-page-content .sidebar {
		flex: 0 0 400px;
		width: 400px;
	}

	.inner-page-content .inner-content-area {
		flex: 0 0 calc(100% - 400px);
		max-width: calc(100% - 400px);
		padding: 50px 100px 0 40px;
	}

	.TitleText {
		font-size: 36px;
	}

	blockquote,
	blockquote.pull-right {
		font-size: 20px;

	}

	blockquote:before,
	blockquote:after {
		width: 40px;
		height: 40px;
	}

	blockquote:before {
		left: -14px;
	}

	blockquote:after {
		right: -14px;
	}

	blockquote.pull-right:after {
		left: -14px;
	}

	blockquote.pull-right:before {
		right: -14px;
	}

	.captionBtnBox ul li a .iconBox img {
		width: 40px;
		height: 40px;
	}

	.captionBtnBox ul li a .iconBox {
		width: 50px;
	}

	.captionFrame ul li:nth-child(2) {}

	.captionFrame ul li:nth-child(1) {}

	.slider .owl-carousel .item img {}

	.info-iconbox img {
		width: 50px;
		height: 50px;
		padding: 0;
	}

	.info-iconbox span {}

	.info-iconbox h2 {}

	.newscard .news-inner-wrap {
		padding-left: 120px;
	}

	.newscard .news-inner-wrap img {
		left: 30px;
		width: 70px;
		height: 70px;
		object-fit: contain;
	}

	.info-iconbox .iconlink {
		width: 50px;
		height: 50px;
		font-size: 30px;
	}

	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
		height: 100px;
	}

	.captionFrame {}

	.captionFrame h3 {
		font-size: 20px;
	}

	.captionFrame h1 {
		font-size: 36px;
		margin-bottom: 20px;
	}

	.captionBtnFrame {
		max-width: 351px;
		padding: 25px 15px;
	}

	.captionBtnBox ul li a {
		padding: 10px 15px;
		min-height: 80px;
	}

	.captionBtnBox ul li a .textBox {
		left: 80px;
		max-width: 180px;
	}

	.captionBtnBox ul li a .textBox h2 {
		font-size: 18px;
	}

	.HeaderText {
		font-size: 36px;
	}

	.BodyTextLarge {
		font-size: 14px;
	}

	.header .nav-collapse .nav {
		/*width: 734px;*/
		width: auto;
	}

	.header .navbar .nav>li {}

	.header .navbar .nav li>a {
		font-size: 14px;
	}

	.header .navbar .nav>li.searchBtnFn>a {
		padding: 50px 10px 50px 5px;
	}

	.header .navbar .nav li .megaMenuSection a {
		height: auto;
	}

	.header .navbar .nav>li:last-child>a {}

	.header .navbar .nav li.dropdown .memberSection li,
	.header .navbar .nav li.dropdown .memberSection li p,
	.header .navbar .nav li.dropdown .memberSection li a {
		font-size: 14px;
	}

	.header .navbar .nav li.dropdown .memberSection li label {
		font-weight: 300;
		font-size: 14px;
		letter-spacing: 0.2px;
	}

	.header .navbar .nav li.dropdown .memberSection li input,
	.header .navbar .nav li.dropdown .memberSection li form a.btn,
	.header .navbar .nav li.dropdown .megaMenuSection .heading .btn {
		height: 40px;
		line-height: 36px;
	}

	.header .navbar .nav li.dropdown .memberSection li form a {
		width: 100%;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .HeaderText {
		font-size: 24px;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .heading .TitleText {
		font-size: 24px;
	}

	.header .navbar .nav>li.dropdown:last-child:hover:hover>a::after,
	.header .navbar .nav>li.dropdown:last-child:hover:focus>a::after,
	.header .navbar .nav>li.dropdown:last-child:hover:visited>a::after {
		border-top: 10px solid #017977;
	}

	.header .navbar .nav li.dropdown .megaMenuSection li a {
		/* font-size: 14px; */
	}

	.header .navbar .nav li.dropdown .megaMenuSection .searchHeading {
		max-width: 180px;
	}

	.header .navbar .nav>li.dropdown:hover>a::after,
	.header .navbar .nav>li.dropdown:focus>a::after,
	.header .navbar .nav>li.dropdown:visited>a::after {
		/* border-left: 15px solid transparent; */
		/* border-right: 15px solid transparent; */
		/* border-top: 10px solid #f1f1ef; */
		/* top: 80px; */
	}

	.header .navbar .nav li.dropdown .megaMenuSection .formframe {
		padding: 0;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .formframe input {
		height: 40px;
		padding: 0 15px 0 40px;
		background-position: left 15px center;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .formframe a {
		height: 40px;
		line-height: 36px;
		padding: 0 25px;
	}

	.header .navbar .nav li.dropdown .memberSection li form a:last-child {
		margin-left: 0px;
		margin-top: 10px;
		color: #ffffff;
	}

	.header .navbar .container.containerCustom,
	.container.containerCustom {
		width: 980px;
	}

	.header {
	}

	.header .navbar-brand {
		max-width: 210px;
		padding: 20px 15px 10px 0px;
	}

	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
		width: calc(100% - 349px);
		right: 150px;
	}

	.header .navbar .nav>li:nth-last-child(2)>a {}

	.header .navbar .nav li:nth-last-child(2) a img {
		margin-bottom: 2px;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta ul li:first-child {
		margin-left: 15px;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta ul li:first-child img {
		max-width: 200px
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu {
		top: 100px;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .HeaderText {
		width: 180px;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .heading {
		left: inherit;
	}

	.header .navbar .nav>li:nth-last-child(3)>a {}

	ul.follow-us li:first-child {
		margin-right: 10px;
	}

	/****************/
	.inner-page-content {}

	.inner-page-content .sidebar {
		width: 350px;
	}

	.inner-page-content .inner-content-area {
		padding: 50px;
	}

	header .navbar .nav li.headerlogin {
		width: 150px;
		padding: 0 10px;
		min-height: 100px;
	}

	.header-drop-title {
		width: 34%;
		padding-right: 20px;
	}

	.mainMenuMob {
		width: 66%;
	}

	.footer .row.d-flex-wrap>div.col1 {
		padding-right: 50px;
		-webkit-flex: 0 0 47%;
		flex: 0 0 47%;
		max-width: 47%;
		margin-right: 0;
		margin-bottom: 40px;
	}

	.footer .row.d-flex-wrap>div.col2 {
		margin-left: 1%;
	}

	.footer {
		padding: 50px 0 0;
	}

	.footer .row.d-flex-wrap>div.col2,
	.footer .row.d-flex-wrap>div.col3 {
		-webkit-flex: 0 0 26%;
		flex: 0 0 26%;
		max-width: 26%;
		margin-bottom: 40px;
	}

	.footer-links ul.social-list li {
		margin-right: 15px;
	}

	.footer-links h3,
	.contact-links h3 {
		font-size: 20px;
	}

	img.footlogo {
		/* width: 200px; */
	}

	ul.social-list li {
		margin-right: 10px;
	}
	

	.reduces-costs-sec .boxcontent.flex-sm-reverse .img-holder.img-with-shap {
		margin-right: -100px;
	}

	.img-card .img-card-content {
		padding: 20px 30px;
	}

	.testimonial-block p {
		font-size: 18px;
	}

	.memberCentral-today-sec h2 {
		font-size: 42px;
	}

	.HeaderTextSmall {
		font-size: 26px;
	}
	.navbar .nav>li.btn-link>a.MCButton {
		padding: 8px 20px;
		font-size: 16px;
	}
	.header .navbar .nav>li:not(.btn-link)>a {
		padding: 24px 12px;
	}
	.explore-links-sec ul {
		gap: 35px;
	}
	.pd_60 {
		padding: 40px 0;
	}
	.sec-shadowbox .span3 {
		flex: 0 0 33.33%;
		max-width: 33.33%;
	}
	.details-1 p {
		font-size: 17px;
	}
	.sec-topnotch .img-holder img {
		border-top-right-radius: 50px;
		border-bottom-left-radius: 50px;
	}
	.WhiteBorder, .TextButtonWhite {
		font-size: 16px;
	}
	.bring-toggather-sec .row.flex-row .span6:nth-child(1) {
		padding-right: 15px;
	}
	.Herobanner-2.slider {
		padding-top: 60px;
	}
	.explore-links-sec {
		margin-top: -150px;
	}
	.header .nav-collapse.collapse {
		gap: 20px;
	}
	.header .MCButton {
		padding: 10px 20px;
		font-size: 16px;
	}
	.slider {
		padding-top: 50px;
	}
	.testimonial-block {
		padding: 40px 30px;
	}
	.testimonial-block:before {
		width: 60px;
		height: 60px;
		background-size: contain;
	}
	.testimonials-sec .right-wrap p {
		font-size: 16px;
	}
	.testimonials-sec .right-wrap h2 {
		font-size: 18px;
	}
	.testimonial-block ul {
		flex: 0 0 calc(100% - 180px);
		max-width: calc(100% - 180px);
	}
	.testimonial-user .left-logo img {
		width: 50px;
	}
	.footer .row.d-flex-wrap>.foot-logo-wrap {
		-webkit-flex: 0 0 80px;
		flex: 0 0 80px;
		max-width: 80px;
	}
	.footer .row.d-flex-wrap>.rightfoot {
		-webkit-flex: 0 0 calc(100% - 80px);
		flex: 0 0 calc(100% - 80px);
		max-width: calc(100% - 80px);
	}
	.MCButton {
		font-size: 16px;
	}
}

@media(min-width: 980px) and (max-width: 1250px) {
	.header .navbar .nav>li.dropdown.megamenu>.dropdown-menu {
		padding-left: 33.2% !important; 
	}
}

/* 1199px */
@media(min-width: 980px) and (max-width: 1050px) {

	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
		width: calc(100% - 299px);
	}

	.header .navbar-brand {
		padding: 15px 10px 10px 0px;
	}

	.header .navbar .nav>li {}

	.header .navbar .nav>li>a {
		padding: 20px 8px;
	}

	.header .navbar .nav>li:nth-last-child(3)>a {
		padding: 15px 15px 15px 8px;
	}

	.header .navbar .nav>li:nth-child(2)>a {}

	header .navbar .nav li.headerlogin,
	.header {
		min-height: 100px;
	}

	.headerSpace {
		height: 100px;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu {
		top: 100px;
	}

}

@media screen and (max-width: 979px) {
	
	.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection.show-menu > a > .nav_icon img {
        filter: none !important;
        -webkit-filter: none !important;
        -moz-filter: none !important;
        -ms-filter: none !important;
        -o-filter: none !important;
    }

    .header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection.show-menu > a > h4,
    .header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection.show-menu > a > p {
        color: inherit !important;
    }

	header .navbar .nav li.headerlogin.show-form .nav-member-center {
		display: none !important;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxthree {
		width: 100%;
		padding: 15px;
		background: #ee3a43;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxone,
	.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxtwo {
		display: none !important;
		background: transparent !important;
		padding: 0 !important;
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection {
		background: #ee3a43;
		padding: 0 !important;
		position: relative;
	}

	.header .navbar .nav>li:not(.btn-link)>a {
		padding: 16px 0px 16px 20px;
		display: block;
	}

	.header .navbar .nav li .memberSection a.toggle-form {
		font-size: 0;
		line-height: 1;
		padding: 0 2px 0 0;
		top: 5px;
		right: 5px;
		background: #ffffff;
		color: #ee3a43;
		display: inline-flex;
		width: 30px;
		height: 30px;
		align-items: center;
		justify-content: center;
	}

	.header .navbar .nav li .memberSection a.toggle-form i {
		font-size: 20px;
		margin: 0;
	}

	header .navbar .nav li.headerlogin span.menu-arrow {
		display: none;
	}

	.RedButton,
	.header .navbar .nav li.dropdown .memberSection a.RedButton {
		align-self: self-end;
	}

	.header .navbar .nav li.dropdown .dropdown-menu .mainMenuMob-col:last-child a {
		border-bottom: none;
	}

	

	header .navbar .nav li.headerlogin a.nav-member-center p {
		margin: 0;
		padding-left: 15px;
	}

	header .navbar .nav li.headerlogin a.nav-member-center:after {
		display: none !important;
	}

	header .top-strip {
		display: none;
	}

	.headerSpace {
		height: 80px;
	}

	.header {
		background: #ffffff;
	}

	.inner-page-content>.row-fluid {
		display: block;
	}

	.droptitle {
		display: none;
	}

	.header .navbar .nav li.dropdown .dropdown-menu li a .droptitle {
		display: inline-block;
		margin: 0 !important;
		vertical-align: middle;
		font-size: 16px;
		font-weight: 400;
		text-transform: none;
	}

	.footer .row.d-flex-wrap>div.col2,
	.footer .row.d-flex-wrap>div.col3 {
		flex: 0 0 50%;
		max-width: 50%;
		margin-left: 0;
	}

	.loggedinBox {
		width: 100%;
		max-width: 100%;
		padding-right: 0;
	}

	.row.d-flex-wrap:before,
	.row.d-flex-wrap:after {
		display: none;
	}

	.slider .item>img {
		display: none;
	}

	.slider .item:before,
	.hero-banner:before {
		border-bottom-right-radius: 35%;
		left: 0;
		width: 100%;
		z-index: -1;
	}

	.slider .item {
		/* padding: 0; */
		padding-bottom: 146px;
	}

	.footer .row.d-flex-wrap>div.col1 {
		flex: 0 0 100%;
		max-width: 100%;
		margin-left: 0;
	}

	.footer .footer-links,
	.footer .footer-info {
		padding: 0px 0;
	}

	.infoicon-sec .flex-row .col-6 {
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		max-width: 100%;
	}

	.row.d-flex-wrap {
		margin-left: -15px !important;
		margin-right: -15px !important;
	}

	.col1.footer-info:before {
		width: 100vw;
		left: 50%;
		right: auto;
		transform: translateX(-50%);
		margin-left: 0;
	}

	.footer .row.d-flex-wrap>div.col1 .footstar {
		width: 30vw;
	}

	.header .navbar .nav>li.mobile-links ul.dropdown-menu {
		display: flex !important;
		background: #ffffff !important;
		flex-wrap: wrap;
		justify-content: center;
	}

	.header .navbar .nav>li.mobile-links span.menu-arrow {
		display: none !important;
	}

	.header .navbar .nav>li.mobile-links ul.dropdown-menu a {
		padding: 10px !important;
		height: auto !important;
		display: inline-block;
		color: #ed3943 !important;
		font-size: 12px !important;
		border-style: none !important;
		height: auto !important;
		min-height: auto !important;
		line-height: 1.4;
	}

	.header .navbar .nav>li.mobile-links ul.dropdown-menu li {
		flex: 0 0 auto;
		width: auto;
		margin: 0;
		border-style: none;
		padding: 0;
	}

	.header .navbar .nav>li.mobile-links ul.dropdown-menu li:not(:first-child):before {
		content: "/";
		color: #d1d1d1;
	}

	.event-mobile .sbm-event {
		border-top: 1px solid #ebebeb;
		padding: 15px 0px;
		margin-bottom: 0;
		border-radius: 0;
		text-align: center;
	}

	.captionBtnBox ul li a .iconBox img {
		width: 30px;
		height: 30px;
	}

	.event-mobile .event-list {
		padding-top: 15px;
	}

	.event-mobile .event-list .sbm-event .sbm-e-head span {
		min-width: auto;
	}

	.event-mobile .event-list .sbm-event .sbm-e-head span:after {
		margin: 0 20px;
		position: relative;
	}

	.event-mobile .event-list .sbm-event .sbm-e-head {
		justify-content: center;
	}

	.header-member-form {
		display: none !important;
	}

	header .navbar .nav li.headerlogin {
		width: 100%;
		max-width: 100%;
		display: block;
		background: transparent;
		text-align: center;
		min-height: auto;
		border-bottom: none;
		padding-top: 30px;
		padding-bottom: 10px;
	}

	.home3 header .navbar .nav li.headerlogin,
	.home2 header .navbar .nav li.headerlogin {
		background: transparent;
	}

	header .navbar .nav li.headerlogin a.member-center-btn {
		text-align: center;
		display: inline-block;
		background: #A8462B;
		color: #ffffff;
		width: auto;
		margin: 0 auto;
		padding: 5px 40px !important;
		border-radius: 50px;
		min-height: auto !important;
	}

	.member-center-btn {
		display: block;
	}

	header .navbar .nav li.headerlogin a.member-center-btn img {
		width: 35px;
		height: 35px;
		margin-right: 10px;
	}

	.header-drop-title {
		display: none;
		padding: 0;
		width: 100%;
		padding: 15px 0;
	}

	.mainMenuMob {
		display: block;
		width: 100%;
		background: transparent;
		padding: 5px 15px;
	}

	.mainMenuMob .mainMenuMob-col {
		display: block;
		width: 100%;
		padding: 0;
		float: none;
	}

	.mainMenuMob .mainMenuMob-col ul {
		margin: 0;
	}

	.header .navbar .nav li.dropdown .dropdown-menu li a {
		/* color: #083372; */
		/* font-size: 18px; */
	}

	.header .navbar .nav li.dropdown .dropdown-menu li a:hover {
	}

	.footer-links ul.social-list li {
		margin-right: 9px;
	}

	.footer-links h3,
	.contact-links h3 {
		font-size: 20px;
	}

	img.footlogo {
		max-width: 70%;
	}

	.infoicon-sec .flex-row .col-3 {
		flex: 0 0 50%;
		-webkit-flex: 0 0 50%;
		max-width: 50%;
		margin-bottom: 30px;
	}

	.info-iconbox h2 {
		min-height: auto;
		font-size: 20px;
	}

	.header .navbar .nav li.open-droupdown a {}

	.captionFrame ul li:nth-child(2) {}

	.whats-new-sec .flex-row>div.span8 {
		padding-left: 15px;
	}

	.whats-new-sec .flex-row>div.span8 {
		padding-left: 15px;
	}

	.newscard .news-inner-wrap {
		padding: 30px 30px 30px 100px;
	}

	.newscard .news-inner-wrap img {
		width: 50px;
		left: 15px;
	}

	.newscard .news-inner-wrap h2 {
		font-size: 25px;
		margin: 0 0 5px;
	}

	.magazine-block h2 {
		font-size: 25px;
		line-height: 1.5;
		margin-top: 20px;
	}

	.upcoming-event-sec .flex-row>div {
		width: 33.33%;
	}

	.img-card .img-holder span {
		width: 40px;
		height: 40px;
		font-size: 28px;
	}

	.img-card .img-holder span small {
		font-size: 16px;
	}

	.captionFrame ul li:nth-child(1) {
		margin: 0 0 10px;
	}

	.anouncebanner {
		display: block;
	}

	.mainMenuMobBtn {
		cursor: pointer;
		display: inline-block;
		font-size: 20px !important;
	}

	.mainMenuOnclickBtn {
		display: none;
		padding-left: 20px;
	}

	.megaMenuSection.closeBox ul.mainMenuOnclick {
		display: none !important;
	}

	.dropdown-menu>.megaMenuSection {
		margin-left: 0px;
	}

	.header .navbar .nav li.memberFirst {
		padding: 0;
		margin: 20px 0 0 0;
		padding: 0 30px 20px 30px;
		background: #fff;
	}

	.header .navbar .nav li>.dropdown-menu {
		padding: 0px 0 0 0px !important;
	}

	.header .navbar .nav li.memberFirst>a {
		background: #0BBA97;
		padding: 5px 20px;
		text-align: center;
		font-size: 14px;
		text-transform: uppercase;
	}

	.header .navbar .nav li.memberFirst .dropdown-menu li p a {
		padding: 0px;
		height: auto;
	}

	.header .navbar .nav li.memberFirst>a>img {
		margin-right: 20px;
	}

	.header .navbar .nav li.memberFirst>.dropdown-menu {
		margin: 0;
		padding: 0 !important;
		background: #2d2d2d;
	}

	.header .navbar .nav li.memberFirst>.dropdown-menu .megaMenuSection {
		padding: 20px;
		background: #2d2d2d;
	}

	.header .navbar .nav li.memberFirst>.dropdown-menu .megaMenuSection.formDiv {
		padding: 0 20px 20px 20px !important;
	}

	.header .navbar .nav li.memberFirst.open-droupdown>a,
	.header .navbar .nav li.memberFirst.open-droupdown:hover>a,
	.header .navbar .nav li.memberFirst.open-droupdown:focus>a,
	.header .navbar .nav li.memberFirst.open-droupdown:visited>a {
		background-color: #BA0C2F;
		color: #fff;
	}

	.header .navbar .nav li.memberFirst.open-droupdown .menu-arrow {
		display: block;
		width: 100%;
		left: 0;
		height: 86px;
		top: 0px;
		opacity: 1;
		transform: none;
		text-align: center;
	}

	.header .navbar .nav li.memberFirst.open-droupdown .menu-arrow:after {
		color: #fff !important;
		left: 10px;
	}

	.header .navbar .nav li.memberFirst>.menu-arrow {
		top: 20px;
	}

	.social-mobile,
	.mobile-links {
		display: block;
	}

	.header .navbar .nav>li.social-mobile.dropdown {
		background: #fff;
		padding: 0px 0 30px 0;
		text-align: center;
	}

	.header .navbar .nav>li.social-mobile.dropdown .follow-us.dropdown-menu {
		display: block !important;
		padding: 20px 0 0 !important;
		background: #ffffff;
	}

	.header .navbar .nav>li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection {
		font-size: 20px;
		font-weight: 600;
		display: inline-block;
		vertical-align: middle;
		width: auto;
		margin: 0 2px;
	}

	.header .navbar .nav>li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection:first-child {
		width: 100%;
		margin: 0 0 20px 0;
	}

	.header .navbar .nav>li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection a {
		width: 40px;
		height: 40px;
		color: #083372;
		line-height: 40px;
		font-size: 20px;
		text-align: center;
		border-radius: 50%;
		padding: 0;
	}

	.header .navbar .nav>li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection a:hover,
	.header .navbar .nav>li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection a:focus {
		border-color: #709ED1;
		color: #ffffff;
		background: #709ED1;
	}

	.header .navbar .nav>li.social-mobile.dropdown .menu-arrow {
		display: none;
	}

	.header .navbar .nav>li:nth-last-child(2)>a {}

	.TitleText {
		font-size: 32px;
	}

	.HeaderTextMediumLink {
		color: #008e89;
		font-size: 16px;
		text-decoration: none;
	}

	.HeaderTextSmall {
		font-size: 25px;
		margin-bottom: 10px;
	}

	.HeaderTextMedium {
		font-size: 18px;
	}

	.HeaderTextMediumLink {
		font-size: 16px;
	}

	.captionBtnBox ul li a .iconBox {
		margin: 0px 0px;
	}

	.captionBtnFrame {
		max-width: 320px;
		padding: 25px 10px;
	}

	.captionBtnBox ul li a .arrow {
		float: right;
		padding: 9px 0px;
	}

	.captionBtnBox ul li a .textBox h2 {
		font-size: 16px;
	}

	.captionFrame {
		max-width: 370px;
		margin-left: 30px;
	}

	.captionFrame h1 {
		font-size: 28px;
		margin-top: 10px;
	}

	.captionFrame h3 {
		font-size: 18px;
	}

	.slider .owl-carousel .item img {
		height: 405px;
		object-fit: cover;
	}

	.slider .owl-carousel .owl-dots {
		bottom: 30px;
	}


	.xsHidden979 {
		display: none !important;
	}

	.header .navbar .nav>li {
		padding: 0 30px;
	}

	.xs979 {
		display: block !important;
	}

	.header .navbar .nav .searchBtnFn.xs979 {
		margin: 0px;
		padding: 0;
		margin-bottom: 0px;
	}

	.header .navbar .nav .searchBtnFn.xs979 ul.dropdown-menu {
		display: block !important;
		padding-left: 0px !important;
		padding: 0 !important;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe {
		display: inline-block;
		vertical-align: top;
		width: 100%;
		margin: 0;
		background: #f3f3f3;
		height: 60px;
		padding: 10px;
		border-radius: 0;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe form {
		display: inline-block;
		width: 100%;
		margin: 0;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .menu-arrow {
		display: none;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe input {
		color: #33383A;
		font-size: 18px;
		font-weight: 400;
		background: #f3f3f3;
		font-family: 'Open Sans';
		padding: 5px 15px;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe a {
		border: 0;
		margin: 0;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe a img {
		margin: 0 auto;
		margin-top: 0px;
		padding: 0;
		width: 20px;
		height: 20px;
		line-height: 35px;
		float: none;
		margin-top: 0;
	}

	.header .navbar .nav>li.dropdown>a.dropdown-toggle:after, .header .navbar .nav>li.dropdown:hover>a::after, .header .navbar .nav>li.dropdown:focus>a::after, .header .navbar .nav>li.dropdown:visited>a::after, .header .navbar .nav>li.dropdown:hover>a::after, .header .navbar .nav>li.dropdown:focus>a::after, .header .navbar .nav>li.dropdown:visited>a::after {top: 23px;left: auto;right: 20px;position: absolute;width: 6px;height: 6px;border-width: 0;transform: rotate(-45deg);border: 2px solid #a0a0a0;border-style: none none solid solid;}


/* .header .navbar .nav>li.dropdown>a:before {
    content: "";
    z-index: 1;
    position: absolute;
    top: 28px;
    left: 0px;
    display: inline-block;
    background: #a0a0a0;
    width: 14px;
    height: 2px;
} */
	.header .navbar-brand {
		padding: 10px 20px 10px 0px;
		line-height: 60px;
		position: relative;
		z-index: 2;
		height: 80px;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe a {
		background: #ee3a43;
		border-radius: 50%;
		color: #fff;
		padding: 4px 10px;
		border: 0;
		margin: 0;
		line-height: normal;
		height: auto;
		position: relative;
		top: 0;
		width: 40px;
		height: 40px;
		display: inline-flex;
		align-items: center;
	}

	.btn.btn-navbar {
		min-width: auto;
	}

	.navbar .btn-navbar .icon-bar {
		width: 38px;
		margin: 0px auto 4px;
		height: 6px;
		border-radius: 2px;
	}

	.navbar .nav>li {
		width: 100%;
	}

	.header .nav-collapse.collapse {
		margin: 0;
		background: #fff;
		opacity: 1;
		position: absolute;
		top: 80px;
		width: 100%;
		box-shadow: none;
		display: block;
		margin: 0;
	}

	.header .nav-collapse.collapse .nav {
		padding: 10px 0px;
	}

	.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.formDiv {
		width: 100%;
	}

	.header .navbar .nav>li {
		max-width: 100%;
		height: auto;
		position: relative;
		width: 100%;
		vertical-align: top;
		border-bottom: 2px solid #efefef;
		padding: 0;
		min-height: auto;
	}

	.header .navbar .nav li a,
	.header .navbar .nav li a,
	.header .navbar .nav li .dropdown-menu>li>a {
		text-transform: uppercase;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .HeaderText {
		display: none;
		width: 100%;
	}

	.header .navbar .nav {
		position: relative;
	}

	.header .navbar .nav>li>a {
		margin: 0;
		padding: 0;
		border: 0px solid;
		background-color: transparent;
		height: auto;
	}

	/* .header .navbar .nav li:last-child a img {
		margin-right: 10px;
		margin-bottom: 0px;
		height: 26px;
		width: 30px;
		object-fit: contain;
	} */


	.header .navbar .nav>li:last-child a {}

	.brand {
		margin-left: -45px;
		max-width: 250px;
	}

	.header .navbar .container.containerCustom {
		width: 750px;
	}

	.container.containerCustom {
		width: 750px;
	}

	.navMain {
		float: none;
		height: 40px;
		padding: 0;
		text-align: center;
	}

	.header .navbar-inner {
		width: 100%;
	}

	.nav>.dropdown {
		padding-bottom: 0;
	}

	.navbar .btn-navbar .icon-bar {
		width: 30px;
		margin: 0px auto 4px;
		height: 4px;
		border-radius: 3px;
	}

	.navbar .btn-navbar .icon-bar:last-child {
		margin-bottom: 0;
	}

	.dropdown-menu {
		width: 100%;
	}

	.header .nav-collapse {
		float: none;
		padding: 0;
		width: 100%;
		z-index: 99;
		max-height: calc(100vh - 80px);
		overflow-y: auto;
	}

	.header .nav-collapse li {
		display: block;
		width: 100%;
		padding-bottom: 0px;
	}

	.header .navbar .nav li a,
	.header .navbar .nav li .dropdown-menu>li:last-child a {
		border: none;
		margin: 0;
	}

	.header .navbar .nav>li:last-child .menu-arrow {
		display: inline-block;
		width: 100%;
		left: 0;
		height: 100%;
		top: 0;
		opacity: 0;
	}

	.header .navbar .nav li .dropdown-menu>li>a {
		padding: 15px 15px;
		font-size: 13px;
	}

	.header .navbar .btn-navbar {
		margin: 0;
		position: absolute;
		right: 15px;
		top: 15px;
		background: none;
		border: none;
		-moz-border-radius: 4px;
		-ms-border-radius: 4px;
		-o-border-radius: 4px;
		-webkit-border-radius: 4px;
		border-radius: 4px;
		box-shadow: none;
		line-height: 1.42857;
		margin: 0;
		padding: 10px 12px;
		z-index: 9999;
	}

	.header .navbar .btn-navbar.collapsed {
		/* background: #fff; border-color: #fff;*/
		border-radius: 2px;
		color: #ffffff;
		padding: 30px 0px;
		height: auto;
		line-height: normal;
		margin-right: 0;
		margin-top: 0;
		width: 30px;
		z-index: 100;
		text-align: center;
		border-radius: 3px;
		top: 0;
		margin: 0;
	}

	.header .navbar .btn-navbar {
		top: 7px;
		padding: 0;
		margin: 30px 0;
	}

	.header .navbar-inner {
		position: relative;
		top: 0;
		width: 750px;
		margin: 0 auto;
		background: transparent;
	}

	.navIcon {
		background: #0c1923;
		min-height: 52px;
		z-index: 9;
		width: 100%;
	}

	.header .navbar .btn-navbar .icon-bar {
		background: #231f20!important;
		box-shadow: none;
	}

	.header .navbar .btn-navbar.collapsed .icon-bar {
		width: 30px;
		height: 4px;
		border-radius: 0;
		background: #2d2d2d!important;
		opacity: 1
	}

	.header .navbar .btn-navbar .icon-bar:first-child {
		transform: rotate(45deg);
	}

	.header .navbar .btn-navbar .icon-bar:nth-child(2) {
		display: none;
	}

	.header .navbar .btn-navbar .icon-bar:last-child {
		transform: rotate(-45deg);
		margin-top: -8px;
	}

	.header .navbar .btn-navbar.collapsed .icon-bar:first-child,
	.header .navbar .btn-navbar.collapsed .icon-bar:nth-child(2),
	.header .navbar .btn-navbar.collapsed .icon-bar:last-child {
		transform: none;
		display: block;
		margin-top: 0;
	}

	.header .navbar .btn-navbar:hover .icon-bar {
		background: #2d2d2d;
	}

	/*.header .navbar .btn-navbar:hover, .header .navbar .btn-navbar:focus, .header .navbar .btn-navbar:active, .header .navbar .btn-navbar.active, .header .navbar .btn-navbar.disabled, .header .navbar .btn-navbar[disabled], .header .navbar .btn-navbar:hover, .header .navbar .btn-navbar:focus, .header .navbar .btn-navbar:active, .header .navbar .btn-navbar.active, .header .navbar .btn-navbar.disabled, .header .navbar .btn-navbar[disabled], .header .navbar .nav li .dropdown-menu>li>a:hover, .header .navbar .nav li .dropdown-menu>li:hover a { background: #fff; border-color: #fff; }*/
	.header .navbar .nav li a,
	.header .navbar .nav li .dropdown-menu>li>a {
		text-align: center;
		color: #333;
		border-radius: 0;
	}

	.header .navbar .nav li a,
	.header .navbar .nav li a,
	.header .navbar .nav li .dropdown-menu>li>a {
		font-size: 18px;
		border: none;
		border-top-width: medium;
		border-bottom-width: medium;
		border-top-style: none;
		border-bottom-style: none;
		border-top-color: currentcolor;
		border-bottom-color: currentcolor;
		border-bottom-width: medium;
		border-bottom-style: none;
		border-bottom-color: currentcolor;
		border-top: 0px solid rgba(255, 255, 255, .5);
		background: transparent;
		font-weight: 400;
		line-height: 1.42857;
		color: #083372;
		text-decoration: none;
		text-transform: uppercase;
		padding: 0 20px 0px;
		padding-right: 0px;
		padding-right: 0px;
		text-align: left;
		margin-bottom: 0px;
		box-shadow: none;
		cursor: pointer;
	}

	.header .navbar .nav>li:last-child>a {
		margin: 0 auto;
		line-height: 1.5;
	}

	.header .navbar .nav li:hover a,
	.header .navbar .nav li:focus a,
	.header .navbar .nav li a:hover,
	.header .navbar .nav li a:focus {
		/* background: transparent; */
		/* color: #083372; */
		font-weight: 700;
		text-shadow: none;
		outline: none;
	}

	.header .navbar .nav li.memberFirst:hover a,
	.header .navbar .nav li.memberFirst:focus a {
		background: #BA0C2F;
		color: #fff;
	}

	.header .navbar .nav li:hover .menu-arrow::after,
	.header .navbar .nav li:focus .menu-arrow::after {
		color: #0BBA97;
	}

	.header .navbar .nav li.dropdown .megaMenuSection li a:focus {
	}

	.header .nav-collapse .nav .dropdown .dropdown-menu {
		height: auto !important;
		display: none;
	}

	.navbar .nav li.dropdown>.dropdown-toggle .caret {
		float: right;
		border-top-color: #eeeeee;
		border-bottom-color: #eeeeee;
	}

	.navbar .nav li.dropdown>.dropdown-toggle:hover .caret {
		border-top-color: #006eb3;
		border-bottom-color: #006eb3;
	}

	.header .navbar .pull-right>li>.dropdown-menu,
	.header .navbar .nav>li>.dropdown-menu {
		position: relative;
		float: none;
		width: auto;
		margin-top: 0;
		background-color: transparent;
		border: 0;
		box-shadow: none;
		top: 0 !important;
		margin: 0px;
		padding: 0;
		z-index: 1;
	}

	.dropdown .dropdown-menu {
		position: static;
		float: none;
		width: auto;
		margin-top: 0;
		background-color: transparent;
		border: 0;
		box-shadow: none;
		padding: 5px 0;
	}

	.dropdown .dropdown-menu li {
		padding: 0;
		background: transparent;
	}

	.header .navbar .nav li .dropdown-menu>li>a:hover {
		background: #c1d82f;
		color: #3b3b3c;
	}

	.dropdown-menu>li.active>a {
		color: #44687d;
	}

	.header .navbar .nav li.dropdown .dropdown-menu>li>a {
		border: 0;
		text-align: left;
		padding: 10px 10px 10px 70px;
		background: transparent;
		color: #083372;
		font-weight: 400;
		margin-bottom: 0px;
		position: relative;
	}
	.dropdown .dropdown-menu li.megaMenuSection>ul {margin: 0;padding: 0 0 0 25px;display: none;}
.dropdown .dropdown-menu li.megaMenuSection.menushow>ul {
    display: block;
}
	.header .nav li .dropdown-menu>li.dropdown-submenu li {
		padding: 0px 10px;
	}

	.header .nav li .dropdown-menu>li.dropdown-submenu li a {
		background: transparent;
		font-weight: normal;
	}

	.dropdown-submenu .caret {
		float: right;
		transform: rotate(-90deg);
		-webkit-transform: rotate(-90deg);
		-moz-transform: rotate(-90deg);
		-o-transform: rotate(-90deg);
		-ms-transform: rotate(-90deg);
		border-top-color: #eeeeee;
		border-bottom-color: #eeeeee;
		margin-top: 6px;
	}

	.dropdown-submenu a:hover .caret {
		border-top-color: #fff;
		border-bottom-color: #fff;
	}

	.header .navbar .nav li .dropdown-menu>li>a:hover {
	}

	.header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:hover,
	.header .navbar .nav li a:hover,
	.header .navbar .nav li a:focus,
	.navbar .nav li.dropdown.open>.dropdown-toggle,
	.navbar .nav li.dropdown.active>.dropdown-toggle,
	.navbar .nav li.dropdown.open.active>.dropdown-toggle,
	.dropdown:hover .dropdown-toggle {
		-moz-border-radius: 0;
		-ms-border-radius: 0;
		-o-border-radius: 0;
		/* -webkit-border-radius: 0; */
		/* border-radius: 0; */
	}

	.dropdown-menu {
		margin-left: 0 !important;
	}

	.header .nav-collapse li .menu-arrow::after {
		display: none;
		content: "\f107";
		font-family: 'FontAwesome';
		position: absolute;
		right: 17px;
		top: 12px;
		color: #fff;
		font-size: 24px;
		z-index: 99999;
		width: 15px;
		height: 15px;
		line-height: 15px;
		opacity: 1;
		font-weight: bolder;
		transform: rotate(270deg);
		font-weight: 300;
	}

	.header .nav-collapse li.open-droupdown .menu-arrow {
		transform: none;
	}

	.header .nav-collapse li.dropdown.memberFirst.xs979.open-droupdown .menu-arrow::after {
		content: "\f00d";
		font-family: 'FontAwesome';
		font-weight: 100;
		font-size: 18px;
	}

	.header .nav-collapse li .menu-arrow {
		cursor: pointer;
		width: 100%;
		background: transparent;
		left: 0;
		top: 0;
		position: absolute;
		height: 50px;
		z-index: 999;
	}

	.header .nav-collapse li.dropdown:hover:after,
	.header .nav-collapse li.dropdown.open::after {
		color: #9a0203;
	}

	.header .nav-collapse .nav {
		/* overflow-y: auto; */
		margin: 0;
		width: 100%;
		float: none;
		padding: 0;
		display: block;
	}

	.navbar .btn-navbar .icon-bar {
		transition: all ease-in-out 0.3s;
	}

	.navMain {
		box-sizing: border-box;
		display: block;
		height: 100%;
		left: 0;
		max-height: 0;
		opacity: 0;
		overflow-x: hidden;
		overflow-y: auto;
		position: static;
		-moz-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
		-ms-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
		-o-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
		-webkit-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
		transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
		width: 100%;
		z-index: 999;
	}

	body.overlay {
		position: fixed;
	}

	body.overlay .navMain {
		max-height: 100vh;
		opacity: 1;
	}

	.overlay header {
		position: fixed;
		top: 0;
		width: 100%;
		background: #FCFEFF;
	}

	.overlay .overlay div#semwebcatalog_application .browseLink-in li a,
	.overlay div#semwebcatalog_application .well,
	.overlay div#semwebcatalog_application .browseLink-in li a {
		position: static;
	}

	.navMain {
		border-bottom: none;
	}

	.interestGroup>.dropdown-menu li p:before {
		display: none;
	}

	.header .navbar .nav li.dropdown.interestGroup .megaMenuSection p.HeaderText {
		font-size: 18px !important;
		font-weight: 500;
		margin-bottom: 0px;
	}

	.header .navbar .nav li.dropdown.interestGroup .megaMenuSection p.HeaderText:hover,
	.header .navbar .nav li.dropdown.interestGroup .megaMenuSection ul li a:hover {
		text-decoration: underline;
	}

	.header .navbar .nav li.dropdown.interestGroup .megaMenuSection ul li a {
		padding-left: 20px;
	}

	.header .navbar .nav li.dropdown.interestGroup .megaMenuSection.xs979 .heading {
		position: static;
		text-align: left;
		margin-top: 20px;
	}

	.row-fluid .event_outer {
		width: 33.33%;
		margin: 0;
	}

	.eventimgText .HeaderText {
		font-size: 24px;
	}

	.eventimgText .HeaderText:after {
		bottom: -22px;
	}

	.event_outer:last-child .eventimgText .HeaderText:after {
		display: block;
	}

	.sliderFrame .item ul li {
		padding-right: 0;
	}

	.sliderFrame {
		padding: 0 15px;
	}


	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta {
		display: none;
	}

	.header .navbar .nav li:nth-last-child(1) ul.memberSection ul li a {
		height: auto !important;
	}

	.header .navbar .nav li:nth-last-child(1).dropdown .megaMenuSection .HeaderText {
		display: block;
	}

	.mainContent {
		width: calc(100% - 280px)
	}

	.pd_70 {
		padding: 50px 0;
	}

	.captionBtnBox ul li a .iconBox {
		width: 40px;
		height: 32px;
		position: relative;
		top: 1px;
	}

	.captionBtnBox ul li a .textBox {
		left: 70px;
	}

	.captionBtnBox ul li a .arrow {
		padding: 5px 0px;
	}

	.eventBoxFrame {
		padding: 15px 10px;
		margin-bottom: 50px;
	}

	.captionBtnBox ul li a .iconBox img.default {
		width: 30px;
	}

	.eventBoxFrame .HeaderTextSmall {
		line-height: 25px;
		margin-bottom: 25px;
	}

	.captionFrame ul {
		margin-bottom: 20px;
	}

	.carousel-caption {
		top: 44%;
	}

	.captionFrame ul li h1 {
		font-size: 32px;
		text-align: left;
	}

	.captionFrame ul li {}

	.captionFrame ul li:nth-child(2) {}

	.captionFrame ul li small {
		font-size: 16px;
	}

	.footer-info a>img {
		width: 235px;
	}

	.footer .footer-info {
		width: 100%;
		margin-bottom: 15px;
		position: relative;
	}

	.follow-us {
		position: absolute;
		right: 0;
		top: 0;
	}

	ul.follow-us li {
		font-size: 16px;
	}

	.footer-info p {
		margin: 25px 0 0 0;
	}

	.footer .row.d-flex-wrap .col1,
	.footer .row.d-flex-wrap .col2,
	.footer .row.d-flex-wrap .col3,
	.footer .row.d-flex-wrap .col4,
	.footer .row.d-flex-wrap .col5 {



		padding-left: 10px !important;
		padding-right: 10px !important;
	}

	ul.follow-us li:first-child {
		margin: 0 0 5px 0;
		display: block;
	}

	ul.follow-us li {
		margin: 0 4px 0 0;
	}

	.footer-links ul li a,
	.contact-links ul li a,
	.contact-links ul li span,
	.footer-links.contact-links ul li {
		font-size: 16px;
		line-height: 20px;
	}

	.footer-links ul li {
		margin-bottom: 8px;
	}

	.contact-links ul li {
		margin-bottom: 10px;
	}

	.copyright>p {
		max-width: 40%;
		text-align: left;
	}

	.copyright p,
	.copyright p a {
		font-size: 12px;
	}

	.copyright p a {
		padding: 0 6px 0 10px;
	}

	.copyright p a:first-child {
		margin-left: 15px;
	}

	/*****************/
	.inner-page-content {
		padding-left: 0;
		min-height: inherit !important;
	}

	.inner-page-content .inner-content-area {
		padding: 30px 0 0;
		margin-bottom: 30px;
	}

	.content-info {
		padding: 30px;
	}

	.inner-page-content .sidebar,
	.inner-page-content .inner-content-area {
		width: 100%;
		position: static;
		padding: 30px;
		flex: 0 0 100%;
		max-width: 100%;
	}

	.quicklink-mobile {
		display: block;
		background: #2d2d2d;
		margin: 30px 30px 0;
		background: #FFFFFF;
		box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
		padding: 15px 20px;
	}

	.quicklink-mobile h3 {
		margin: 0;
		color: #083372;
		position: relative;
		font-size: 20px;
		font-weight: 400;
	}

	.quicklink-mobile h3:before {
		content: "\f18e";
		font-family: FontAwesome;
		position: absolute;
		top: 0;
		right: 0;
	}

	.quicklink-mobile h3.quicklink-open:before {
		content: "\f01a";
	}

	.event-mobile {
		display: block;
		background: #2d2d2d;
		margin: 30px 30px 0;
		background: #FFFFFF;
		box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
		padding: 15px 20px;
	}

	.event-mobile h3 {
		margin: 0;
		color: #083372;
		position: relative;
		font-size: 20px;
		font-weight: 400;
	}

	.event-mobile h3:before {
		content: "\f18e";
		font-family: FontAwesome;
		position: absolute;
		top: 0;
		right: 0;
	}

	.event-mobile h3.event-open:before {
		content: "\f01a";
	}

	.DiamondBullets ul,
	.event-list ul {
		padding: 20px 0 0 0px;
		margin: 0;
		list-style: none;
	}

	.quicklink-desktop {
		display: none;
	}

	.events {
		margin-top: 0;
	}

	.sponsors-boxthree {
		margin: auto;
		width: 250px;
	}
	

	.header .navbar .nav li.dropdown .memberSection .megaMenuSection .DiamondBullets ul {
		padding: 0;
	}

	.header .navbar .nav li.dropdown .memberSection .megaMenuSection .DiamondBullets ul li {
		margin-bottom: 8px;
		padding-left: 30px;
	}

	.header .navbar .nav li.dropdown .memberSection .megaMenuSection .DiamondBullets ul li a {
		padding: 0;
		font-size: 16px;
	}

	.header .navbar .nav li.dropdown .memberSection .megaMenuSection .DiamondBullets ul li a:before {
		top: 3px;
	}

	.carousel-caption {
		position: relative;
		top: 0;
		transform: none;
		padding: 30px 0px 60px;
		max-width: 100%;
	}

	.slider .owl-carousel .item img {
		position: absolute;
		width: 100%;
		height: 100%;
		z-index: -1;
	}

	.captionFrame {
		max-width: 100%;
		margin: 0;
		padding: 0;
	}

	.captionBtnBox {
		position: relative;
		height: auto;
		background: #003485;
	}

	.home2 .captionBtnBox {
		background: #a2968c;
	}

	.home3 .captionBtnBox {
		background: #a8462b;
	}

	.captionBtnBox .captionBtnFrame {
		width: 100%;
		position: relative;
		max-width: 100%;
		padding: 20px 30px;
	}

	.slider .owl-carousel .owl-dots {
		position: absolute;
		transform: translateX(-50%);
		flex-direction: row;
		justify-content: center;
		height: auto;
		top: unset;
		left: 50%;
		bottom: 15px;
	}

	.captionBtnBox ul li a {
		min-height: 60px;
	}

	.captionBtnBox ul li a .iconBox svg {
		width: 30px;
		height: 30px;
	}

	.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.formDiv {
		padding: 0;
	}

	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
		height: auto;
		display: block !important;
		width: 100%;
	}

	header .navbar .nav li.headerlogin.show-form .header-member-form {
		display: flex !important;
	}

	header .navbar .nav li.headerlogin .header-member-form {
		background: #a8462b;
		padding: 30px 20px 15px 20px;
	}

	.header .navbar .nav li form a:last-child {
		font-size: 14px;
		line-height: 1.2;
		margin-top: 10px;
	}

	header .navbar .nav li.headerlogin .header-member-form {
		text-align: left;
	}

	header .navbar .nav li form a.MAJButton {
		height: auto;
		min-height: auto;
		line-height: 1.2;
	}

	header .navbar .nav li.headerlogin.show-form .header-member-form .MAJButton,
	header .navbar .nav li.headerlogin.show-form .header-member-form a:last-child {
		color: #ffffff;
	}

	header .navbar .nav li.headerlogin.show-form .header-member-form .MAJButton {
		background: #472103;
		line-height: 1.2;
	}

	header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form {
		position: absolute;
		top: 5px;
		right: 5px;
		padding: 0 !important;
		width: 30px;
		height: 30px;
		background: #472103;
	}

	header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form>img {
		display: none;
	}

	header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form {
		font-size: 0;
	}

	header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form:before,
	header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form:after {
		content: "";
		width: 15px;
		height: 2px;
		background: #ffffff;
		display: inline-block;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		z-index: 1;
	}

	header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form:after {
		-webkit-transform: translate(-50%, -50%) rotate(-45deg);
		transform: translate(-50%, -50%) rotate(-45deg);
	}

	header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form:before {
		-webkit-transform: translate(-50%, -50%) rotate(45deg);
		transform: translate(-50%, -50%) rotate(45deg);
	}

	header .navbar .nav li.headerlogin.show-form .header-member-form>p {
		margin-bottom: 8px;
	}

	.reduces-costs-sec .boxcontent .HeaderTextSmall {
		margin-bottom: 20px;
	}

	.info-iconbox span {
		padding: 22px;
	}

	.upcoming-event-sec .flex-row .btn-wrap {
		margin-top: 30px;
	}

	.testimonial-block p {
		font-size: 18px;
	}

	.testimonials-sec .right-wrap .img-holder {
		margin-right: -100px;
	}

	.memberCentral-today-sec h2 {
		font-size: 40px;
	}

	.footer .row.d-flex-wrap>.foot-logo-wrap {
		-webkit-flex: 0 0 60px;
		flex: 0 0 60px;
		max-width: 60px;
		padding-right: 0;
	}

	.footer .row.d-flex-wrap>.rightfoot {
		-webkit-flex: 0 0 calc(100% - 60px);
		flex: 0 0 calc(100% - 60px);
		max-width: calc(100% - 60px);
		padding-left: 0;
		padding-right: 10px;
	}

	.HeaderText {
		font-size: 35px;
	}

	.hero-banner .row.d-flex-wrap {
		min-height: 150px;
	}

	.hero-banner {
		padding: 40px 0;
	}

	.num-block {
		padding: 0;
	}
	.header .navbar .nav li.dropdown.megamenu {
		position: relative;
	}
	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection>.mainMenu>li:nth-child(2) {
		display: none;
	}
	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection>.mainMenu>li:nth-child(1) {
    margin-bottom: 15px;
}
	.nav a.has-after::after  {
		top: 15px;
		left: -14px;
		right: auto;
		position: absolute;
		width: 2px;
		height: 14px;
		border-width: 0;
		background: #a0a0a0;
		transform: rotate(0);
		content: "";
	}
	
	li.megaMenuSection.dromenulist>a:before {
			content: "";
			z-index: 1;
			position: absolute;
			top: 21px;
			left: -20px;
			display: inline-block;
			background: #a0a0a0;
			width: 14px;
			height: 2px;
	}
	/* .header .navbar .nav>li.dropdown.open-droupdown>a.dropdown-toggle:after, */
	li.megaMenuSection.dromenulist.menushow>a:after{
		display: none;
	}
	.sec-shadowbox .span3,
	.sec-shadowbox .span4 {
		flex: 0 0 50%;
		max-width: 50%;
	}

	.header .navbar .nav>li.dropdown>a {
		font-size: 15px;
	}
	.header .MCButton {
		margin: 20px 5%;
		width: 90%;
	}
	li.megaMenuSection.dromenulist>a:before {
		display: none;
	}
	.nav a.has-after::after {
		width: 7px;
		height: 7px;
		background: transparent;
		border: 2px solid #a0a0a0;
		border-style: none solid solid none;
		transform: rotate(45deg);
		left: auto;
		right: 20px;
		display: inline-block !important;
	}
	.dropdown .dropdown-menu li.megaMenuSection>ul.mainMenu {
		background: #0E55A4;
		padding: 10px 30px;
	}
	
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul li a {
		padding: 10px 20px;
	}
	
	.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul li a:hover {
		color: #ffffff;
	}

	.dropdown .dropdown-menu li.megaMenuSection.menushow>ul.mainMenu {
		display: flex;
		flex-direction: column-reverse;
	}
	
	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection>.mainMenu>li:nth-child(2) {
		display: block;
		background: #ffffff;
		padding: 20px;
		margin: 10px 0;
		border-radius: 5px;
	}
	.list-doubleline.check-list {
		margin-top: 15px;
	}	
	.infoicon-sec .row-fluid.flex-row .span3 {
		flex: 0 0 50%;
		max-width: 50%;
	}
	.infoicon-sec .row-fluid.flex-row {
		row-gap: 20px;
	}
	.banner-card.banner-card-2 .benefit {
		width: 110px;
		padding: 10px;
		left: -59px;
		top: -105px;
	}
	
	.banner-card .benefit span {
		min-height: 80px;
	}
	
	.banner-card .statistic {
		width: 220px;
		padding: 10px;
	}
	
	.banner-card .statistic span {
		font-size: 12px;
	}
	
	.banner-card .statistic span:first-child {
		font-size: 18px;
	}
	
	.banner-card .rating img {
		width: 12px !important;
	}
	
	.bring-toggather-sec .banner-card {
		margin: 150px 0 0;
	}
	
	.banner-card.banner-card-2 {
		padding-left: 60px;
	}
	
	.banner-card.banner-card-2 .statistic {
		left: 60px;
		top: -62px;
	}
	
	.banner-card.banner-card-2 .rating {
		top: -100px;
		left: 63px;
	}
	
	.banner-card .membership {
		width: 50px;
		height: 60px;
		padding: 10px;
	}
	
	.banner-card .edit-icon {
		width: 60px;
		height: 60px;
	}
	.accordion-design .accordion-toggle {
		font-size: 15px;
	}
	.banner-card.banner-card-2 .membership {
		bottom: 0;
	}
	.banner-card.banner-card-2 .benefit img {
		width: 30px;
	}
	.testimonial-block ul {
        flex: 0 0 100%;
        max-width: 100%;
    }

.testimonial-block {
    flex-wrap: wrap;
    flex-direction: column-reverse;
    gap: 30px;
}
.Herobanner-2.slider .carousel-caption {
    padding-bottom: 30px;
}
.explore-links-sec ul {
	gap: 20px;
}
.explore-links-sec ul li:not(:first-child) {
    display: none;
}

.explore-links-sec ul li:first-child {
    display: flex;
    justify-content: space-between;
    width: 100%;
    flex-direction: row-reverse;
    border-bottom: 1px solid #D0D0D0;
    padding-top: 16px;
    padding-bottom: 16px;
}

.explore-links-sec ul li:first-child:before {
    margin: 0;
}
.Herobanner-2.slider {
	padding-top: 0;
}	
.Herobanner-2.slider .row.d-flex-wrap {
    flex-direction: column;
}
.img-style-1 {
    padding: 0 0 30px;
}

.img-style-1 .is1-icon-1 {
    right: auto;
    left: 50%;
    top: auto;
    bottom: 0;
}
.Herobanner-2.slider .item {
    padding-bottom: 70px;
    z-index: 2;
    position: relative;
}
.explore-links-sec {
	border-radius: 0px;
}
.schedule-box .span6:first-child,
.schedule-box .span6 {
	padding: 0 15px;
}
.schedule-box {
	padding: 30px;
}
.check-list.list-col-4 li {
    flex: 0 0 calc(33.33% - 12px);
    max-width: calc(33.33% - 12px); 
}
.card-img-right-box .cirb-content {padding: 20px 70px 20px 30px;flex: 0 0 50%;max-width: 50%;}

.card-img-right-box .img-holder .icons-left-centred {
    width: 80px;
    height: 80px;
}

.card-img-right-box .img-holder .icons-left-centred img {
    max-width: 60%;
}
.card-img-right-box .img-holder {
    flex: 0 0 50%;
    max-width: 50%;
}
.number-box-sec .row.d-flex-wrap .span3 {
    flex: 0 0 50%;
    max-width: 50%;
}
.banner-card.banner-card-3 .benefit {
    width: 190px;
    top: -60px;
    left: 0;
}

.banner-card.banner-card-3 .edit-icon {
    width: 60px;
    height: 60px;
    padding: 12px;
}

.accordion-design .accordion-body .accordion-left-content .banner-card.banner-card-3 {
    margin: 80px 0 0;
}


.banner-card.banner-card-3 .statistic {
    padding-top: 110px;
    left: 0;
    width: 330px;
}

.banner-card.banner-card-3 .statistic span {
    font-size: 18px;
}
.banner-card.banner-card-3 .image-section img.w-100 {
	margin-left: 30px
}
.banner-card.banner-card-3 {
    margin: 80px 0 30px !important;
}
.sec-row-flex {}

.sec-row-flex .row.d-flex-wrap {
    flex-direction: column-reverse;
	gap: 30px;
}

.sec-row-flex .span6 {
    flex: 0 0 100%;
    max-width: 100%;
    width: 100%;
}
.card-icon-top-right .icons-top-right {
	width: 80px;
	height: 80px;
	margin-left: -40px;
	left: 50%;
	right: auto;
	padding: 15px;
}
.slider.home3-banner + .explore-links-sec {
    margin-top: -105px;
}


.slider.home3-banner .carousel-caption {
    padding-bottom: 0;
}
.fs21,
.captionFrame ul li.fs21 {
	font-size: 19px;
}
.explore-links-sec ul li a {
    padding: 10px 0;
    display: block;
}

}

/* 979px */
@media only screen and (min-width:768px) {
	.for-mobile {
		display: none;
	}

	.flex-sm-reverse {
		flex-direction: row-reverse;
	}
}

@media only screen and (max-width:767px) {
	.btns-wrap .MAJButton {
		margin-bottom: 15px;
	}

	.BulletList-row .BulletList {
		-webkit-flex: 0 0 100%;
		flex: 0 0 100%;
		max-width: 100%;
	}

	.BulletList-row {
		flex-wrap: wrap;
	}

	.BulletList-row .BulletList ul {
		margin-bottom: 0;
	}

	.sponsors-link ul a {
		padding: 12px 20px;
		font-size: 16px;
	}

	.row.row-flex>.span4,
	.row.row-flex>.span8 {
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		max-width: 100%;
		width: 100%;
	}

	.slider .row.d-flex-wrap .span6 {
		flex: 0 0 100%;
		max-width: 100%;
		margin: 0;
	}

	.slider .row.d-flex-wrap .span6>img {
		width: 80%;
		margin: 0 auto;
	}

	.slider .row.d-flex-wrap .span6:last-child {
	}

	.footer .row.d-flex-wrap>div.col2,
	.footer .row.d-flex-wrap>div.col3,
	.footer .row.d-flex-wrap>div.col4,
	.footer .row.d-flex-wrap>div.col5 {}

	.footer .row.d-flex-wrap>div.col2,
	.footer .row.d-flex-wrap>div.col3 {
		flex: 0 0 50%;
		max-width: 50%;
	}

	.friendsSliderBox .owl-carousel .owl-item ul li {
		max-width: 46%;
	}

	.home3 .footer .row.d-flex-wrap>div.col5,
	.home3 .footer .row.d-flex-wrap>div.col2 {
		flex: 0 0 50%;
		max-width: 50%;
		padding-top: 30px;
	}

	.footer .row.d-flex-wrap {
		justify-content: center;
	}

	.footer .row.d-flex-wrap>div.col5 {
		text-align: center;
		flex: 0 0 100%;
		max-width: 100%;
		padding-top: 0px;
	}

	.footer .row.d-flex-wrap>div.col5.footer-links h3:after {
		margin: 15px auto 15px;
	}

	.footer .row.d-flex-wrap>div.col5.footer-links .social-list {
		justify-content: center;
		margin-left: 10px;
	}

	.footer .row.d-flex-wrap>.foot-logo-wrap,
	.footer .row.d-flex-wrap>.rightfoot,
	.footer .row.d-flex-wrap .col1,
	.footer .row.d-flex-wrap .col2,
	.footer .row.d-flex-wrap .col3,
	.footer .row.d-flex-wrap .col4,
	.footer .row.d-flex-wrap .col5 {
		-webkit-flex: 0 0 100%;
		flex: 0 0 100%;
		max-width: 100%;
		padding-left: 15px;
		padding-right: 15px;
	}

	.footer .row.d-flex-wrap .col1,
	.footer .row.d-flex-wrap .col2,
	.footer .row.d-flex-wrap .col3,
	.footer .row.d-flex-wrap .col4,
	.footer .row.d-flex-wrap .col5 {
		text-align: center;
		margin: 15px 0;
	}

	.footer .row.d-flex-wrap>.foot-logo-wrap {
		text-align: center;
	}

	.footer .row.d-flex-wrap>.foot-logo-wrap img {
		height: 50px;
		object-fit: contain;
		padding-right: 15px;
	}

	.memberCentral-today-sec h2 {
		font-size: 32px;
	}

	.testimonials-sec .owl-theme .owl-dots {
		margin-top: 20px !important;
	}

	.testimonial-block p {
		font-size: 16px;
	}

	.testimonials-sec .left-wrap {
		padding-top: 50px;
		padding-bottom: 50px;
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		max-width: 100%;
		padding: 15px;
	}

	.testimonials-sec .right-wrap {
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		max-width: 100%;
		padding: 0;
		text-align: right;
		display: none;
	}

	.testimonials-sec .right-wrap img {
		width: 200px;
	}

	.testimonials-sec .right-wrap .img-holder {
		margin: 0;
	}

	.testimonials-sec .below-blue-img {
		display: none;
	}
/* 
	.testimonials-sec:before {
		content: "";
		background: #0655a3;
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
		border-bottom-right-radius: 40%;
	} */

	.sponsors-img-list ul li {
		max-width: 50%;
	}

	.whats-new-sec .flex-row>div.span4,
	.whats-new-sec .flex-row>div.span8 {
		flex: 0 0 100%;
		max-width: 100%;
	}

	.newscard:not(:first-child) {
		margin-top: 30px;
	}

	.whats-new-sec .flex-row>div.span8 {
		margin-top: 30px;
	}

	.newscard .newstag {
		font-size: 16px;
		line-height: 1.1;
	}

	.SectionHeader {
		font-size: 14px;
		text-align: center;
	}

	.info-iconbox {}

	.info-iconbox .iconlink {
		width: 40px;
		height: 40px;
		font-size: 20px;
	}

	.info-iconbox img {
		width: 40px;
		height: 40px;
	}

	.mt-40 {
		margin-top: 25px !important;
	}

	.newscard .newstag {
		width: 55px;
	}

	.friendsLogoBox .owl-carousel .owl-nav button.owl-prev,
	.friendsLogoBox .owl-carousel .owl-nav button.owl-prev:hover {
		margin-top: -15px;
		opacity: 1;
	}

	.friendsLogoBox .owl-carousel .owl-nav button.owl-next,
	.friendsLogoBox .owl-carousel .owl-nav button.owl-next:hover {
		margin-top: -15px;
		opacity: 1;
	}

	.newscard {
		padding-left: 55px;
	}

	.newscard .news-inner-wrap {
		padding: 15px 15px 15px 75px;
	}

	.newscard .news-inner-wrap img {
		width: 40px;
		height: 40px;
	}

	.newscard .news-inner-wrap h2 {
		font-size: 22px;
	}

	.learnMoreButton {
		font-size: 16px;
	}

	.learnMoreButton:before {
		padding: 2px 8px;
	}

	blockquote,
	blockquote.pull-right {
		font-size: 18px;

	}

	.upcoming-event-sec .flex-row>div {
		width: 100%;
		margin-bottom: 30px !important;
		height: auto;
	}

	.event-list .sbm-event .sbm-e-head span {
		min-width: auto;
		color: #495761;
		font-size: 14px;
	}

	.event-list .sbm-event .sbm-e-head {
		justify-content: center;
	}

	.event-list .sbm-event .sbm-e-head span:first-child:after {
		position: relative;
		margin: 0 20px;
		color: #33383A;
	}

	.event-list .sbm-event .sbm-e-head span>i {
		margin-right: 5px;
	}

	.sbm-event h4 {
		color: #33383A;
		font-weight: 400;
		font-size: 16px;
	}

	.side-title-center {
		position: relative;
		border-bottom: none;
	}

	.side-title-center>img {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		opacity: 0.2;
		width: 70px;
		height: 70px;
		object-fit: contain;
	}

	.events h3 {
		font-size: 20px;
		color: #083372;
		padding: 30px 0;
	}

	.img-card {
		text-align: center;

		max-width: 400px;
		margin: 0 auto 30px;
	}

	.img-card img {
		width: 100%;
	}

	.img-card .img-holder {
		max-width: 100%;
		margin: 0 auto;
	}

	.img-card .img-holder img {
		width: 100%;
	}

	.sidebar .events {
		display: none;
	}

	.infoicon-sec .flex-row .col-3 {
		flex: 0 0 50%;
		max-width: 50%;
		-webkit-flex: 0 0 50%;
		margin-bottom: 30px;
	}

	.for-desktop {
		display: none;
	}

	.captionFrame ul li:nth-child(2) {
		font-size: 17px;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe a {
		top: 3px;
	}

	.copyright-block {}

	.xs767 {
		display: block !important;
	}

	.xsHidden767,
	.xsHidden {
		display: none !important;
	}

	.pd_70 {
		padding: 30px 0px;
	}

	.header {
		min-height: 90px;
	}

	.header .navbar .nav li.dropdown .megaMenuSection .formframe input {
		font-size: 18px;
		height: 40px;
		padding: 0 15px;
		font-family: 'Roboto', sans-serif;
	}

	.header .nav-collapse {
		max-height: calc(100vh - 90px);
	}

	.header .navbar .nav li a,
	.header .navbar .nav li a,
	.header .navbar .nav li .dropdown-menu>li>a {
		font-size: 18px;
		padding: 8px 20px;
	}

	.header .navbar .nav li.dropdown .memberSection li form a:last-child {
		margin-left: 0px;
		font-weight: normal;
		width: 100%;
		padding: 0px 0px 0 0px;
		margin-top: 15px;
		line-height: 1.6;
		height: auto;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe input {
		height: 30px;
		width: calc(100% - 50px);
		margin: 9px 0;
	}

	.header .nav-collapse.collapse {
		left: 0;
		top: 90px;
	}

	.sidebar {
		width: 100%;
		max-width: 100%;
	}


	.captionBtnBox,
	.captionBtnFrame {
		width: 100%;
		max-width: 100%;
	}

	.captionBtnFrame {
		padding: 20px 20px;
	}


	.top-inner h1 {
		width: 50%;
		font-size: 22px;
	}

	.captionFrame ul li h1 {
		text-align: center;
	}

	.captionBtnBox ul li a .textBox h2 {
		font-size: 18px;
		font-weight: 400;
		margin-bottom: 5px;
	}

	.captionFrame {
		max-width: 100%;
		margin-left: 0;
		padding: 0px 0px;
		text-align: center;
	}

	.captionFrame h1 {
		margin-top: 0px;
		font-size: 32px;
		line-height: 36px;
	}

	.header .navbar .container.containerCustom,
	.container.containerCustom {
		width: 100%;
		padding: 0px 15px;
		margin: 0 auto;
	}

	.header .navbar-inner {
		position: relative;
		top: 0;
		margin: 0 auto;
		width: 100%;
	}

	.header .navbar-brand {
		margin-left: 0px;
	}

	.navbar .navbar-brand img {
		margin-left: 0px;
	}

	.captionFrame h3 {
		font-size: 22px;
	}

	.sliderFrame {
		margin: 55px 0 35px;
	}

	.carousel-caption {}

	.slider .owl-carousel .owl-dots {
		bottom: 20px;
	}

	.navbar .navbar-brand {
		max-width: 200px;
	}

	.header .navbar .nav>li:last-child a {
		/* height: 60px; */
		/* line-height: 60px; */
		/* font-size: 18px; */
	}

	.captionBtnBox ul li {
		margin-bottom: 15px;
	}


	.header .navbar-brand {
		height: 90px;
		padding: 18px 0 18px 0px;
	}

	.header .navbar .btn-navbar.collapsed {
		padding: 35px 0;
	}

	.header .navbar .btn-navbar {
		padding: 35px 0px 35px;
		margin: 0;
	}

	.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe a {
		top: 0;
		right: 10px;
	}

	.headerSpace {
		height: 90px;
	}

	.section-HeaderText {
		font-size: 25px;
		margin-bottom: 30px;
	}

	.BlackLine:before,
	.WhiteLine:before {
		width: 150px;
		bottom: -10px;
	}

	.friendsSliderBox {
		display: none;
	}

	.friendsSliderBox.friendsSliderBox-mobile {
		display: block;
	}

	.friendsSliderBox .HeaderText {
		margin-bottom: 15px;
		color: #6C6C6C;
		font-weight: 600;
		font-size: 14px;
		margin-top: 0;
	}

	.friendsSliderBox .owl-carousel ul li {
		width: auto;
		padding: 0;
	}

	.friendsSliderBox.friendsSliderBox-mobile .owl-nav {
		margin: 0;
	}

	.friendsSliderBox {
		margin-bottom: 20px;
	}

	.eventBoxFrame {
		padding: 30px 10px 50px 10px;
		margin-bottom: 0px;
		/* height: auto; */
	}

	.eventBox .LAFJButton {
		display: none;
	}

	.eventBoxFrame button {
		left: 0;
		margin: 0 auto;
	}

	.event-slider {
		margin-bottom: 30px;
	}

	.friendsLogoBox.pd_70,
	.become-member-section.pd_70 {
		padding-bottom: 50px;
	}

	.member-boxleft {
		width: 100%;
	}

	.member-boxright {
		padding-left: 0;
		margin-top: 20px;
	}

	.become-member-section .member-left {
		text-align: center;
	}

	.become-member-section .member-right {
		padding: 24px 0 0 0;
		margin: 40px 0 0 0;
		text-align: center;
	}

	.member-boxleft h3,
	.member-boxright h3 {
		font-size: 22px;
	}

	.member-right .member-boxright {
		width: 280px;
		margin: 0;
	}

	.member-right a.LAFJButton {
		width: 100%;
		display: block;
	}

	.footer .footer-links,
	.footer .contact-links {
		float: left;
	}

	.footer-links h3,
	.contact-links h3 {
		font-size: 22px;
		margin: 0 0 10px 0;
	}

	.footer .footer-info {}

	.footer .footer-info .foot-logo:after {
		margin: 15px auto 14px;
	}

	ul.follow-us {
		margin-top: 20px;
		position: static;
	}

	ul.follow-us li:first-child {
		display: none;
	}

	.footer-links ul li::before {
		display: none;
	}

	.footer-links ul li {
		margin-bottom: 10px;
		padding-left: 0;
	}

	.friendsSliderBox .owl-carousel ul li a {
		padding: 0 25px;
	}

	/*******************/
	.bannerInner h1 {
		font-size: 36px;
	}

	.quicklink-mobile,
	.event-mobile {
		padding: 10px 15px;
		margin: 15px 15px 0;
	}

	.DiamondBullets ul li a {
		font-size: 18px;
		padding: 15px 40px 15px 15px;
	}

	.content-info {
		padding: 20px 15px;
	}

	.content-info>h2 {
		font-size: 30px;
	}

	.content-info h3 {
		font-size: 25px;
		line-height: 1.3;
	}

	
	

	.inner-page-content .sidebar,
	.inner-page-content .inner-content-area {
		margin-bottom: 0;
		padding: 30px 15px;
	}

	.membership-headlinebox {
		display: none;
	}

	.BlackBorder {
		padding: 14px 18px;
	}

	.membership-headlinebox h5 {
		font-size: 20px;
	}

	.inner-page-content .sidebar {
		padding: 20px 15px;
		background: #fff;
	}

	.sponsors-boxtwo {
		margin: 30px 0 20px 0;
		padding: 0;
	}

	.sponsors-boxtwo img {
		width: 100%;
	}

	.sponsors-boxthree {
		width: 100%;
	}

	.captionBtnBox {}

	.captionBtnBox.captionBtnBox-mb {
		display: block;
	}

	.eventBoxFrame .HeaderTextSmall {
		margin-bottom: 35px;
	}

	.footer-info a>img {
		width: 280px;
	}

	.events {
		background: #F6F1E4;
		padding: 15px 15px;
		margin-bottom: 5px;
	}

	.sponsors-box {
		display: none;
	}

	.events .friendsLogoBox {
		background: #fff;
	}

	.forgot-mb {
		display: block;
		font-size: 14px;
		font-weight: 500;
		color: #535353;
		text-decoration: underline;
		margin-top: 10px;
	}

	.bannerInner {
		overflow: hidden;
		min-height: 200px;
	}

	.bannerInner img {
		width: auto;
		height: 100%;
		max-width: inherit;
	}

	.textBox p {
		color: #fff;
		margin: 0;
		line-height: 18px;
		font-size: 14px;
	}

	.friendsLogoBox {
		background: #fff;
	}

	.captionBtnBox ul li a .textBox {
		max-width: 100%;
	}

	.BlackBorder,
	.primary-btnmb {
		margin: 0px auto;
		width: 250px;
		display: block;
	}

	.events .friendsLogoBox {
		display: block;
	}


	.foot-logo-wrap {
		flex-flow: column;
		align-items: center;
	}

	.footer .footer-info ul.social-list {
		margin-top: 30px;
	}

	.HeaderTextSmall {
		font-size: 25px;
	}

	.reduces-costs-sec .boxcontent:not(.flex-sm-reverse) .img-holder {
		margin: 0;
	}

	.reduces-costs-sec .row.d-flex-wrap>.col-6 {
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		max-width: 100%;
	}

	.reduces-costs-sec .row.d-flex-wrap>.col-6:first-child,
	.reduces-costs-sec .boxcontent:not(.flex-sm-reverse) .img-holder {
		/* text-align: center; */
		/* margin-bottom: 20px; */
	}

	.reduces-costs-sec .row.d-flex-wrap>.col-6:first-child img,
	.reduces-costs-sec .boxcontent:not(.flex-sm-reverse) .img-holder img {
		width: 100%;
		object-fit: contain;
		margin: 0 auto;
	}

	.reduces-costs-sec .boxcontent.flex-sm-reverse .img-holder.img-with-shap,
	.inner-pg2.reduces-costs-sec .boxcontent.flex-sm-reverse .img-holder.img-with-shap {
		margin: -10% 0 0;
	}

	.infoicon-sec .flex-row .col-4 {
		flex: 0 0 50%;
		-webkit-flex: 0 0 50%;
		max-width: 50%;
	}

	.upcoming-event-sec .flex-row>.col-6 {
		-webkit-flex: 0 0 100%;
		width: 100%;
		flex: 0 0 100%;
	}

	.img-card .img-card-content {
		padding: 20px 15px;
	}

	.img-card h2 {
		font-size: 20px;
	}

	.testimonials-sec {
		margin-top: -50px;
	}

	.copyright>p {
		max-width: 100%;
		text-align: center;
		max-width: 100%;
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		margin: 15px 0;
	}

	.start-box {
		margin: 0 auto;
		width: 300px;
		flex: 0 0 225px;
		max-width: 225px;
	}

	.TitleText {
		font-size: 32px;
	}

	.HeaderText {
		font-size: 30px;
	}

	.upcoming-event-sec .flex-row>.col-4 {
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		width: 100%;
	}

	p.BodyTextLarge,
	.BodyTextLarge {
		font-size: 17px;
	}

	.iconbox {
		padding-left: 80px;
	}

	.iconbox .left-icon img {
		width: 40px;
		height: 40px;
	}

	.iconbox .left-icon {
		width: 65px;
		height: 65px;
		max-width: 65px;
		min-width: 65px;
	}

	.hero-banner {
		padding: 0px 0;
	}

	.num-sec .flex-row .col-4 {
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		max-width: 100%;
	}

	.num-block {

		padding: 20px 0;
	}

	.num-sec .flex-row .col-4:not(:last-child) {
		border-bottom: 2px solid #ffffff;
		border-right: 0px;
	}

	.pd_60.num-sec {
		padding: 20px 0;
	}

	.SubHeading {
	}

	.mob-text-center {
		text-align: center;
	}

	.memberCentral-today-sec .CTAWhiteBorder {
		font-size: 18px;
	}

	.reduces-costs-sec .boxcontent {
		padding-top: 0;
		padding-bottom: 0;
	}

	.pd_60 {
		padding-top: 40px;
		padding-bottom: 40px;
	}
	.slider {
		padding-top: 0;
	}
	.trust-member-sec {
		margin-top: -256px;
	}
	.slider .img-holder {
		max-width: 595px;
		margin: 0 auto;
	}
	.slider .row.d-flex-wrap {
		flex-direction: column-reverse;
	}
	.captionFrame ul li {
		text-align: center;
	}
	.left-icon-box .lib-icon {
		flex: 0 0 65px;
		-webkit-flex: 0 0 65px;
		max-width: 65px;
		height: 65px;
	}
	
	.left-icon-box .lib-content {
		flex: 0 0 calc(100% - 65px);
		-webkit-flex: 0 0 calc(100% - 65px);
		max-width: calc(100% - 65px);
		padding-left: 15px;
	}
	.infoicon-sec .row-fluid.flex-row .span3 {
        flex: 0 0 100%;
        max-width: 100%;
    }
	.sec-topnotch .span6 {
		flex: 0 0 100%;
		max-width: 100%;
	}
	.notch-box .row.d-flex-wrap {
		gap: 30px;
		flex-direction: column-reverse;
	}
	.notch-box {
		padding: 20px;
	}
	.bring-toggather-sec .row.flex-row .span6:nth-child(1) {
		display: none;
	}
	.bring-toggather-sec .row.flex-row .span6 {
		flex: 0 0 100%;
		max-width: 100%;
	}


	.accordion-design .accordion-body.in .accordion-left-content, .accordion-design .accordion-body .accordion-left-content {
		width: 100%;
		position: relative;
		top: 0;
		left: 0;
		transform: none;
		display: none;
		padding: 0 0px;
	}
		.accordion-design .accordion-body.in .accordion-left-content {
	    display: block;
	}
	
	.accordion-design .accordion-body.in .accordion-left-content .banner-card {
		margin: 50px 0 0;
	}
	.accordion-design .accordion-body .accordion-left-content .banner-card {
		max-width: 350px;
	}
	
	.accordion-design .accordion-body.in .accordion-left-content .banner-card.banner-card-2 {
		margin-top: 140px;
	}
	
	.bring-toggather-sec .btn-wrap {
		margin-top: 20px;
	}	
	.bring-toggather-sec .row.flex-row .span6:nth-child(2) {
		padding-left: 15px;
	}
	.reduces-costs-sec .span4 {
		padding: 0 15px;
	}
	.check-list.list-col-4 li {
		flex: 0 0 calc(50% - 12px);
		max-width: calc(50% - 12px); 
	}

	.card-img-right-box {
		flex-direction: column-reverse;
		box-shadow: none;
		gap: 20px;
	}
	
	.card-img-right-box .img-holder {
		flex: 0 0 100%;
		max-width: 100%;
	}
	
	.card-img-right-box .img-holder .icons-left-centred {left: 50%;top: 0;margin-left: -40px;}
	
	.card-img-right-box .cirb-content {
		flex: 0 0 100%;
		max-width: 100%;
		padding: 0;
		width: 100%;
	}
	
	.card-img-right-box .TextButton {
		margin: 0;
	}
	.number-box-sec .row.d-flex-wrap .span3 {
		flex: 0 0 100%;
		max-width: 100%;
	}
	.infoicon-sec.number-box-sec .row.d-flex-wrap {gap: 20px;}
	.numberBox {
		text-align: center;
	}
	.infoicon-sec.number-box-sec {
		padding-top: 40px!important;
		background: transparent;
	}
	.sec-shadowbox .span3, .sec-shadowbox .span4 {
		flex: 0 0 100%;
        max-width: 100%;
	}
	.schedule-box .span6 {
		flex: 0 0 100%;
		max-width: 100%;
		width: 100%;
	}
	.schedule-box .row.d-flex-wrap {
		gap: 30px;
	}
	.fs21, 
	.captionFrame ul li.fs21 {
		font-size: 17px;
	}
	.title20 {
		font-size: 17px
	}
}

/* 767px */

@media only screen and (max-width:600px) {
	.infoicon-sec .flex-row .col-3 {
		flex: 0 0 100%;
		max-width: 100%;
		-webkit-flex: 0 0 100%;
		margin-bottom: 30px;
	}

	.footer .container.containerCustom {
		max-width: 450px;
	}

	.footer .row.d-flex-wrap>div.col2,
	.footer .row.d-flex-wrap>div.col3 {
		flex: 0 0 100%;
		max-width: 100%;
	}

	.banner-card .benefit {
		padding: 10px;
		width: 100px;
		left: -80px;
	}
	
	.banner-card .benefit span {
		min-height: 80px;
	}
	
	.banner-card .badge {
		font-size: 10px;
	}
	
	.banner-card {
		padding-left: 80px;
	}
	
	.banner-card .statistic span {
		font-size: 10px;
	}
	
	.banner-card .statistic {
		padding: 10px;
		width: 185px;
		right: 75px;
	}
	
	.banner-card .statistic span:first-child {
	font-size: 16px;
	}
	
	.banner-card .image-section {
		padding-bottom: 20px;
	}
	
	.banner-card .membership {
		width: 50px;
		height: 50px;
		bottom: 6px;
		padding: 10px;
	}
	
	.banner-card .edit-icon {
		width: 50px;
		height: 50px;
	}
	
	.banner-card .rating {
		bottom: 65px;
		padding: 5px;
		border-radius: 5px;
	}
	
	.banner-card .rating img {
		width: 10px !important;
	}
	.owl-carousel .owl-nav button.owl-prev span, .owl-carousel .owl-nav button.owl-next span {
		width: 30px;
		height: 30px;
		line-height: 24px;
	}
	.list-doubleline.check-list {
		margin-bottom: 30px;
	}
	.list-doubleline.check-list li,
	.check-list.list-col-4 li {
		flex: 0 0 100%;
		max-width: 100%;
		margin-bottom: 0;
	}
}

@media only screen and (max-width:375px) {
	.friendsSliderBox .owl-carousel .owl-item ul li {
		max-width: 60%;
	}

	.HeaderTextSmall {
		font-size: 22px;
	}

	a.HeaderTextSmall,
	h4 a {
		font-size: 14px;
	}

	p,
	.BodyText {
		font-size: 14px;
		color: #434343;
	}

	.bannerInner .HeaderTextSmall {
		font-size: 16px;
	}

	.bannerInner .TitleText {
		font-size: 24px;
	}

	.sidebar {
		width: 100%;
		padding: 0;
	}

	.contentdivFrame {
		padding: 0px 0px 20px;
	}

	.header .navbar .container.containerCustom,
	.container.containerCustom {
		padding: 0px 15px;
	}

	.navbar .navbar-brand img.xsVisible {
		left: 0px;
	}

	.slider .owl-carousel .owl-dots {
		bottom: 15px;
	}

	.captionBtnBox ul li {
		margin-bottom: 10px;
	}


	.sliderFrame {
		padding: 0px;
	}

	.BodyTextLarge {}

	.top-header {
		background: #5b9cde;
	}

	.top-inner h1 {
		width: 100%;
	}

	.carousel-caption {}

	.footer-links ul li a,
	.contact-links ul li a,
	.contact-links ul li span,
	.footer-links.contact-links ul li {
		font-size: 14px;
	}

	.contact-links ul li i {
		font-size: 16px;
		width: 20px;
	}

	.header .navbar .nav li.dropdown .memberSection li a.LAFJButton {
		padding: 15px 20px;
	}


	.footer .row.d-flex-wrap>div.col2,
	.footer .row.d-flex-wrap>div.col3,
	.footer .row.d-flex-wrap>div.col4 {}
}

@media only screen and (max-width:320px) {
	.getinvolved-left {
		width: 120px;
	}

	.getinvolved-right {
		width: calc(100% - 125px);
	}

}