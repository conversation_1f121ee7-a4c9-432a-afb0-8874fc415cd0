/* tsApps styles and overrides */
@import url("/assets/common/css/tsApps.css");

/* Include in Editor: Start */
.TitleText {font-size: 65px;font-weight: bold;line-height: normal;margin-bottom: 15px;border: 0;text-shadow: none;text-align: left;color: #00529b;}
.HeaderText {font-size: 36px;font-family: "proxima-nova";color: #00529b;font-weight: 700;margin: 0 0 20px;}
.HeaderTextSmall{font-size: 28px;color: #00529b;line-height: 1.2;font-family: "proxima-nova";font-weight: 700;margin: 0 0 15px;}
.SectionHeader {font-weight: bold;font-size: 40px;display: block;color: #00529b;margin-bottom: 30px;margin-top: 0;}
.SubHeading{font-size: 22px;color: #00529b;font-family: "proxima-nova";}  
.DateStyle {font-size: 18px;display: block;color: #707070;font-weight: 700;margin-top: 5px;font-family: "proxima-nova";}
.GoldUnderLine {font-size: 18px;display: block;color: #9ea2a6;font-weight: 700;margin-top: 5px;font-family: 'proxima-nova';text-transform: uppercase;}
.GoldUnderLine:after {display: block;height: 4px;background: #e9b11d;content: "";width: 50px;margin: 10px 0 12px;}
.ColumnHeader {color: #00529b;font-size: 32px;margin: 0;line-height: 1.3;font-family: "activ-grotesk";}
.BodyText {font-size: 18px;line-height: 1.5;color: #000000;font-family: 'activ-grotesk', sans-serif;font-weight: 400;}
.BodyTextLarge {font-size: 20px; }
.InfoText {font-size: 14px; }
.GothamBook {font-family: 'activ-grotesk', sans-serif !important;}
.GothamBold {font-family: 'activ-grotesk', sans-serif !important;font-weight: 700 !important;}
.GothamMedium {font-family: 'activ-grotesk', sans-serif !important;font-weight: 500 !important;}
.ActivGroteskBook {font-family: activ-grotesk,sans-serif!important; }
.ActivGroteskBook {font-family: activ-grotesk,sans-serif!important; font-weight: 700!important;}
.ActivGroteskBook {font-family: activ-grotesk,sans-serif!important; font-weight: 500!important;}
.KBAButton {background: #e9b11d;border: 1px solid #e9b11d;color: #0c2c48;font-size: 14px;font-weight: 600;text-transform: uppercase;letter-spacing: 0.10em;text-decoration: none;display: inline-block;padding: 10px 30px;border-radius: 0;}
.NavyButton {color: #ffffff;border: 1px solid #0b2b47;background: #0b2b47;font-size: 14px;text-transform: uppercase;letter-spacing: 0.15em;text-decoration: none;display: inline-block;padding: 7px 15px;font-weight: 600;}
.LightBlueButton {color: #ffffff;border: 1px solid #438cca;background: #438cca;font-size: 14px;font-weight: 900;text-transform: uppercase;letter-spacing: 0.150em;text-decoration: none;display: inline-block;padding: 12px 20px;font-weight: 600;}
.SkyBlueButton {color: #ffffff;border: 1px solid #438ac9;background: #438ac9;font-size: 14px;text-transform: uppercase;letter-spacing: 0.15em;text-decoration: none;display: inline-block;padding: 7px 15px;font-weight: 600;
}
/* Include in Editor: Stop */

/*STYLES NOT IN THE EDITOR: ***********************************************************************************************/
a .BodyText, .BodyText a, a{color:#0086BF;}
a:hover .BodyText, .BodyText a:hover, a:hover{ color:#0086BF;}