use membercentral
GO

ALTER PROC dbo.tr_report_creditBalance
@orgID int,
@reportType varchar(20),
@payProfileList varchar(300),
@endDate datetime,
@displayType varchar(7),
@includeTotals bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tblCredits') IS NOT NULL 
		DROP TABLE #tblCredits;
	CREATE TABLE #tblCredits (memberID int, memberName varchar(300), memberNumber varchar(50), memberCompany varchar(200), 
		hasMemberPhotoThumb bit, totalCreditAmount decimal(18,2), creditAmount decimal(18,2), profileID int, profileName varchar(100),
		paymentTransactionID int, paymentDetail varchar(500), paymentTransactionDate datetime, paymentAmount decimal(18,2),
		batchID int, batchDate date, batchName varchar(400));

	-- displayType must be valid
	IF @displayType not in ('summary','detail')
		SET @displayType = 'summary';

	-- split payProfileList
	DECLARE @tblPayProfiles TABLE (profileID int);

	INSERT INTO @tblPayProfiles (profileID)
	select distinct mp.profileID
	from dbo.fn_intListToTable(@payProfileList,',') as tmp
	inner join dbo.mp_profiles as mp on mp.profileID = tmp.listitem 
	inner join dbo.mp_gateways as g on mp.gatewayID = g.gatewayID
	inner join dbo.sites as s on s.siteID = mp.siteID
	where s.orgID = @orgID
	and mp.[status] in ('A','I')
	and g.isActive = 1
	and g.gatewayType <> 'PayLater';

	IF @@ROWCOUNT = 0
		INSERT INTO @tblPayProfiles (profileID)
		select distinct mp.profileID
		from dbo.mp_profiles as mp
		inner join dbo.mp_gateways as g on mp.gatewayID = g.gatewayID
		inner join dbo.sites as s on s.siteID = mp.siteID
		where s.orgID = @orgID
		and mp.status IN ('A','I')
		and g.isActive = 1
		and g.gatewayType <> 'PayLater';

	IF @reportType = 'postedBatch' BEGIN
		DECLARE @newendDate datetime = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)));

		IF OBJECT_ID('tempdb..#tblTrans') IS NOT NULL 
			DROP TABLE #tblTrans;
		CREATE TABLE #tblTrans (transactionID int, memberID int, profileID int, batchID int, refundableAmount decimal(18,2), allocatedAmount decimal(18,2));
		
		insert into #tblTrans (transactionID, memberID, profileID, batchID)
		select t.transactionID, m.activememberID, mp.profileID, b.batchID
		from dbo.tr_batches as b
		inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.batchID = b.batchID
		inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = bt.transactionID
		inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
		inner join @tblPayProfiles as mpList on mpList.profileID = mp.profileID
		inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID 
			and t.transactionID = bt.transactionID
			and t.typeID = 2	
			and t.statusID in (1,2)
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.assignedTomemberID
		where b.orgID = @orgID
		and b.statusID = 4
		and b.depositDate <= @newendDate;

		update tmp
		set tmp.refundableAmount = ref.refundableAmount
		from #tblTrans as tmp
		cross apply dbo.fn_tr_getRefundableAmountofPaymentByBatchDate(@orgID,tmp.transactionID,@newendDate) as ref;

		update tmp
		set tmp.allocatedAmount = alloc.allocatedAmount
		from #tblTrans as tmp
		cross apply dbo.fn_tr_getAllocatedAmountofPaymentByBatchDate(@orgID,tmp.transactionID,@newendDate) as alloc;

		IF @displayType = 'summary'
			insert into #tblCredits (memberID, memberName, memberNumber, memberCompany,	hasMemberPhotoThumb, profileID, profileName, creditAmount)
			select m.memberID, m.lastname + ', ' + m.firstname as memberName, m.memberNumber, m.company, m.hasMemberPhotoThumb,
				mp.profileID, mp.profileName, sum(t.refundableAmount - t.allocatedAmount) as creditAmount
			from #tblTrans as t
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.memberID
			inner join dbo.mp_profiles as mp on mp.profileID = t.profileID
			group by m.memberID, m.lastname + ', ' + m.firstname, m.memberNumber, m.company, m.hasMemberPhotoThumb, mp.profileID, mp.profileName
			having sum(t.refundableAmount - t.allocatedAmount) > 0;
		ELSE
			insert into #tblCredits (memberID, memberName, memberNumber, memberCompany,	hasMemberPhotoThumb, profileID, profileName, 
				paymentTransactionID, paymentDetail, paymentTransactionDate, paymentAmount, batchID, batchDate, 
				batchName, creditAmount)
			select m.memberID, m.lastname + ', ' + m.firstname as memberName, m.memberNumber, m.company, m.hasMemberPhotoThumb,
				mp.profileID, mp.profileName, tP.transactionID, tP.detail, tP.transactionDate, tP.amount,
				b.batchID, b.depositDate, b.batchName, sum(t.refundableAmount - t.allocatedAmount) as creditAmount
			from #tblTrans as t
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.memberID
			inner join dbo.mp_profiles as mp on mp.profileID = t.profileID
			inner join dbo.tr_transactions as tP on tP.ownedByOrgID = @orgID and tP.transactionID = t.transactionID
			inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = t.batchID
			group by m.memberID, m.lastname + ', ' + m.firstname, m.memberNumber, m.company, m.hasMemberPhotoThumb, mp.profileID, mp.profileName,
				tP.transactionID, tP.detail, tP.transactionDate, tP.amount, b.batchID, b.depositDate, b.batchName
			having sum(t.refundableAmount - t.allocatedAmount) > 0;
	END
	ELSE BEGIN
		-- Real-Time Credit Balances
		insert into #tblCredits (memberID, memberName, memberNumber, memberCompany, hasMemberPhotoThumb, profileID, profileName, creditAmount)
		select m.memberID, 
			m.lastname 
			+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m.suffix,''),'') else '' end
			+ ', ' + m.firstname 
			+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m.middleName,''),'') else '' end
			as memberName, m.memberNumber, m.company, m.hasMemberPhotoThumb,
			mp.profileID, mp.profileName, cb.balance
		from dbo.tr_creditBalances as cb
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = cb.memberID
		inner join dbo.organizations as o on o.orgID = @orgID
		inner join dbo.mp_profiles as mp on mp.profileID = cb.profileID
		inner join @tblPayProfiles as mpList on mpList.profileID = mp.profileID
		where cb.orgID = @orgID;
	END

	IF @@rowCount > 0 and @includeTotals = 1
		insert into #tblCredits (memberID, memberName, memberNumber, memberCompany, hasMemberPhotoThumb, profileID, profileName, creditAmount)
		select NULL, 'Report Total', NULL, NULL, 0, profileID, profileName, sum(creditAmount)
		from #tblCredits
		group by profileID, profileName
		order by profileName;

	-- update totalCreditAmount by member
	update tmp
	set tmp.totalCreditAmount = tmp2.totalCreditAmount
	from #tblCredits as tmp
	inner join (
		select isnull(memberID,0) as memberID, sum(creditAmount) as totalCreditAmount
		from #tblCredits
		group by isnull(memberID,0)
	) as tmp2 on isnull(tmp2.memberID,0) = isnull(tmp.memberID,0);

	SELECT memberID, memberName, memberNumber, memberCompany, hasMemberPhotoThumb, totalCreditAmount, creditAmount,
		profileID, profileName, paymentTransactionID, paymentDetail, paymentTransactionDate, paymentAmount,
		batchID, batchDate, batchName,
		ROW_NUMBER() OVER (ORDER BY CASE WHEN memberID IS NULL THEN 2 ELSE 1 END, memberName, profileName) AS rowNum
	FROM #tblCredits;


	IF OBJECT_ID('tempdb..#tblCredits') IS NOT NULL 
		DROP TABLE #tblCredits;
	IF OBJECT_ID('tempdb..#tblTrans') IS NOT NULL 
		DROP TABLE #tblTrans;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
