<cfoutput>
	<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
	<cfset local.isLoggedIn = 0>
	<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)> 
		<cfset local.isLoggedIn = 'loggedIn'>
	</cfif>

	<cfset local.zone = "A"><!-- Site Logo -->
	<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
		<div id="zoneAObj" class="hide">
			#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
		</div>
	</cfif>

	<cfset local.menuContent = ''>
	<cfif structKeyExists(local.strMenus,"primaryNav")>
		<cfset local.menuContent = trim(REReplace(REReplace(local.strMenus.primaryNav.menuHTML.rawcontent,"<p>",""),"</p>",""))>
		<div id="menuObj" class="hide">	
			#local.menuContent#
		</div>
	</cfif> 
	
	<!--Header Start-->
      <header class="header outer-width">
         <div id="navbar-example" class="navbar">
            <div class="navbar-inner">
               <div class="container">
                  <div class="row-fluid">
                     <a class="btn btn-navbar collapsed" data-toggle="collapse" data-target=".nav-collapse">
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                     </a>
                     
                     <div class="nav-collapse collapse navListWrap">
                        <div class="top-action-wrapper">
                           <div class="centered-btn-wrap">
                           </div>
                            <div class="navigation-close">
                              <a class="close-menu" href="javascript:void(0);">
                                <i class="fa-solid fa-xmark"></i>
                             </a>
                           </div>

                        </div>
                        <ul class="nav menuNavWrap hidden">
                           <li class="searchBtnFn xs979 navLiList ">
                              <ul>
                                 <li class="span9 ">
                                    <div class="formframe">
                                       <form name="searchbox" id="searchbox11" action="/?pg=search" method="get">
											<input name="pg" id="pg" type="hidden" value="search">
											<input name="s_a" id="s_a" type="hidden" value="doSearch"> 
											<input name="s_frm" id="s_frm" type="hidden" value="1">
											<input name="s_key_all" id="s_key_all" autocomplete="off"
												placeholder="What can we help you find?"
												value="" type="text">

											<a href="javascript:void(0);" onclick="initiateHeaderSerach('searchbox11');" >
												<i class="fa fa-search"></i>
											</a>
                                       </form>
                                    </div>
                                 </li>
                              </ul>
                           </li>
                           
                        </ul>

                        <div class="rightMenus hidden">
                           <ul>
                              <li class="searchBtnFn xsHidden979">
                                 <a href="javascript:void(0);" class="searchLg">
                                    <i class="fa fa-search"></i>
                                 </a>
                                 <ul class="searchLgUl">
                                    <li class="span9">
                                       <div class="formframe">
                                          <form name="searchbox" id="searchbox" action="/?pg=search" method="get"> 
										  	<input name="pg" id="pg" type="hidden" value="search">
											<input name="s_a" id="s_a" type="hidden" value="doSearch"> 
											<input name="s_frm" id="s_frm" type="hidden" value="1"> 

                                            <input name="s_key_all" id="s_key_all" autocomplete="off"
												placeholder="What can we help you find?"
												value="" type="text">
                                             <a href="javascript:void(0);" onclick="initiateHeaderSerach('searchbox');" class="btnWhite">Search</a>
                                          </form>
                                          <a href="##" class="searchclose">
                                             <svg width="19" height="19" viewBox="0 0 19 19" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                   d="M11.4197 9.49948L18.6026 2.31603C19.1325 1.78616 19.1325 0.927246 18.6026 0.397403C18.0716 -0.132468 17.2127 -0.132468 16.6828 0.397403L9.5 7.58004L2.31715 0.397403C1.78621 -0.132468 0.928323 -0.132468 0.397414 0.397403C-0.132472 0.927273 -0.132472 1.78619 0.397414 2.31603L7.58027 9.49948L0.397414 16.6829C-0.132472 17.2128 -0.132472 18.0717 0.397414 18.6026C0.663402 18.8675 1.01033 19 1.35728 19C1.70528 19 2.0522 18.8675 2.31715 18.6026L9.5 11.42L16.6828 18.6026C16.9478 18.8675 17.2958 19 17.6427 19C17.9897 19 18.3366 18.8675 18.6026 18.6026C19.1325 18.0717 19.1325 17.2128 18.6026 16.6829L11.4197 9.49948Z"
                                                   fill="##9A8D83" />
                                             </svg>
                                          </a>
                                       </div>
                                    </li>
                                 </ul>
                              </li>
                              <li class="headerlogin headerMyCaaa">
                              </li>
								<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
									<li class="logout-btn">
										<a href="/?logout">
											<img src="./images/logout.png" alt="">
										</a>
									</li>
								</cfif>
                           </ul>
                        </div>
                       
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <!-- /navbar- -->
      </header>
      <div class="headerSpace"></div>
      <!--Header End-->
</cfoutput>