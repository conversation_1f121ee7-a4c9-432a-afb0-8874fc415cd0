<cfcomponent output="no">

	<cffunction name="generateCertificate" access="public" returntype="struct" output="no">
		<cfargument name="registrantID" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { certificateURL='', certificatePath='', certificateFolderPath='' }>
		<cfset local.objEvent = CreateObject("component","model.events.events")>

		<cfset local.startTime = getTickCount()>

		<!--- get event info --->
		<cfquery name="local.qryGetEventFromRegistrant" datasource="#application.dsn.membercentral.dsn#">
			select top 1 reg.eventid, r.recordedOnSiteID, s.defaultLanguageID, m.activeMemberID as memberID, s.sitecode, o.orgcode, o.orgID
			from dbo.ev_registrants as r
			inner join dbo.sites as s on s.siteid = r.recordedOnSiteID
			inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID and reg.siteID = s.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
			inner join dbo.ams_members as m on m.memberID = r.memberID
			where r.registrantID = <cfqueryparam value="#arguments.registrantID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfset local.strEvent = local.objEvent.getEvent(eventid=local.qryGetEventFromRegistrant.eventid, siteid=local.qryGetEventFromRegistrant.recordedOnSiteID, languageid=local.qryGetEventFromRegistrant.defaultLanguageID)>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryGetEventFromRegistrant.siteCode)>

		<!--- Get all the registrant ids --->
		<cfquery name="local.qryAllRegistrantIDs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam value="#local.qryGetEventFromRegistrant.recordedOnSiteID#" cfsqltype="CF_SQL_INTEGER">;

			select reg.registrantID
			from (
				select e.eventID as masterEventID, subEvent.eventID as childEventID
				FROM dbo.ev_events as e
				inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = e.eventID 	
				where e.siteID = @siteID
				and e.eventID = <cfqueryparam value="#local.qryGetEventFromRegistrant.eventid#" cfsqltype="CF_SQL_INTEGER">
			) as tmp
			inner join dbo.ev_events e2 on e2.siteID = @siteID
				and e2.eventID = tmp.childEventID 
				and e2.status = 'A'
			inner join dbo.ev_registration as r on r.siteID = @siteID
				and r.eventID = tmp.childEventID
			inner join dbo.ev_registrants reg on reg.registrationID = r.registrationID
				and reg.status = 'A'
				and reg.memberid = <cfqueryparam value="#local.qryGetEventFromRegistrant.memberID#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>

		<cfset local.registrantsIncluded = arguments.registrantID>
		<cfif local.qryAllRegistrantIDs.recordcount>
			<cfset local.registrantsIncluded = valuelist(local.qryAllRegistrantIDs.registrantID) & "," & arguments.registrantID>
		</cfif>

		<!--- determine which certificates we need to generate --->
		<cfquery name="local.qryCertficatesToGenerate" datasource="#application.dsn.membercentral.dsn#">
			select c.certificateID, rc.creditValueAwarded, isnull(ecast.ovTypeName,cat.typeName) as creditType, cat.typeName as originalCreditType,
				ec.ApprovalNum as courseApproval, ecas.certificateMessage, cs.statementAppProvider, ename.contentTitle as eventName,
				a.authorityID, a.authorityName, a.state, c.certFileName, s.siteCode as certSiteCode, o.orgcode as certOrgCode,
				ec.completeByDate, c.isPortrait, case when c.isportrait = 0 then 'landscape' else 'portrait' end as orientation, ec.notes as notes
			from dbo.ev_registrants as r
			inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
			inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
			inner join dbo.crd_offerings as ec on ec.offeringID = ect.offeringID
			inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
			inner join dbo.crd_authorityTypes as cat on cat.typeID = ecast.typeID
			inner join dbo.crd_authoritySponsors as ecas on ecas.asid = ecast.asid
			inner join dbo.crd_sponsors as cs on cs.sponsorID = ecas.sponsorID
			inner join dbo.crd_certificates as c on c.certificateID = ecast.LiveApprovedCertificateID
			inner join dbo.ev_events e on e.eventID = ec.eventid
			inner join dbo.cms_contentLanguages as ename on ename.contentID = e.eventContentID and ename.languageID = 1
			inner join dbo.crd_authorities as a on a.authorityID = ecas.authorityID		
			inner join dbo.sites as s on s.siteID = c.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
			where r.registrantid in (<cfqueryparam value="#local.registrantsIncluded#" cfsqltype="CF_SQL_INTEGER" list="true">)			
			and r.status = 'A'
			and r.attended = 1
			and rc.creditAwarded = 1
		</cfquery>
		<cfquery name="local.qryUniqueCertficatesToGenerate" dbtype="query">
			select distinct certificateID, certFileName, certSiteCode, certOrgCode, isPortrait, orientation, notes
			from [local].qryCertficatesToGenerate
		</cfquery>

		<!--- if no certificates to generate, kick out --->
		<cfif local.qryCertficatesToGenerate.recordcount is 0>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- get registrant info --->
		<cfset local.registrantID = arguments.registrantID>
		<cfset local.qryRegistrantInfo = CreateObject("component","eventReg").getRegistrantInfo(mid=local.qryGetEventFromRegistrant.memberID)>

		<!--- get other member data needed for the certificates. No need to get the data we already have from qryRegistrantInfo --->
		<cfset local.memberFieldsList = "">
		<cfloop query="local.qryUniqueCertficatesToGenerate">
			<cfset local["obj#local.qryUniqueCertficatesToGenerate.certFileName#"] = createObject("component","sitecomponents.#local.qryUniqueCertficatesToGenerate.certOrgCode#.#local.qryUniqueCertficatesToGenerate.certsiteCode#.certificate.#local.qryUniqueCertficatesToGenerate.certFileName#")>
			<cfset local.thisCertMergeCodes = local["obj#local.qryUniqueCertficatesToGenerate.certFileName#"].getCertMergeCodes()>
			<cfif listLen(local.thisCertMergeCodes.member)>
				<cfset local.memberFieldsList = listAppend(local.memberFieldsList,local.thisCertMergeCodes.member.listItemTrim())>
			</cfif>
		</cfloop>
		<cfset local.memberFieldsList = listRemoveDuplicates(local.memberFieldsList, ',', true).listFilter(function(el) {
			return NOT listFindNoCase("prefix,firstName,middleName,lastName,suffix,professionalSuffix,company,memberNumber",arguments.el);	
		})>
		<cfif listLen(local.memberFieldsList)>
			<cfset local.qryMemberFields = application.objMember.getMemberDataByFields(orgID=local.qryGetEventFromRegistrant.orgID, memberID=local.qryGetEventFromRegistrant.memberID, fieldsList=local.memberFieldsList)>
		<cfelse>
			<cfset local.qryMemberFields = queryNew("memberID","Integer")>
		</cfif>


		<!--- registrant custom fields and role fields --->
		<cfset local.objResourceCustomFields = CreateObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.qryCrossEventCustomFields = local.objResourceCustomFields.getResponses(itemType="CrossEvent", itemID=local.strEvent.qryEventMeta.siteResourceID).map(function(thisRow){
			if (arguments.thisRow.dataTypeCode == 'STRING' and arguments.thisRow.displayTypeCode == 'TEXTAREA')
				arguments.thisRow.customValue = Replace(arguments.thisRow.customValue,newLine(),"<br/>","ALL");
			return arguments.thisRow;
		})>
		<cfset local.qryEventCustomFields = local.objResourceCustomFields.getResponses(itemType="eventRegCustom", itemID=arguments.registrantID).map(function(thisRow){
			if (arguments.thisRow.dataTypeCode == 'STRING' and arguments.thisRow.displayTypeCode == 'TEXTAREA')
				arguments.thisRow.customValue = Replace(arguments.thisRow.customValue,newLine(),"<br/>","ALL");
			return arguments.thisRow;
		})>
		<cfset local.qryRegistrantRoleFields = local.objResourceCustomFields.getResponses(itemType="EventRole", itemID=arguments.registrantID).map(function(thisRow){
			if (arguments.thisRow.dataTypeCode == 'STRING' and arguments.thisRow.displayTypeCode == 'TEXTAREA')
				arguments.thisRow.customValue = Replace(arguments.thisRow.customValue,newLine(),"<br/>","ALL");
			return arguments.thisRow;
		})>

		<cfset local.showTimeZone = true>
		<cfif local.mc_siteinfo.defaultTimeZoneID eq local.strEvent.qryEventTimes_selected.timezoneID and local.strEvent.qryEventMeta.alwaysShowEventTimezone eq 0>
			<cfset local.showTimeZone = false>
		</cfif>
		
		<!--- event dates/times --->
		<cfset local.eventtime = local.objEvent.generateEventDateString(mode='eventCertificate', 
			startTime=local.strEvent.qryEventTimes_selected.startTime, endTime=local.strEvent.qryEventTimes_selected.endTime, 
			isAllDayEvent=local.strEvent.qryEventMeta.isAllDayEvent, showTimeZone=local.showTimeZone, 
			timeZoneAbbr=local.strEvent.qryEventTimes_selected.timeZoneAbbr)>

		<!--- maximum credit possible --->
		<cfset local.eventsIncluded = local.qryGetEventFromRegistrant.eventid>
		<cfif local.strEvent.qrySubEvents.recordcount>
			<cfset local.eventsIncluded = valuelist(local.strEvent.qrySubEvents.childEventID) & "," & local.qryGetEventFromRegistrant.eventid>
		</cfif>
		<cfquery name="local.qryPossibleCredits" datasource="#application.dsn.membercentral.dsn#">
			select isNull(creditValue, 0.00) as creditValue, st.typeName, ename.contentTitle eventName, isnull(c.ovTypeName,st.typeName) as creditType, cs.status
			from dbo.crd_offeringTypes et
			inner join dbo.crd_offerings ec on ec.offeringID = et.offeringID
			inner join dbo.crd_authoritySponsorTypes c on c.astid = et.astid
			inner join dbo.crd_authorityTypes st on st.typeID = c.typeID
			inner join dbo.ev_events e on e.eventID = ec.eventid
			inner join dbo.crd_statuses cs ON cs.statusID = ec.statusID
			inner join dbo.cms_contentLanguages as ename on ename.contentID = e.eventContentID and ename.languageID = 1
			where ec.eventID in (<cfqueryparam value="#local.eventsIncluded#" cfsqltype="CF_SQL_INTEGER" list="true">)
			order by ename.contentTitle, st.typeName
		</cfquery>

		<cfquery name="local.qryAllCreditTypes" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#CreateTimeSpan(0,0,5,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID INT = <cfqueryparam value="#local.mc_siteinfo.orgID#" cfsqltype="CF_SQL_INTEGER">;

			select ca.authorityCode, cat.typeCode, isnull(ast.ovtypeName,cat.typeName) as authorityTypeNameOrg, cat.typeName as authorityTypeName
			from dbo.crd_sponsors as cs
			inner join dbo.crd_authoritySponsors as cas on cas.sponsorID = cs.sponsorID
			inner join dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
			inner join dbo.crd_authorityTypes as cat on cat.authorityID = ca.authorityID
			inner join dbo.crd_authoritySponsorTypes as ast on ast.ASID = cas.ASID and ast.typeID = cat.typeID
			where cs.orgID = @orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.EventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=local.mc_siteinfo.siteID)>

		<!--- event roles and the names for each --->
		<cfquery name="local.qryEventRoles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @orgID int, @categoryTreeID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.siteID#">;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.orgID#">;
			select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EventAdminSRID#">, 'Event Roles');

			select c.categoryName, 
				ltrim(m.prefix + ' ' + m.firstname + ' ' + m.lastname + ', ' + m.company) as fullname,
				m.firstname, m.lastname, m.middleName, m.memberNumber, m.company
			from dbo.cms_categories c
			inner join dbo.ev_registrantCategories rc on rc.categoryID = c.categoryid
				and c.categoryTreeID = @categoryTreeID
				and c.isActive = 1 
				and c.parentCategoryID is NULL
			inner join dbo.ev_registrants r on r.registrantID = rc.registrantID
			inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID 
				and reg.siteID = @siteID 
				and reg.status = 'A'
				and reg.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryGetEventFromRegistrant.eventid#">
			inner join dbo.ams_members m on m.orgID = @orgID and m.memberID = r.memberID 
			inner join dbo.ams_members m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<!--- event roles for registrant --->
		<cfquery name="local.qryRegistrantRoles" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @categoryTreeID int;
			select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EventAdminSRID#">, 'Event Roles');

			select c.categoryID, c.categoryname
			from dbo.ev_registrantCategories as rc
			inner join dbo.cms_categories as c on c.categoryID = rc.categoryID
			where rc.registrantID = <cfqueryparam value="#arguments.registrantID#" cfsqltype="CF_SQL_INTEGER">
			and c.categoryTreeID = @categoryTreeID
			order by c.categoryname;
		</cfquery>
		
		<cfset local.orgIdentityInfo = CreateObject("component","model.admin.organization.organization").getorgIdentityDetailsStruct(orgID=local.mc_siteinfo.orgID, orgIdentityID=local.mc_siteinfo.defaultOrgIdentityID)>

		<!--- set merge attributes --->
		<cfset local.strCertMergeCodes = {
			"certificate" = { 
				"imagesurl" = local.mc_siteinfo.internalAssetsURL & "images/",
				"siteName" = local.mc_siteinfo.siteName,
				"siteCode" = local.mc_siteinfo.siteCode,
				"orgID" = local.mc_siteinfo.orgID,
				"orgIdentityInfo" = local.orgIdentityInfo
				},
			"event" = {
				"qryEventMeta" = local.strEvent.qryEventMeta,
				"qryEventCategories" = local.strEvent.qryEventCategories,
				"hasSubEvents" = local.strEvent.hasSubEvents,
				"qryEventTimes" = local.strEvent.qryEventTimes_selected,
				"qryEventSponsors" = local.strEvent.qryEventSponsors,
				"eventdateFormatted" = local.eventtime,
				"qryEventCustomFields" = local.qryCrossEventCustomFields,
				"qryEventRoles" = local.qryEventRoles
				},
			"registrant" = {
				"registrantID" = arguments.registrantID,
				"qryEventCustomFields" = local.qryEventCustomFields,
				"qryEventRoles" = local.qryRegistrantRoles,
				"qryRoleFields" = local.qryRegistrantRoleFields
				},
			"credit" = {
				"qryPossibleCredits" = local.qryPossibleCredits,
				"qryAllCreditTypes" = local.qryAllCreditTypes
				},
			"member" = {
				"prefix" = local.qryRegistrantInfo.prefix,
				"firstName" = local.qryRegistrantInfo.firstname,
				"middleName" = local.qryRegistrantInfo.middlename,
				"lastName" = local.qryRegistrantInfo.lastname,
				"suffix" = local.qryRegistrantInfo.suffix,
				"professionalSuffix" = local.qryRegistrantInfo.professionalsuffix,
				"company" = local.qryRegistrantInfo.company,
				"memberNumber" = local.qryRegistrantInfo.memberNumber
				}
			}>
		<cfif local.qryMemberFields.recordCount>
			<cfset local.strCertMergeCodes.member.append(QueryRowData(local.qryMemberFields,1),true)>
		</cfif>

		<!--- Extend timeout in case file system slowly responding --->
		<cfsetting requesttimeout="500">

		<!--- loop over certificates and generate them --->
		<cfset local.arrCertificates = arrayNew(1)>		
		<cfset local.arrCertificatesOrientation = arrayNew(1)>		
		<cfloop query="local.qryUniqueCertficatesToGenerate">
			<cfset local.certStr = structNew()>
		
			<!--- get specific cert info --->
			<cfquery name="local.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate" dbtype="query">
				select certificateID, certOrgCode, certSiteCode, certFileName, creditValueAwarded, creditType, originalCreditType, 
					courseApproval, certificateMessage, statementAppProvider, eventName, authorityID, authorityName, state,completeByDate
				from [local].qryCertficatesToGenerate
				where certificateID = #local.qryUniqueCertficatesToGenerate.certificateID#
			</cfquery>
			<cfset local.strCertMergeCodes.credit.notes = local.qryUniqueCertficatesToGenerate.notes>
			
			<!--- render cert --->	
			<cfset local.certBody = local["obj#local.qryUniqueCertficatesToGenerate.certFileName#"].generateCertBody(strCertMergeCodes=local.strCertMergeCodes)>			
			<cfset local.certStr = structNew()>
			<cfset local.certStr.orientation = local.qryUniqueCertficatesToGenerate.orientation>
			<cfset local.certStr.certBody = local.certBody>				
			<cfset arrayAppend(local.arrCertificates,local.certStr)>
			<cfset structDelete(local, "certStr")>
		</cfloop>

		<!--- create temp directory for pdf --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.qryGetEventFromRegistrant.sitecode)>
		
		<!--- loop over array of certificates and create a PDF --->
		<cfloop from="1" to="#arrayLen(local.arrCertificates)#" index="local.thisCert">
			<cfset generateCertificatePDF(local.arrCertificates[local.thisCert].certBody,"#local.strFolder.folderPath#/ev_#local.thisCert#A.pdf",local.arrCertificates[local.thisCert].orientation)>
		</cfloop>

		<!--- merge pdfs --->
		<cfdirectory action="LIST" directory="#local.strFolder.folderPath#" name="local.qryPDFs" filter="ev_*.pdf" sort="name">
		<cfif local.qryPDFs.recordcount>
			<cfset application.objCommon.mergePDFs(local.strFolder.folderPath,valuelist(local.qryPDFs.name),"certificate.pdf")>
		<cfelse>
			<cfset local.returnStruct = { certificateURL='', certificatePath='', certificateFolderPath='' }>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- set temp url to certificate --->
		<cfset local.returnStruct.certificateURL = "/tsdd/" & application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/certificate.pdf", displayName="certificate.pdf", forceDownload=0, deleteSourceFile=0)>
		<cfset local.returnStruct.certificateFolderPath = local.strFolder.folderPath>
		<cfset local.returnStruct.certificatePath = "#local.strFolder.folderPath#/certificate.pdf">

		<cfset local.timeMS = getTickCount() - local.startTime>
		<cfset logCertGenerateTime(registrantID=arguments.registrantID, certificateID=local.qryUniqueCertficatesToGenerate.certificateID, timeMS=local.timeMS)>
		
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="generateCertificatePDF" access="private" returntype="void" output="no">
		<cfargument name="pdfTextIn" type="string" required="yes">
		<cfargument name="filepath" type="string" required="yes">
		<cfargument name="orientation" type="string" default="portrait" required="false">
		
		<cfdocument filename="#arguments.filepath#" pagetype="letter" margintop="0" marginbottom="0" marginright="0" format="PDF" marginleft="0" backgroundvisible="Yes" orientation="#arguments.orientation#" unit="in" fontembed="Yes" scale="100">
			<cfdocumentsection margintop=".25" marginbottom=".25" marginright=".25" marginleft=".25">
				<cfoutput>#arguments.pdfTextIn#</cfoutput>
			</cfdocumentsection>
		</cfdocument>		
	</cffunction>
	
	<cffunction name="generateCertificateEmail" access="public" returntype="struct" output="no">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="customText" type="string" required="no" default="">

		<cfset var local = structNew()>

		<cfset local.returnStruct = structNew()>

		<cfquery name="local.qryGetEventFromRegistrant" datasource="#application.dsn.membercentral.dsn#">
			select top 1 reg.eventid, r.recordedOnSiteID, s.defaultLanguageID, m.activeMemberID as memberid, s.siteCode
			from dbo.ev_registrants as r
			inner join dbo.sites as s on s.siteid = r.recordedOnSiteID
			inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID and reg.siteID = s.siteID
			inner join dbo.ams_members as m on m.memberID = r.memberid
			where r.registrantID = <cfqueryparam value="#arguments.registrantID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryGetEventFromRegistrant.siteCode)>
		<cfset local.strEvent = CreateObject("component","model.events.events").getEvent(eventid=local.qryGetEventFromRegistrant.eventid, siteid=local.qryGetEventFromRegistrant.recordedOnSiteID, languageid=local.qryGetEventFromRegistrant.defaultLanguageID)>
		<cfset local.qryRegistrantInfo = CreateObject("component","eventReg").getRegistrantInfo(mid=local.qryGetEventFromRegistrant.memberID)>
		<cfset local.returnStruct.memberID = local.qryGetEventFromRegistrant.memberID>
		<cfset local.returnStruct.subject = "Your Certificate: #local.strEvent.qryEventMeta.eventContentTitle#">
		<cfset local.returnStruct.registrantName = "#local.qryRegistrantInfo.FirstName# #local.qryRegistrantInfo.lastname#">
		<cfset local.returnStruct.emailto = local.qryRegistrantInfo.email>
		<cfset local.returnStruct.replyto = trim(local.strEvent.qryEventRegMeta.replyToEmail)>

		<!--- html version --->
		<cfsavecontent variable="local.returnStruct.htmlemail">
			<cfoutput>
			#local.qryRegistrantInfo.FirstName# #local.qryRegistrantInfo.lastname#:<br/><br/>
			Thank you for your registration with #local.mc_siteInfo.orgName#.<br/><br/>

			Attached to this e-mail is your Certificate of Attendance for attending <b><i>#local.strEvent.qryEventMeta.eventContentTitle#</i></b> 
			<cfif DateFormat(local.strEvent.qryEventTimes_selected.endTime,"dddd, mmmm d, yyyy") EQ DateFormat(local.strEvent.qryEventTimes_selected.startTime,"dddd, mmmm d, yyyy")>
			on 
			#DateFormat(local.strEvent.qryEventTimes_selected.endTime,"dddd, mmmm d, yyyy")#.
			<cfelse>
			from #DateFormat(local.strEvent.qryEventTimes_selected.startTime,"dddd, mmmm d, yyyy")# to #DateFormat(local.strEvent.qryEventTimes_selected.endTime,"dddd, mmmm d, yyyy")#.
			</cfif>
			<br/><br/>
			<span id="customtext">
				<cfif len(trim(arguments.customText))>
					#trim(arguments.customText)#<br/><br/>
				</cfif>
			</span>

			<br/>
			#local.mc_siteinfo.orgName#
			<br/><br/>
			</cfoutput>
		</cfsavecontent>
		<cfset local.returnStruct.htmlemail = replace(replace(replace(local.returnStruct.htmlemail,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL")>
			
		<cfset local.returnStruct.emailTitle = "#local.mc_siteInfo.sitename# Certificate of Attendance">

		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="logCertGenerateTime" access="private" returntype="void" output="no">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="certificateID" type="numeric" required="yes">
		<cfargument name="timeMS" type="numeric" required="yes">

		<cfset var qryInsertLog = "">

		<cfquery name="qryInsertLog" datasource="#application.dsn.platformstatsMC.dsn#">
			INSERT INTO dbo.ev_certificateLog (certificateID, registrantID, timeMS)
			VALUES (<cfqueryparam value="#arguments.certificateID#" cfsqltype="CF_SQL_INTEGER">,
					<cfqueryparam value="#arguments.registrantID#" cfsqltype="CF_SQL_INTEGER">,
					<cfqueryparam value="#arguments.timeMS#" cfsqltype="CF_SQL_INTEGER">)
		</cfquery>
	</cffunction>

</cfcomponent>