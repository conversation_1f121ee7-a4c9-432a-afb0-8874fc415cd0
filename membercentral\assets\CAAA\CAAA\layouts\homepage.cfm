<cfoutput>
	<!doctype html>
	<html lang="en">
		<head>
			<cfinclude template="head.cfm">		
		</head>
		<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
		<body >
				<!-- wrapper start -->
				<div id="wrapper" class="wrapper">
					<!--Header Start-->
					<cfinclude template="header.cfm">
					<!--Header End--> 
					<!-- slider start -->
					<div class="heroBanner hidden">
						<!-- <div class="owl-carousel owl-theme"> -->
						<div class="container">
							
						</div>
					</div>
					<!-- slider End -->


					<div class="quick-links-sec zoneBWrap">
						<div class="container">
							<div class="row flex-row">
								<div class="span8 zoneBLeftWrap">
									<ul class="quick-links-wrapper">
										
									</ul>
								</div>
								<div class="span4 zoneBRighttWrap">
									
								</div>
							</div>
						</div>
					</div>

					<div class="important-dates-sec zoneDWrap hidden">
						<div class="container">
							<div class="row d-flex-wrap">
								<div class="span4 zoneDLeft">
								</div>
								<div class="span8">
									<div class="d-flex justify-between">
										<div class="left-icon-title zoneDTitle">
										</div>
									</div>
									<div class="date-card-wrap zoneDList">
										
									</div>
									<div class="btn-wrap zoneDLinks">
									</div>
								</div>
							</div>
						</div>
					</div>


					<div class="sec-pd70 news-sec zoneEWrap hidden">
						<div class="container">
							<div class="left-icon-title zoneELeft">
								<img src="" alt="">
								<div class="d-flex flex-1 zoneELeftTop">
									<h2 class="HeaderText bottom-line-0">Latest News</h2>
								</div>
							</div>
							<div class="row d-flex-wrap strBlogList">
								
							</div>
						</div>
					</div>


					<div class="sec-pd70 member-sec zoneFGWrap hidden">
						<div class="container">
							<div class="row d-flex-wrap">
								<div class="span6 zoneFWrap">
									<div class="member-card">
										<div class="member-card-slider zoneFCnt">
											
										</div>
									</div>
								</div>
								<div class="span6 zoneGWrap">
									<div class="member-card bg-gray">
										<div class="member-card-slider zoneGCnt">
										
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				  
					<!--Footer Start-->
					<cfinclude template="footer.cfm">				
					<!--Footer End-->                
					<cfinclude template="toolBar.cfm">
				</div>
				<!-- wrapper end -->	
				
	
				<!--slide-->
				<div id="zoneMainObj" class="hide">
					<cfif application.objCMS.getZoneItemCount(zone='Main',event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Main'], 1) AND event.getValue("mc_pageDefinition").pageZones['Main'][1].view EQ "echo">
						<cfset local.carouselContent = event.getValue("mc_pageDefinition").pageZones['Main']>
						<cfloop from="1" to="#arrayLen(local.carouselContent)#" index="local.thisItem" >
							<cfif lcase(trim(local.carouselContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.carouselContent[local.thisItem].data))>
								#trim(REReplace(REReplace(local.carouselContent[local.thisItem].data,"<p>","","All"),"</p>","","All"))#			
							</cfif>
						</cfloop>
					</cfif> 
				</div>

				<cfset local.zone = "B">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<cfset local.zoneBContent = event.getValue("mc_pageDefinition").pageZones[local.zone]>
					
					<cfloop from="1" to="#arrayLen(local.zoneBContent)#" index="local.thisItem" >
						<cfif FindNoCase("right", local.zoneBContent[local.thisItem].CONTENTATTRIBUTES.CONTENTTITLE) GT 0>
							<div id="zoneBRightObj" class="hide">
								#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][local.thisItem].data,"<p>",""),"</p>",""))#
							</div>
						<cfelseif FindNoCase("left", local.zoneBContent[local.thisItem].CONTENTATTRIBUTES.CONTENTTITLE) GT 0>
							<div id="zoneBLeftObj" class="hide">
								#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][local.thisItem].data,"<p>",""),"</p>",""))#
							</div>
						</cfif>
					</cfloop>
				</cfif> 

				<cfset local.zone = "B">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneBObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
				<cfset local.zone = "C">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneCObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
				<cfset local.zone = "D">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneDObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>

				<cfset local.zone = "E">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneEObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>

				<div id="zoneFObj" class="hide">
					<cfif application.objCMS.getZoneItemCount(zone='F',event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['F'], 1) AND event.getValue("mc_pageDefinition").pageZones['F'][1].view EQ "echo">
						<cfset local.carouselContent = event.getValue("mc_pageDefinition").pageZones['F']>
						<cfloop from="1" to="#arrayLen(local.carouselContent)#" index="local.thisItem" >
							<cfif lcase(trim(local.carouselContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.carouselContent[local.thisItem].data))>
								#trim(REReplace(REReplace(local.carouselContent[local.thisItem].data,"<p>","","All"),"</p>","","All"))#			
							</cfif>
						</cfloop>
					</cfif> 
				</div>

				<div id="zoneGObj" class="hide">
					<cfif application.objCMS.getZoneItemCount(zone='G',event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['G'], 1) AND event.getValue("mc_pageDefinition").pageZones['G'][1].view EQ "echo">
						<cfset local.carouselContent = event.getValue("mc_pageDefinition").pageZones['G']>
						<cfloop from="1" to="#arrayLen(local.carouselContent)#" index="local.thisItem" >
							<cfif lcase(trim(local.carouselContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.carouselContent[local.thisItem].data))>
								#trim(REReplace(REReplace(local.carouselContent[local.thisItem].data,"<p>","","All"),"</p>","","All"))#			
							</cfif>
						</cfloop>
					</cfif> 
				</div>
			</body>
		<cfelse>
			<body style="background:none!important;background:unset!important;padding:20px;" class="innerPage-content">
				#application.objCMS.renderZone(zone='Main',event=event)#  
			</body>		
		</cfif>
		<cfinclude template="foot.cfm">   
	</html>
	</cfoutput>