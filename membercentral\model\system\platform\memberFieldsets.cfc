<cfcomponent output="no">

	<cffunction name="getMemberFieldsXMLByUID" access="public" returntype="xml" output="no">
		<cfargument name="uid" type="string" required="yes">
		<cfargument name="usage" type="string" required="yes">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryFS" datasource="#application.dsn.membercentral.dsn#">
			select fieldSetID
			from dbo.ams_memberFieldSets
			where uid = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.uid#">
		</cfquery>

		<cfreturn getMemberFieldsXML(fieldsetID=val(local.qryFS.fieldsetID), usage=arguments.usage)>
	</cffunction>

	<cffunction name="getMemberFieldsXML" access="public" returntype="xml" output="no">
		<cfargument name="fieldsetID" type="numeric" required="yes">
		<cfargument name="usage" type="string" required="yes">
		
		<cfset var local = structNew()>
		
		<!--- ------------------------ --->
		<!--- Get the fieldset fields  --->
		<!--- ------------------------ --->
		<cfstoredproc procedure="ams_getMemberFields" datasource="#application.dsn.membercentral.dsn#">	
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldsetID#">
			<cfprocresult name="local.xmlFields" resultset="1">
		</cfstoredproc>
		<cfset local.xmlFields = XMLParse(local.xmlFields.fieldsXML)>
		
		<!--- -------------------------- --->
		<!--- Handle special processing  --->
		<!--- remove fields not supported by the desired usage --->
		<!--- -------------------------- --->
		<cfswitch expression="#arguments.usage#">

			<cfcase value="accountLocaterSearch">
				<!--- if prefix/select, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and @fieldCode='m_prefix']")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.qryOrg.orgID)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfloop query="local.qryOrgPrefixes">
						<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
						<cfset local.xmlOpt.xmlAttributes["valueID"] = 0>
						<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgPrefixes.prefix>
						<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
					</cfloop>
				</cfif>		
				
				<!--- if country, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and substring(@fieldCode,1,3)='ma_' and contains(@fieldCode,'_country')]")>
				<cfif arrayLen(local.node)>
					<cfset local.qryCountries = application.objCommon.getCountries()>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfloop query="local.qryCountries">
						<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
						<cfset local.xmlOpt.xmlAttributes["valueID"] = 0>
						<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryCountries.country>
						<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
					</cfloop>
				</cfif>

				<!--- if district data, add in real values as opts and set as select --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='mad_']")>
				<cfif arrayLen(local.node)>
					<cfloop array="#local.node#" index="local.thisNode">
						<cfset local.thisNode.xmlAttributes["displayTypeCode"] = "SELECT">
						<cfset local.thisDTID = GetToken(local.thisNode.xmlAttributes.fieldCode,3,'_')>
						<cfquery name="local.qryOrgAddressDataValues" datasource="#application.dsn.membercentral.dsn#">
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

							select distinct mdv.valueID, mdv.vendorValue
							from dbo.ams_memberDistrictValues as mdv
							inner join dbo.ams_memberAddressData as mad on mad.valueID = mdv.valueID
							where mdv.districtTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisDTID#">
							order by mdv.vendorValue;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						</cfquery>
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.thisNode.xmlChildren)>
						<cfloop query="local.qryOrgAddressDataValues">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgAddressDataValues.valueID>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgAddressDataValues.vendorValue>
							<cfset arrayAppend(local.thisNode.xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfloop>
				</cfif>		

				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,13)='acct_balance_'
									or substring(@fieldCode,1,4)='grp_' 
									or @fieldCode='m_recordtypeid' or @fieldCode='m_status' or @fieldCode='m_membertypeid'
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>
			
			<cfcase value="accountLocaterResults">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,13)='acct_balance_'
									or substring(@fieldCode,1,4)='grp_' 
									or @fieldCode='m_recordtypeid' or @fieldCode='m_status' or @fieldCode='m_membertypeid']"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>
			
			<cfcase value="accountLocaterNew">
				<!--- if prefix/select, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and @fieldCode='m_prefix']")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.qryOrg.orgID)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfloop query="local.qryOrgPrefixes">
						<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
						<cfset local.xmlOpt.xmlAttributes["valueID"] = 0>
						<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgPrefixes.prefix>
						<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
					</cfloop>
				</cfif>		

				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or (substring(@fieldCode,1,3)='ma_' and contains(@fieldCode,'_country'))
									or substring(@fieldCode,1,4)='mat_'
									or substring(@fieldCode,1,4)='met_'
									or substring(@fieldCode,1,4)='mpt_'
									or substring(@fieldCode,1,5)='madt_'
									or @isReadOnly='1' 
									or substring(@fieldCode,1,4)='mpl_' 
									or substring(@fieldCode,1,13)='acct_balance_'
									or substring(@fieldCode,1,4)='grp_' 
									or @fieldCode='m_recordtypeid' 
									or @fieldCode='m_status' 
									or @fieldCode='m_membertypeid'
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="memberDirectorySearch">
				<!--- if country, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and substring(@fieldCode,1,3)='ma_' and contains(@fieldCode,'_country')]")>
				<cfif arrayLen(local.node)>
					<cfset local.qryCountries = application.objCommon.getCountries()>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfloop query="local.qryCountries">
						<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
						<cfset local.xmlOpt.xmlAttributes["valueID"] = 0>
						<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryCountries.country>
						<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
					</cfloop>
				</cfif>

				<!--- if district data, add in real values as opts and set as select --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='mad_']")>
				<cfif arrayLen(local.node)>
					<cfloop array="#local.node#" index="local.thisNode">
						<cfset local.thisNode.xmlAttributes["displayTypeCode"] = "SELECT">
						<cfset local.thisDTID = GetToken(local.thisNode.xmlAttributes.fieldCode,3,'_')>
						<cfquery name="local.qryOrgAddressDataValues" datasource="#application.dsn.membercentral.dsn#">
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

							select distinct mdv.valueID, mdv.vendorValue
							from dbo.ams_memberDistrictValues as mdv
							inner join dbo.ams_memberAddressData as mad on mad.valueID = mdv.valueID
							where mdv.districtTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisDTID#">
							order by mdv.vendorValue;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						</cfquery>
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.thisNode.xmlChildren)>
						<cfloop query="local.qryOrgAddressDataValues">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgAddressDataValues.valueID>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgAddressDataValues.vendorValue>
							<cfset arrayAppend(local.thisNode.xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfloop>
				</cfif>		

				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,13)='acct_balance_'
									or substring(@fieldCode,1,4)='grp_' 
									or substring(@fieldCode,1,4)='mpl_' 
									or @fieldCode='m_recordtypeid' or @fieldCode='m_status' or @fieldCode='m_membertypeid'
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="memberDirectorySearchBucket">
				<!--- only first, last, and company are supported until search schema can be changed --->
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,13)='acct_balance_'
									or substring(@fieldCode,1,4)='grp_' 
									or substring(@fieldCode,1,4)='mpl_' 
									or ( not(@fieldCode='m_firstname') and not(@fieldCode='m_lastname') and not(@fieldCode='m_company') )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="memberDirectoryResults,memberDirectoryDetails,memberDirectoryVCard">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@fieldCode='m_recordtypeid' or @fieldCode='m_status' or @fieldCode='m_membertypeid'
									or substring(@fieldCode,1,13)='acct_balance_' ]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="updateMember">
				<!--- only show md_, mpl_, mad_, grp_ fields here since the others are already on this form. --->
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[substring(@fieldCode,1,4)='mat_'
									or substring(@fieldCode,1,4)='met_'
									or substring(@fieldCode,1,4)='mpt_'
									or substring(@fieldCode,1,5)='madt_'
									or ( not(substring(@fieldCode,1,3)='md_') and not(substring(@fieldCode,1,4)='mpl_') and not(substring(@fieldCode,1,4)='mad_') and not (substring(@fieldCode,1,4)='grp_') )
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
				
				<!--- mpl, grps is allowed on this form, but it needs to be readonly. --->
				<cfset local.nodes = XMLSearch(local.xmlFields,"/fields/mf[(substring(@fieldCode,1,4)='mpl_' or substring(@fieldCode,1,4)='grp_') and not(@isReadOnly='1')]")>
				<cfloop array="#local.nodes#" index="local.thisnode">
					<cfset local.thisnode.xmlAttributes.isReadOnly = 1>
				</cfloop>

				<!--- grps is allowed on this form, but it needs to be TEXTBOX. --->
				<cfset local.nodes = XMLSearch(local.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='grp_' and not(@displayTypeCode='TEXTBOX')]")>
				<cfloop array="#local.nodes#" index="local.thisnode">
					<cfset local.thisnode.xmlAttributes.displayTypeCode = "TEXTBOX">
				</cfloop>

			</cfcase>
			
			<cfcase value="memberAdminSearch">
				<!--- if recordtype, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_recordtypeid')]")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgRecordTypes = application.objOrgInfo.getOrgRecordTypes(local.qryOrg.orgID)>
					<cfif local.qryOrgRecordTypes.recordcount gt 1>
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
						<cfloop query="local.qryOrgRecordTypes">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgRecordTypes.recordTypeID>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgRecordTypes.recordTypeName>
							<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfif>
				</cfif>
				
				<!--- if account status, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_status')]")>
				<cfif arrayLen(local.node)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "A">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Active">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>					
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "I">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Inactive">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>	
				</cfif>
				
				<!--- if account type, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_membertypeid')]")>
				<cfif arrayLen(local.node)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "1">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Guest">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>					
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "2">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "User">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>	
				</cfif>

				<!--- if prefix/select, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and @fieldCode='m_prefix']")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.qryOrg.orgID)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfloop query="local.qryOrgPrefixes">
						<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
						<cfset local.xmlOpt.xmlAttributes["valueID"] = 0>
						<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgPrefixes.prefix>
						<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
					</cfloop>
				</cfif>

				<!--- if country, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,4)='mat_') and contains(@fieldCode,'_country')]")>
				<cfloop array="#local.node#" index="local.thisNode">
					<cfset local.qryCountries = application.objCommon.getCountries()>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.thisNode.xmlChildren)>
					<cfloop query="local.qryCountries">
						<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
						<cfset local.xmlOpt.xmlAttributes["valueID"] = 0>
						<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryCountries.country>
						<cfset arrayAppend(local.thisNode.xmlChildren,local.xmlOpt)>
					</cfloop>
				</cfloop>

				<!--- if district data, add in real values as opts and set as select --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='mad_' or substring(@fieldCode,1,5)='madt_']")>
				<cfif arrayLen(local.node)>
					<cfloop array="#local.node#" index="local.thisNode">
						<cfset local.thisNode.xmlAttributes["displayTypeCode"] = "SELECT">
						<cfset local.thisDTID = GetToken(local.thisNode.xmlAttributes.fieldCode,3,'_')>
						<cfquery name="local.qryOrgAddressDataValues" datasource="#application.dsn.membercentral.dsn#">
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

							select distinct mdv.valueID, mdv.vendorValue
							from dbo.ams_memberDistrictValues as mdv
							inner join dbo.ams_memberAddressData as mad on mad.valueID = mdv.valueID
							where mdv.districtTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisDTID#">
							order by mdv.vendorValue;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						</cfquery>
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.thisNode.xmlChildren)>
						<cfloop query="local.qryOrgAddressDataValues">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgAddressDataValues.valueID>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgAddressDataValues.vendorValue>
							<cfset arrayAppend(local.thisNode.xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfloop>
				</cfif>

				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,13)='acct_balance_'
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>
			
			<cfcase value="memberAdminResultsWithContentObjects">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,4)='grp_']"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>
			
			<cfcase value="memberAdminResults">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,4)='grp_']"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="memberAdminNew">
				<!--- only show md_ fields here since the others are already on this form. --->
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@isReadOnly='1'
									or not(substring(@fieldCode,1,3)='md_')
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>
			
			<cfcase value="memberAdminMain">
				<!--- only show md_, me_, mw_, acct_balance_ here --->
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or ( not(substring(@fieldCode,1,3)='md_') and not(substring(@fieldCode,1,3)='me_') and not(substring(@fieldCode,1,4)='met_') and not(substring(@fieldCode,1,3)='mw_') and not(substring(@fieldCode,1,13)='acct_balance_') )
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>
			
			<cfcase value="adminLinkedRecordResults">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,4)='grp_']"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>			

			<cfcase value="memberAdminCustom">
				<!--- only show md_ fields here --->
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[not(substring(@fieldCode,1,3)='md_')
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 )]"
						)>											
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="reportResults">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='DOCUMENTOBJ']"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="contributorsDownload,eventRegDownload,subscriberDownload,invoiceDownload,taskDownload,solicitorsDownload">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='DOCUMENTOBJ']"
						)>						
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="groupMemDownload">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='DOCUMENTOBJ']"
						)>						
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="CustomApp">
				<!--- only show md_ fields here that are not read only. --->
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or @isReadOnly='1'
									or not(substring(@fieldCode,1,3)='md_')
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>
			
			<cfcase value="CustomPage">
				<!--- if prefix/select, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and @fieldCode='m_prefix']")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.qryOrg.orgID)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfloop query="local.qryOrgPrefixes">
						<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
						<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgPrefixes.prefix>
						<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgPrefixes.prefix>
						<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
					</cfloop>
				</cfif>		

				<!--- if mat_xxx_addresstype, display as dropdown of address types --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='mat_' and contains(@fieldCode,'_addresstype')]")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=local.qryOrg.orgID)>
					<cfloop array="#local.node#" index="local.thisNode">
						<cfset local.thisNode.xmlAttributes.displayTypeCode = "SELECT">
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.thisNode.xmlChildren)>
						<cfloop query="local.qryOrgAddressTypes">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgAddressTypes.addressTypeID>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgAddressTypes.addressType>
							<cfset arrayAppend(local.thisNode.xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfloop>
				</cfif>		

				<!--- if met_xxx_emailType, display as dropdown of email types --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='met_' and contains(@fieldCode,'_emailType')]")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID=local.qryOrg.orgID)>
					<cfloop array="#local.node#" index="local.thisNode">
						<cfset local.thisNode.xmlAttributes.displayTypeCode = "SELECT">
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.thisNode.xmlChildren)>
						<cfloop query="local.qryOrgEmailTypes">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgEmailTypes.emailTypeID>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgEmailTypes.emailType>
							<cfset arrayAppend(local.thisNode.xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfloop>
				</cfif>		

				<!--- if mpl_xxx_status, display as dropdown of professional license statuses --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='mpl_' and contains(@fieldCode,'_status')]")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryProfessionalLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=local.qryOrg.orgID)>
					<cfloop array="#local.node#" index="local.thisNode">
						<cfset local.thisNode.xmlAttributes.displayTypeCode = "SELECT">
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.thisNode.xmlChildren)>
						<cfloop query="local.qryProfessionalLicenseStatuses">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryProfessionalLicenseStatuses.StatusName>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryProfessionalLicenseStatuses.StatusName>
							<cfset arrayAppend(local.thisNode.xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfloop>
				</cfif>	
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@isReadOnly='1'
									or substring(@fieldCode,1,13)='acct_balance_'
									or substring(@fieldCode,1,17)='ml_datelastlogin_'
									or @fieldCode='m_membernumber' or @fieldCode='m_recordtypeid' or @fieldCode='m_status' or @fieldCode='m_membertypeid' or @fieldCode='m_earliestdatecreated' or @fieldCode='m_datelastupdated'
									or (substring(@fieldCode,1,4)='met_' and not(contains(@fieldCode,'_emailType')))
									or (substring(@fieldCode,1,4)='mat_' and not(contains(@fieldCode,'_addresstype')))
									or substring(@fieldCode,1,4)='mpt_'
									or substring(@fieldCode,1,5)='madt_'
									or substring(@fieldCode,1,4)='mad_'
									or substring(@fieldCode,1,4)='grp_'
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="memberReferralSearch">
				<!--- if recordtype, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_recordtypeid')]")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgRecordTypes = application.objOrgInfo.getOrgRecordTypes(local.qryOrg.orgID)>
					<cfif local.qryOrgRecordTypes.recordcount gt 1>
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
						<cfloop query="local.qryOrgRecordTypes">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgRecordTypes.recordTypeID>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgRecordTypes.recordTypeName>
							<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfif>
				</cfif>		
				
				<!--- if account status, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_status')]")>
				<cfif arrayLen(local.node)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "A">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Acive">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>					
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "I">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Inactive">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>	
				</cfif>	
				
				<!--- if account type, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_membertypeid')]")>
				<cfif arrayLen(local.node)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "1">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Guest">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>					
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "2">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "User">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>	
				</cfif>				

				<cfset local.arrMF = XmlSearch(local.xmlFields,
					"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
								or substring(@fieldCode,1,13)='acct_balance_'
								or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
					)>				
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>	

			<cfcase value="memberReferralResult,memberReferralLawyersResult">
				<!--- if recordtype, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_recordtypeid')]")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgRecordTypes = application.objOrgInfo.getOrgRecordTypes(local.qryOrg.orgID)>
					<cfif local.qryOrgRecordTypes.recordcount gt 1>
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
						<cfloop query="local.qryOrgRecordTypes">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgRecordTypes.recordTypeID>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgRecordTypes.recordTypeName>
							<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfif>
				</cfif>		
				
				<!--- if account status, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_status')]")>
				<cfif arrayLen(local.node)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "A">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Acive">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>					
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "I">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Inactive">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>	
				</cfif>	
				
				<!--- if account type, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_membertypeid')]")>
				<cfif arrayLen(local.node)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "1">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Guest">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>					
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "2">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "User">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>	
				</cfif>

				<cfif arguments.usage EQ 'memberReferralLawyersResult'>
					<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='DOCUMENTOBJ'
									or @fieldCode='m_membernumber'
									or substring(@fieldCode,1,13)='acct_balance_'
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
						)>
				<cfelse>
					<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,13)='acct_balance_'
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
						)>
				</cfif>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>			
			
			<cfcase value="referralresult,clientfeeresult,clientemail">
				<!--- if recordtype, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_recordtypeid')]")>
				<cfif arrayLen(local.node)>
					<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
						
						select s.orgID
						from dbo.sites as s
						inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
						where mf.fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldsetID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgRecordTypes = application.objOrgInfo.getOrgRecordTypes(local.qryOrg.orgID)>
					<cfif local.qryOrgRecordTypes.recordcount gt 1>
						<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
						<cfloop query="local.qryOrgRecordTypes">
							<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
							<cfset local.xmlOpt.xmlAttributes["valueID"] = local.qryOrgRecordTypes.recordTypeID>
							<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgRecordTypes.recordTypeName>
							<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
						</cfloop>
					</cfif>
				</cfif>		
				
				<!--- if account status, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_status')]")>
				<cfif arrayLen(local.node)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "A">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Acive">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>					
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "I">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Inactive">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>	
				</cfif>	
				
				<!--- if account type, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_membertypeid')]")>
				<cfif arrayLen(local.node)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "1">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "Guest">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>					
					<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
					<cfset local.xmlOpt.xmlAttributes["valueID"] = "2">
					<cfset local.xmlOpt.xmlAttributes["columnValueString"] = "User">
					<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>	
				</cfif>				

				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,13)='acct_balance_'
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>

			<cfcase value="regIdNewacct,SWRegNewAcct" delimiters=",">
				<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select s.orgID
					from dbo.sites as s
					inner join dbo.ams_memberFieldSets as mf on mf.siteID = s.siteID
					where mf.fieldsetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldsetID#">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<!--- if prefix/select, add in as opts --->
				<cfset local.node = XMLSearch(local.xmlFields,"/fields/mf[@displayTypeCode='SELECT' and @fieldCode='m_prefix']")>
				<cfif arrayLen(local.node)>
					<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.qryOrg.orgID)>
					<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.node[1].xmlChildren)>
					<cfloop query="local.qryOrgPrefixes">
						<cfset local.xmlOpt = XMLElemNew(local.xmlFields,'opt')>
						<cfset local.xmlOpt.xmlAttributes["valueID"] = 0>
						<cfset local.xmlOpt.xmlAttributes["columnValueString"] = local.qryOrgPrefixes.prefix>
						<cfset arrayAppend(local.node[1].xmlChildren,local.xmlOpt)>
					</cfloop>
				</cfif>

				<cfquery name="local.qryDefaultEmailTypeField" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @orgID int, @orgCode varchar(10);
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrg.orgID#">;
					SELECT @orgCode = orgCode FROM dbo.organizations WHERE orgID = @orgID;

					SELECT 'vw_' + @orgCode + '_me' as dbObject, 'vwme' as dbObjectAlias, emailType as dbField, 'me_' + cast(emailTypeID as varchar(10)) + '_email' as fieldCode, 
						emailType as fieldLabel
					FROM dbo.ams_memberEmailTypes
					WHERE orgID = @orgID
					AND emailTypeOrder = 1;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset local.arrMF = XmlSearch(local.xmlFields,
					"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
					or (substring(@fieldCode,1,3)='ma_' and contains(@fieldCode,'_country'))
					or substring(@fieldCode,1,4)='mat_'
					or substring(@fieldCode,1,4)='met_'
					or substring(@fieldCode,1,4)='mpt_'
					or substring(@fieldCode,1,5)='madt_'
					or @isReadOnly='1' 
					or substring(@fieldCode,1,4)='mpl_' 
					or substring(@fieldCode,1,13)='acct_balance_'
					or substring(@fieldCode,1,4)='grp_' 
					or @fieldCode='m_recordtypeid' 
					or @fieldCode='m_status' 
					or @fieldCode='m_membertypeid'
					or @fieldCode='m_firstname'
					or @fieldCode='m_lastname'
					or @fieldCode='#local.qryDefaultEmailTypeField.fieldCode#'
					or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
				)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>

				<cfxml variable="local.firstNameFieldXML">
					<mf fieldID="0" dbObject="ams_members" dbObjectAlias="m" dbField="firstname" fieldCode="m_firstname" mdColumnID="0" fieldLabel="First Name" fieldDescription="" isRequired="1" isGrouped="0" displayTypeCode="TEXTBOX" dataTypeCode="STRING" isReadOnly="0" allowNull="0" allowMultiple="0" />
				</cfxml>
				<cfxml variable="local.lastNameFieldXML">
					<mf fieldID="0" dbObject="ams_members" dbObjectAlias="m" dbField="lastname" fieldCode="m_lastname" mdColumnID="0" fieldLabel="Last Name" fieldDescription="" isRequired="1" isGrouped="0" displayTypeCode="TEXTBOX" dataTypeCode="STRING" isReadOnly="0" allowNull="0" allowMultiple="0" />
				</cfxml>
				<cfxml variable="local.defaultEmailFieldXML">
					<cfoutput>
					<mf fieldID="0" dbObject="#local.qryDefaultEmailTypeField.dbObject#" dbObjectAlias="#local.qryDefaultEmailTypeField.dbObjectAlias#" dbField="#local.qryDefaultEmailTypeField.dbField#" fieldCode="#local.qryDefaultEmailTypeField.fieldCode#" mdColumnID="0" fieldLabel="#local.qryDefaultEmailTypeField.fieldLabel#" fieldDescription="" isRequired="1" isGrouped="0" displayTypeCode="TEXTBOX" dataTypeCode="STRING" isReadOnly="0" allowNull="0" allowMultiple="0" />
					</cfoutput>
				</cfxml>

				<!--- FirstName --->
				<cfset ArrayInsertAt(local.xmlFields.XmlRoot.XmlChildren, 1, XmlElemNew(local.xmlFields, "mf"))>
				<cfset local.xmlFields.XmlRoot.XmlChildren[1].XmlAttributes = duplicate(local.firstNameFieldXML.XmlRoot.XmlAttributes)>
				<cfset ArrayInsertAt(local.xmlFields.XmlRoot.XmlChildren[1].XmlChildren, 1, XmlElemNew(local.xmlFields, "opt"))>

				<!--- LastName --->
				<cfset ArrayInsertAt(local.xmlFields.XmlRoot.XmlChildren, 2, XmlElemNew(local.xmlFields, "mf"))>
				<cfset local.xmlFields.XmlRoot.XmlChildren[2].XmlAttributes = duplicate(local.lastNameFieldXML.XmlRoot.XmlAttributes)>
				<cfset ArrayInsertAt(local.xmlFields.XmlRoot.XmlChildren[2].XmlChildren, 1, XmlElemNew(local.xmlFields, "opt"))>

				<!--- Email --->
				<cfset ArrayInsertAt(local.xmlFields.XmlRoot.XmlChildren, 3, XmlElemNew(local.xmlFields, "mf"))>
				<cfset local.xmlFields.XmlRoot.XmlChildren[3].XmlAttributes = duplicate(local.defaultEmailFieldXML.XmlRoot.XmlAttributes)>
				<cfset ArrayInsertAt(local.xmlFields.XmlRoot.XmlChildren[3].XmlChildren, 1, XmlElemNew(local.xmlFields, "opt"))>
			</cfcase>
			
			<cfcase value="panelMemberChecklist">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or @fieldCode='m_recordtypeid' or @fieldCode='m_status' or @fieldCode='m_membertypeid'
									or substring(@fieldCode,1,13)='acct_balance_'
									or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
						)>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>									
			
			<cfcase value="ipReport">
				<cfset local.arrMF = XmlSearch(local.xmlFields,
						"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ'
									or substring(@fieldCode,1,4)='grp_']"
						)>						
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfcase>									

			<!--- default: no fields are supported. Forces registration in this CFC --->
			<cfdefaultcase>
				<cfset local.arrMF = XmlSearch(local.xmlFields,"/fields/mf")>
				<cfset application.objCommon.XmlDeleteNodes(local.xmlFields, local.arrMF)>
			</cfdefaultcase>

		</cfswitch>
		
		<cfreturn local.xmlFields>
	</cffunction>

	<cffunction name="getSQLSearchConditionsFromFieldSetFSMatch" access="private" output="false" returntype="struct">
		<cfargument name="fs_match" type="string" required="true">
		<cfargument name="field" type="string" required="true">
		<cfargument name="paramNum" type="numeric" required="true">
		<cfargument name="paramValue" type="string" required="true">

		<cfscript>
		var strReturn = {};

		switch (arguments.fs_match) {
			case "c":
				strReturn.filterClauseString = "and #arguments.field# like :val#arguments.paramNum# ESCAPE('\')";
				strReturn.sqlParam = "%#replaceNoCase(arguments.paramValue,'_','\_','ALL')#%";
				break;
			case "e":
				strReturn.filterClauseString = "and #arguments.field# = :val#arguments.paramNum#";
				strReturn.sqlParam = arguments.paramValue;
				break;
			default:
				strReturn.filterClauseString = "and #arguments.field# like :val#arguments.paramNum# ESCAPE('\')";
				strReturn.sqlParam = "#replaceNoCase(arguments.paramValue,'_','\_','ALL')#%";
				break;
		}

		return strReturn;
		</cfscript>
	</cffunction>

	<cffunction name="getSQLSearchConditionsFromFieldSet" access="public" output="false" returntype="struct">
		<cfargument name="fieldsetID" type="numeric" required="true">
		<cfargument name="fieldsetUsage" type="string" required="true">
		<cfargument name="Event" type="any" required="true">

		<cfscript>
		var paramNum = 0;
		var criteriaMet = 1;
		var stFilterPrep = "";
		var stFilterPost = "";
		var stFilter = "";
		var arrFilterClauses = [];
		var sqlParams = {};
		var strHolding = {};
		var rc = arguments.event.getCollection();
		var xmlSearchFields = getMemberFieldsXML(fieldsetid=arguments.fieldsetID, usage=arguments.fieldsetUsage);

		// determine if criteria met
		xmlSearchFields.xmlRoot.xmlChildren.each(function(thisField) {
			if (arguments.thisField.xmlattributes.isrequired eq "true" and (NOT structKeyExists(rc,arguments.thisField.xmlattributes.fieldcode) or not len(trim(rc[arguments.thisField.xmlattributes.fieldcode])))) {
				criteriaMet = 0;
				break;
			}
		}, true);		

		if (criteriaMet == 1) {

			// only consider the fields submitted with values.
			// acct fields are not supported in searching so remove those as well.
			var arrSearchFields = xmlSearchFields.xmlRoot.xmlChildren.filter(function(thisField) {
				return structKeyExists(rc,arguments.thisField.xmlattributes.fieldcode) and len(trim(rc[arguments.thisField.xmlattributes.fieldcode])) and arguments.thisField.xmlattributes.dbObjectAlias neq "acct";
			}, true);

			for (var thisField in arrSearchFields) {
				paramNum++;
			
				/* ***************************** */
				/* if searching by member data   */
				/* ***************************** */
				if (thisField.xmlattributes.dbObjectAlias eq "m") {
					switch (thisfield.xmlAttributes.dataTypeCode) {
						case "DATE":
							arrFilterClauses.prepend("and cast(m.#thisfield.xmlattributes.dbfield# as date) = :val#paramNum#");
							structInsert(sqlParams,'val#paramNum#',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_DATE" });
							break;
						case "STRING":
							switch (thisfield.xmlAttributes.displayTypeCode) {
								case "SELECT":
									arrFilterClauses.prepend("and m.#thisfield.xmlattributes.dbfield# = :val#paramNum#");
									structInsert(sqlParams,'val#paramNum#',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_VARCHAR" });
									break;
								case "TEXTBOX":
									strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="m.#thisfield.xmlattributes.dbfield#", 
										paramNum=paramNum, paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
									arrFilterClauses.prepend(strHolding.fsmatch.filterClauseString);
									structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });
									break;
							}
							break;
					}

				/* ***************************** */
				/* if searching by member login  */
				/* ***************************** */
				} else if (thisField.xmlattributes.dbObjectAlias eq "ml") {

					if (thisField.xmlattributes.dbField eq "ml_datelastlogin_0") {
						arrFilterClauses.prepend("and (select cast(max(dateLastLogin) as date) from dbo.ams_memberNetworkProfiles where memberID = m.memberID and status = 'A') = :val#paramNum#");
						structInsert(sqlParams,'val#paramNum#',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_DATE" });
					} elseif (left(thisField.xmlattributes.dbField,17) eq "ml_datelastlogin_") {
						arrFilterClauses.prepend("and (select cast(max(dateLastLogin) as date) from dbo.ams_memberNetworkProfiles where memberID = m.memberID and siteID = :val#paramNum#A and status = 'A') = :val#paramNum#");
						structInsert(sqlParams,'val#paramNum#A',{ value="#val(GetToken(thisField.xmlattributes.dbField,3,'_'))#", cfsqltype="CF_SQL_INTEGER" });
						structInsert(sqlParams,'val#paramNum#',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_DATE" });
					}

				/* ************************ */
				/* if searching by address  */
				/* ************************ */
				} else if (thisfield.xmlattributes.dbObjectAlias eq "vwma") {
					strHolding.typepre = getToken(thisfield.xmlattributes.fieldCode,1,"_");
					strHolding.typeID = val(getToken(thisfield.xmlattributes.fieldCode,2,"_"));
					strHolding.type = getToken(thisfield.xmlattributes.fieldCode,3,"_");

					if (strHolding.type eq "addresstype") {
						strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="matt.addressType", paramNum=paramNum,
							paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
						structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

						arrFilterClauses.append("intersect
							select matagt.memberID
							from dbo.ams_memberAddressTags as matagt
							inner join dbo.ams_memberAddresses as mat on mat.orgID = @orgID 
								and mat.memberID = matagt.memberID 
								and mat.addressTypeID = matagt.addressTypeID 
							inner join dbo.ams_memberAddressTypes as matt on matt.orgID = @orgID
								and matt.addressTypeID = mat.addressTypeID 
								#strHolding.fsmatch.filterClauseString#
							where matagt.orgID = @orgID
							and matagt.addressTagTypeID = #strHolding.typeID#");

					} else if (listFindNoCase("attn,address1,address2,address3,city,county",strHolding.type)) {
						strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="ma.#strHolding.type#", paramNum=paramNum,
							paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
						structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

						if (strHolding.typepre eq "mat")
							arrFilterClauses.append("intersect
								select ma.memberID
								from dbo.ams_memberAddressTags as tag
								inner join dbo.ams_memberAddresses as ma on ma.orgID = @orgID
									and ma.addressTypeID = tag.addressTypeID
									and ma.memberID = tag.memberID
									#strHolding.fsmatch.filterClauseString#
								where tag.orgID = @orgID
								and tag.addressTagTypeID = #strHolding.typeID#");
						else
							arrFilterClauses.append("intersect
								select ma.memberID
								from dbo.ams_memberAddresses as ma
								where ma.orgID = @orgID
								#strHolding.fsmatch.filterClauseString#
								and ma.addressTypeID = #strHolding.typeID#");

					} else if (strHolding.type eq "postalcode") {
						savecontent variable="stFilterPrep" {
							writeOutput("#stFilterPrep#
							IF OBJECT_ID('tempdb..##tmpMFS_zip_#thisfield.xmlattributes.fieldCode#') IS NOT NULL 
								DROP TABLE ##tmpMFS_zip_#thisfield.xmlattributes.fieldCode#;
							CREATE TABLE ##tmpMFS_zip_#thisfield.xmlattributes.fieldCode# (zipcode varchar(50));

							insert into ##tmpMFS_zip_#thisfield.xmlattributes.fieldCode# (zipcode)
							select zipcode 
							from dbo.fn_PythagoreanZipCodeResults(:val#paramNum#Z,:val#paramNum#ZR,:val#paramNum#ZC);");
						}
						savecontent variable="stFilterPost" {
							writeOutput("#stFilterPost#
							IF OBJECT_ID('tempdb..##tmpMFS_zip_#thisfield.xmlattributes.fieldCode#') IS NOT NULL 
								DROP TABLE ##tmpMFS_zip_#thisfield.xmlattributes.fieldCode#;");
						}
						structInsert(sqlParams,'val#paramNum#Z',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_VARCHAR" });
						structInsert(sqlParams,'val#paramNum#ZR',{ value="#arguments.event.getTrimValue('#thisField.xmlattributes.fieldcode#_radius',5)#", cfsqltype="CF_SQL_INTEGER" });
						structInsert(sqlParams,'val#paramNum#ZC',{ value="#arguments.event.getValue('mc_siteinfo.defaultCountryID')#", cfsqltype="CF_SQL_INTEGER" });

						if (strHolding.typepre eq "mat")
							arrFilterClauses.append("intersect
								select tag.memberID
								from dbo.ams_memberAddressTags as tag
								inner join dbo.ams_memberAddresses as ma on ma.orgID = @orgID
									and ma.addressTypeID = tag.addressTypeID
									and ma.memberID = tag.memberID
								where tag.orgID = @orgID
								and tag.addressTagTypeID = #strHolding.typeID#
								and exists (select zipcode from ##tmpMFS_zip_#thisfield.xmlattributes.fieldCode# where zipcode = ma.postalCodeForSearch)");
						else
							arrFilterClauses.append("intersect
								select ma.memberID
								from dbo.ams_memberAddresses as ma
								where ma.orgID = @orgID
								and ma.addressTypeID = #strHolding.typeID#
								and exists (select zipcode from ##tmpMFS_zip_#thisfield.xmlattributes.fieldCode# where zipcode = ma.postalCodeForSearch)");

					} else if (listFindNoCase("mp,mpt",strHolding.typepre)) {
						strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="mp.phone", paramNum=paramNum,
							paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
						structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

						if (strHolding.typepre eq "mpt")
							arrFilterClauses.append("intersect
								select tag.memberID
								from dbo.ams_memberAddressTags as tag
								inner join dbo.ams_memberAddresses as ma on ma.orgID = @orgID
									and ma.addressTypeID = tag.addressTypeID
									and ma.memberID = tag.memberID
								inner join dbo.ams_memberPhones as mp on mp.orgID = @orgID 
									and mp.memberID = ma.memberID
									and mp.addressID = ma.addressID 
									and mp.phoneTypeID = #strHolding.type#
									#strHolding.fsmatch.filterClauseString#
								where tag.orgID = @orgID
								and tag.addressTagTypeID = #strHolding.typeID#");
						else
							arrFilterClauses.append("intersect
								select ma.memberID
								from dbo.ams_memberAddresses as ma
								inner join dbo.ams_memberPhones as mp on mp.orgID = @orgID 
									and mp.memberID = ma.memberID
									and mp.addressID = ma.addressID 
									and mp.phoneTypeID = #strHolding.type#
									#strHolding.fsmatch.filterClauseString#
								where ma.orgID = @orgID
								and ma.addressTypeID = #strHolding.typeID#");

					} else if (listFindNoCase("mad,madt",strHolding.typepre)) {
						strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="mdv.vendorValue", paramNum=paramNum,
							paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
						structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

						if (strHolding.typepre eq "madt")
							arrFilterClauses.append("intersect
								select tag.memberID
								from dbo.ams_memberAddressTags as tag
								inner join dbo.ams_memberAddresses as ma on ma.orgID = @orgID
									and ma.addressTypeID = tag.addressTypeID
									and ma.memberID = tag.memberID
								inner join dbo.ams_memberAddressData as mad on mad.orgID = @orgID 
									and mad.addressID = ma.addressID 
								inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
									and mdv.districtTypeID = #strHolding.type#
									and (mdv.valid_from is null or mdv.valid_from <= getdate()) and (mdv.valid_to is null or mdv.valid_to >= getdate())
									#strHolding.fsmatch.filterClauseString#
								where tag.orgID = @orgID
								and tag.addressTagTypeID = #strHolding.typeID#");
						else
							arrFilterClauses.append("intersect
								select ma.memberID
								from dbo.ams_memberAddresses as ma
								inner join dbo.ams_memberAddressData as mad on mad.orgID = @orgID 
									and mad.addressID = ma.addressID 
								inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
									and mdv.districtTypeID = #strHolding.type#
									and (mdv.valid_from is null or mdv.valid_from <= getdate()) and (mdv.valid_to is null or mdv.valid_to >= getdate())
									#strHolding.fsmatch.filterClauseString#
								where ma.orgID = @orgID
								and ma.addressTypeID = #strHolding.typeID#");

					} else if (strHolding.type eq "stateprov") {
						if (strHolding.typepre eq "mat")
							arrFilterClauses.append("intersect
								select ma.memberID
								from dbo.ams_memberAddressTags as tag
								inner join dbo.ams_memberAddresses as ma on ma.orgID = @orgID
									and ma.addressTypeID = tag.addressTypeID
									and ma.memberID = tag.memberID
								inner join dbo.ams_states as st on st.stateid = ma.stateid
									and st.code = :val#paramNum#
								where tag.orgID = @orgID
								and tag.addressTagTypeID = #strHolding.typeID#");
						else
							arrFilterClauses.append("intersect
								select ma.memberID
								from dbo.ams_memberAddresses as ma
								inner join dbo.ams_states as st on st.stateid = ma.stateid
									and st.code = :val#paramNum#
								where ma.orgID = @orgID
								and ma.addressTypeID = #strHolding.typeID#");
						structInsert(sqlParams,'val#paramNum#',{ value=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode), cfsqltype="CF_SQL_VARCHAR" });

					} else if (strHolding.type eq "country") {
						if (strHolding.typepre eq "mat")
							arrFilterClauses.append("intersect
								select ma.memberID
								from dbo.ams_memberAddressTags as tag
								inner join dbo.ams_memberAddresses as ma on ma.orgID = @orgID
									and ma.addressTypeID = tag.addressTypeID
									and ma.memberID = tag.memberID
								inner join dbo.ams_countries as c on c.countryid = ma.countryid
									and c.country = :val#paramNum#
								where tag.orgID = @orgID
								and tag.addressTagTypeID = #strHolding.typeID#");
						else
							arrFilterClauses.append("intersect
								select ma.memberID
								from dbo.ams_memberAddresses as ma
								inner join dbo.ams_countries as c on c.countryid = ma.countryid
									and c.country = :val#paramNum#
								where ma.orgID = @orgID
								and ma.addressTypeID = #strHolding.typeID#");
						structInsert(sqlParams,'val#paramNum#',{ value=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode), cfsqltype="CF_SQL_VARCHAR" });
					}

				// if searching by custom fields
				} else if (thisfield.xmlattributes.dbObjectAlias eq "vwmd") {
					structInsert(sqlParams,'val#paramNum#A',{ value="#val(getToken(thisfield.xmlattributes.fieldCode,2,"_"))#", cfsqltype="CF_SQL_INTEGER" });

					if (listFindNoCase("RADIO,SELECT,CHECKBOX",thisfield.xmlAttributes.displayTypeCode)) {
						var valIDs = arguments.event.getTrimValue(thisField.xmlattributes.fieldcode);
						local.validValIDs = listFilter(valIDs, function(valueID){ return isNumeric(arguments.valueID); });

						if (listLen(local.validValIDs)) {
							arrFilterClauses.append("intersect
								select md.memberID
								from dbo.ams_memberData as md
								inner join dbo.ams_members as m on m.orgID = @orgID
									and m.memberID = md.memberID 
									and m.memberID = m.activeMemberID
								inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
								inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
									and mdc.columnID = :val#paramNum#A
								where mdcv.valueID IN (:val#paramNum#)");

							structInsert(sqlParams,'val#paramNum#',{ value="#local.validValIDs#", list="true", cfsqltype="CF_SQL_INTEGER" });
						}
					} else {
						switch (thisfield.xmlAttributes.dataTypeCode) {
							case "BIT":
								arrFilterClauses.append("intersect
									select md.memberID
									from dbo.ams_memberData as md
									inner join dbo.ams_members as m on m.orgID = @orgID
										and m.memberID = md.memberID 
										and m.memberID = m.activeMemberID
									inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
									inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
										and mdc.columnID = :val#paramNum#A
									inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
										and mdcdt.dataTypeCode = 'BIT'
									where mdcv.columnValueBit = :val#paramNum#");

								structInsert(sqlParams,'val#paramNum#',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_BIT" });
								break;
							case "DATE":
								arrFilterClauses.append("intersect
									select md.memberID
									from dbo.ams_memberData as md
									inner join dbo.ams_members as m on m.orgID = @orgID
										and m.memberID = md.memberID 
										and m.memberID = m.activeMemberID
									inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
									inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
										and mdc.columnID = :val#paramNum#A
									inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
										and mdcdt.dataTypeCode = 'DATE'
									where mdcv.columnValueDate = :val#paramNum#");

								structInsert(sqlParams,'val#paramNum#',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_DATE" });
								break;
							case "DECIMAL2":
								arrFilterClauses.append("intersect
									select md.memberID
									from dbo.ams_memberData as md
									inner join dbo.ams_members as m on m.orgID = @orgID
										and m.memberID = md.memberID 
										and m.memberID = m.activeMemberID
									inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
									inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
										and mdc.columnID = :val#paramNum#A
									inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
										and mdcdt.dataTypeCode = 'DECIMAL2'
									where mdcv.columnValueDecimal2 = :val#paramNum#");

								structInsert(sqlParams,'val#paramNum#',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_DECIMAL", scale="2" });
								break;
							case "INTEGER":
								arrFilterClauses.append("intersect
									select md.memberID
									from dbo.ams_memberData as md
									inner join dbo.ams_members as m on m.orgID = @orgID
										and m.memberID = md.memberID 
										and m.memberID = m.activeMemberID
									inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
									inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
										and mdc.columnID = :val#paramNum#A
									inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
										and mdcdt.dataTypeCode = 'INTEGER'
									where mdcv.columnValueInteger = :val#paramNum#");

								structInsert(sqlParams,'val#paramNum#',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_INTEGER" });
								break;
							case "STRING":
								switch (thisfield.xmlAttributes.displayTypeCode) {
									case "TEXTBOX":
										strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="mdcv.columnValueString", paramNum=paramNum,
											paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
										structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

										arrFilterClauses.append("intersect
												select md.memberID
												from dbo.ams_memberData as md
												inner join dbo.ams_members as m on m.orgID = @orgID
													and m.memberID = md.memberID 
													and m.memberID = m.activeMemberID
												inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
													#strHolding.fsmatch.filterClauseString#
												inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
													and mdc.columnID = :val#paramNum#A
												inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
													and mdcdt.dataTypeCode = 'STRING'");
										break;
								}
								break;
							case "CONTENTOBJ":
								var searchterms = createObject("component","model.search.bucket").prepareSearchString(arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));

								savecontent variable="stFilterPrep" {
									writeOutput("#stFilterPrep#
									IF OBJECT_ID('tempdb..##tmpMD_SRID_#thisfield.xmlAttributes.fieldCode#') IS NOT NULL 
										DROP TABLE ##tmpMD_SRID_#thisfield.xmlAttributes.fieldCode#;
									IF OBJECT_ID('tempdb..##tmpMD_#thisfield.xmlattributes.fieldCode#') IS NOT NULL 
										DROP TABLE ##tmpMD_#thisfield.xmlattributes.fieldCode#;
									CREATE TABLE ##tmpMD_SRID_#thisfield.xmlAttributes.fieldCode# (siteResourceID int PRIMARY KEY, valueID int, memberID int, contentID int);
									CREATE TABLE ##tmpMD_#thisfield.xmlattributes.fieldCode# (memberID int PRIMARY KEY);

									DECLARE @searchPhrase_#thisfield.xmlattributes.fieldCode# varchar(8000) = :val#paramNum#;

									INSERT INTO ##tmpMD_SRID_#thisfield.xmlAttributes.fieldCode# (siteResourceID, valueID)
									SELECT columnValueSiteResourceID, valueID
									FROM dbo.ams_memberDataColumnValues
									WHERE columnID = :val#paramNum#A;

									UPDATE tmp
									SET tmp.contentID = c.contentID
									FROM ##tmpMD_SRID_#thisfield.xmlAttributes.fieldCode# as tmp
									INNER JOIN dbo.cms_content as c on c.siteResourceID = tmp.siteResourceID;

									UPDATE tmp
									SET tmp.memberID = md.memberID
									FROM ##tmpMD_SRID_#thisfield.xmlAttributes.fieldCode# as tmp
									INNER JOIN dbo.ams_memberData as md on md.valueID = tmp.valueID;

									insert into ##tmpMD_#thisfield.xmlAttributes.fieldCode# (memberID)
									select distinct tmp.memberID
									from searchMC.dbo.cms_contentLanguages as scl
									inner join ##tmpMD_SRID_#thisfield.xmlAttributes.fieldCode# as tmp on tmp.contentID = scl.contentID
									inner join containstable(searchMC.dbo.cms_contentLanguages,searchtext,@searchPhrase_#thisfield.xmlattributes.fieldCode#) as sclsearch on sclsearch.[key] = scl.id;

									IF OBJECT_ID('tempdb..##tmpMD_SRID_#thisfield.xmlAttributes.fieldCode#') IS NOT NULL 
										DROP TABLE ##tmpMD_SRID_#thisfield.xmlAttributes.fieldCode#;");
								}
								savecontent variable="stFilterPost" {
									writeOutput("#stFilterPost#
									IF OBJECT_ID('tempdb..##tmpMD_#thisfield.xmlattributes.fieldCode#') IS NOT NULL 
										DROP TABLE ##tmpMD_#thisfield.xmlattributes.fieldCode#;");
								}
								arrFilterClauses.append("intersect
									select memberID
									from ##tmpMD_#thisfield.xmlAttributes.fieldCode#");

								structInsert(sqlParams,'val#paramNum#',{ value="#searchterms#", cfsqltype="CF_SQL_LONGVARCHAR" });
								break;
						}
					}

				/* *********************************** */
				/* if searching by email tag type  */
				/* *********************************** */
				} else if (ReFindNoCase('met_[0-9]+_emailType',thisfield.xmlattributes.fieldCode)) {
					strHolding.typeID = val(getToken(thisfield.xmlattributes.fieldCode,2,"_"));
					strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="met.emailType", paramNum=paramNum,
						paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
					structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

					arrFilterClauses.append("intersect
						select tag.memberID
						from dbo.ams_memberEmailTags as tag
						inner join dbo.ams_memberEmailTypes as met on met.orgID = @orgID 
							and met.emailTypeID = tag.emailTypeID
							#strHolding.fsmatch.filterClauseString#
						where tag.orgID = @orgID
						and tag.emailTagTypeID = #strHolding.typeID#");

				/* ******************************* */
				/* if searching by email or email tag  */
				/* ******************************* */
				} else if (ReFindNoCase('met?_[0-9]+_email',thisfield.xmlattributes.fieldCode)) {
					strHolding.type = getToken(thisfield.xmlattributes.fieldCode,1,"_");
					strHolding.typeID = val(getToken(thisfield.xmlattributes.fieldCode,2,"_"));
					strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="me.email", paramNum=paramNum,
						paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
					structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

					if (strHolding.type eq "met")
						arrFilterClauses.append("intersect
							select me.memberID
							from dbo.ams_memberEmailTags as tag
							inner join dbo.ams_memberEmailTypes as met on met.orgID = @orgID 
								and met.emailTypeID = tag.emailTypeID
							inner join dbo.ams_memberEmails as me on me.orgID = @orgID
								and me.emailTypeID = met.emailTypeID
								and me.memberID = tag.memberID
								#strHolding.fsmatch.filterClauseString#
							where tag.orgID = @orgID
							and tag.emailTagTypeID = #strHolding.typeID#");
					else
						arrFilterClauses.append("intersect
							select me.memberID
							from dbo.ams_memberEmails as me
							where me.orgID = @orgID
							#strHolding.fsmatch.filterClauseString#
							and me.emailTypeID = #strHolding.typeID#");

				/* *********************** */
				/* if searching by groups  */
				/* *********************** */
				} else if (thisfield.xmlattributes.dbObjectAlias eq "grps") {
					arrFilterClauses.prepend("and exists (select memberid from dbo.cache_members_groups where orgID = @orgID and memberID = m.memberID and groupID = :val#paramNum#)");
					structInsert(sqlParams,'val#paramNum#',{ value="#GetToken(thisField.xmlattributes.fieldcode,2,'_')#", cfsqltype="CF_SQL_INTEGER" });

				/* ****************************** */
				/* if searching by prof licenses  */
				/* ****************************** */
				} else if (thisfield.xmlattributes.dbObjectAlias eq "vwmpl") {
					strHolding.typeID = val(getToken(thisfield.xmlattributes.fieldCode,2,"_"));
					strHolding.type = getToken(thisfield.xmlattributes.fieldCode,3,"_");

					if (strHolding.type eq "licenseNumber") {
						strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="mpl.licensenumber", paramNum=paramNum,
							paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
						structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

						arrFilterClauses.append("intersect
							select mpl.memberID
							from dbo.ams_memberProfessionalLicenses as mpl
							where mpl.PLTypeID = #strHolding.typeID#
							#strHolding.fsmatch.filterClauseString#");

					} else if (strHolding.type eq "activeDate") {
						strHolding.matchClause = "and cast(mpl.activedate as date) = :val#paramNum#";
						structInsert(sqlParams,'val#paramNum#',{ value="#arguments.event.getTrimValue(thisField.xmlattributes.fieldcode)#", cfsqltype="CF_SQL_DATE" });

						arrFilterClauses.append("intersect
							select mpl.memberID
							from dbo.ams_memberProfessionalLicenses as mpl
							where mpl.PLTypeID = #strHolding.typeID#
							#strHolding.matchClause#");

					} else if (strHolding.type eq "status") {
						strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="mpls.statusName", paramNum=paramNum,
							paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
						structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

						arrFilterClauses.append("intersect
							select mpl.memberID
							from dbo.ams_memberProfessionalLicenses as mpl
							inner join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
							#strHolding.fsmatch.filterClauseString#
							where mpl.PLTypeID = #strHolding.typeID#");
					}

				/* ************************* */
				/* if searching by websites  */
				/* ************************* */
				} else if (thisfield.xmlattributes.dbObjectAlias eq "vwmw") {
					strHolding.typeID = val(getToken(thisfield.xmlattributes.fieldCode,2,"_"));
					strHolding.fsmatch = getSQLSearchConditionsFromFieldSetFSMatch(fs_match=arguments.event.getTrimValue('fs_match','s'), field="mw.website", paramNum=paramNum,
						paramValue=arguments.event.getTrimValue(thisField.xmlattributes.fieldcode));
					structInsert(sqlParams,'val#paramNum#',{ value=strHolding.fsmatch.sqlParam, cfsqltype="CF_SQL_VARCHAR" });

					arrFilterClauses.append("intersect
						select mw.memberID
						from dbo.ams_memberWebsites as mw
						where mw.orgID = @orgID
						#strHolding.fsmatch.filterClauseString#
						and mw.websiteTypeID = #strHolding.typeID#");
				}
			}

			for (var cl in arrFilterClauses) 
				stFilter &= " " & cl;
		} else {
			stFilter = "and 1=0";
			stFilterPrep = "";
			stFilterPost = "";
			sqlParams = {};
		}

		return { stFilterPrep=stFilterPrep, stFilter=stFilter, stFilterPost=stFilterPost, sqlParams=sqlParams, criteriaMet=criteriaMet };
		</cfscript>
	</cffunction>

	<cffunction name="getMaskedEmailAddress" access="public" output="false" returntype="string">
		<cfargument name="email" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfset local.maskedEmailUN = listFirst(arguments.email,'@').reReplaceNoCase('(?!^.?).(?!.{0}$)','*','all')>
		<cfset local.maskedEmailDomain = listLast(arguments.email,'@').reReplaceNoCase('(?!^.?).(?!.{0}$)','*','all')>
		<cfset local.email = "#local.maskedEmailUN#@#local.maskedEmailDomain#">
		<cfif len(local.email) lt 3>
			<cfset local.email = "*****@*****">
		</cfif>

		<cfreturn local.email>
	</cffunction>

	<cffunction name="getOutputFieldsFromXML" access="public" output="false" returntype="query">
		<cfargument name="outputFieldsXML" type="xml" required="true">

		<cfset var qryFSFields = QueryNew("fieldOrder,fieldcodeSect,allowMultiple,fieldcode,dataTypeCode,dbobject,dbobjectAlias,dbField,fieldLabel,isGrouped","integer,varchar,bit,varchar,varchar,varchar,varchar,varchar,varchar,bit")>
		<cfset var arrFields = XMLSearch(arguments.outputFieldsXML,'/fields/field')>
		<cfset arrFields.map(function(item) {
			qryFSFields.addRow(arguments.item.XmlAttributes);
		})>
		<cfreturn qryFSFields>
	</cffunction>

</cfcomponent>