<cfscript>
	variables.applicationReservedURLParams = "TestMode,mid";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	
	// page custom fields
	local.arrCustomFields = [];
	local.tmpField = { name="joinOnlineAffiliateActiveSubscriptionError", type="STRING", desc="Error Message for already Active Subscription", value='Records indicate you are currently a member. Please click <a href="/?pg=login">here</a> to log in.' }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="joinOnlineAffiliateBilledSubscriptionError", type="STRING", desc="Error Message for existing Billed Subscription", value="You need to renew your membership via the 'managesubscribers' page." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="confirmationEmailTxt", type="CONTENTOBJ", desc="confirmation email text", value="<b>Your membership term will begin after application review to ensure you meet the eligibility requirements. An email notification will be sent once the membership account has been approved and credit card has been processed.</b>" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="screenConfirmationTxt", type="CONTENTOBJ", desc="screen confirmation text", value="<b>Your membership term will begin after application review to ensure you meet the eligibility requirements. An email notification will be sent once the membership account has been approved and credit card has been processed.</b>" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send member confirmations from", value='<EMAIL>'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send staff confirmations to", value='<EMAIL>,<EMAIL>'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListServeAgreement", type="CONTENTOBJ", desc="CAALA List Agreement", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListServeTopInfoText", type="CONTENTOBJ", desc="List Serve Top Info Text", value="To join and participate in a CAALA List Serve you must maintain current status as an attorney member of the Association. Once your membership has been verified, you will receive a notification and instructions via e-mail and you will be able to send and receive e-mail messages from the CAALA LIST SERVE(s) to which you have subscribed." };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListServeAddText", type="STRING", desc="Text displayed for add List Subscription", value='Please choose which of the following List Serves you would like to subscribe to:'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListSubscriptionAddons", type="STRING", desc="Available List Subscriptions that Display on the Join Form", value='8130FFBF-E48F-4B5E-AE48-F32404F71DCF'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AvailableListSubscriptions", type="STRING", desc="Name of list subscriptions available separated by |", value='CAALA-WOMEN'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListAgreementFields", type="STRING", desc="Fields that display when a List Subscription is checked", value='7BB927E0-571E-4686-AE53-554A2DE6F2D0'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.customPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
	
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=event, 
		formName = 'frmJoinAffiliate',
		formNameDisplay = 'Online Affiliate Vendor Application',
		orgEmailTo = local.customPageFields.ConfirmationEmailStaffTo,
		memberEmailFrom = local.customPageFields.ConfirmationEmailStaffFrom
	));
	
	local.memberData = getMemberData(orgID=local.orgID,siteID=local.siteID,memberID=local.memberID);
	local.qryGender = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID,columnName="gender");	
	local.qryAffiliateLogo = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Affiliate Logo");

	local.profile_1._profileCode = 'AUTHCIM';
	local.profile_1._profileID = application.objCustomPageUtils.acct_getProfileID(siteid=local.siteID,profileCode=local.profile_1._profileCode);

	local.strDues = structNew();
	local.strDues.affiliateDues = structNew();
	local.strDues.affiliateDues.txt = 'Affiliate Vendor Membership';
	local.strDues.affiliateDues.amt = 400;
	local.strDues.enhancedDirectoryListing = structNew();
	local.strDues.enhancedDirectoryListing.txt = 'Enhanced Directory Listing';
	local.strDues.enhancedDirectoryListing.amt = 250;	
	local.strDues.categories = structNew();
	local.USStates 	= application.objCustomPageUtils.mem_getStatesByCountry('United States');
	local.listServeCertAgreePrefFieldSetUID = local.customPageFields.ListAgreementFields;	
	local.affiliateLogoFieldSetUID = "CEC136BA-96F5-49F0-B484-AA65709BC6A2";
</cfscript>

<cfquery name="local.qryCategories" datasource="#application.dsn.membercentral.dsn#">
	select  ROW_NUMBER() OVER(ORDER BY subscriptionName ASC) AS Row, subscriptionName
	from sub_types t
	inner join sub_subscriptions s on s.typeID = t.typeid
	where s.status = 'A'
	and t.uid = '17d212a8-8e8f-4a3c-8f24-b2e7c16eede5'
	and t.siteID = #local.siteID#
</cfquery>
<cfloop query="local.qryCategories">
	<cfset local.strDues.categories[local.qryCategories.row] = local.qryCategories.subscriptionName>
</cfloop>
<cfset local.strDues.categoryColumnCount = ceiling(local.qryCategories.recordcount /3)>

<cfset local.qryOrgMemberFields = application.objCustomPageUtils.getOrgMemberFields(orgID=local.orgID)>
<cfif local.qryOrgMemberFields.usePrefixList is 1>
	<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.orgID)>
</cfif>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##dbdedf; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; color:##000; }
			.frmButtons{ padding:5px 0; border-top:3px solid ##03608B; border-bottom:3px solid ##03608B; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			<!--- background:url(/assets/common/images/interior_titleBG_Tan.jpg) repeat-x; --->
			.TitleText { font-family:Tahoma; font-size:16pt; color:##000; font-weight:bold; padding-bottom: 15px; padding-top: 15px;}
			.CPSection{ border:0.5px solid ##56718c; margin-bottom:15px; }
			.CPSectionTitle { font-size:12pt; height:auto; font-weight:bold; color:##000; padding:10px 5px; background:##bbb; }
			.CPSectionContent{ padding:10px 5px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##ececec; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##ececec; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##ececec;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b { font-weight:bold; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.BB { border-bottom:0.5px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.tsAppBodyText { color:##000000;}
			select.tsAppBodyText{color:##666;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.form-horizontal .control-group { margin-bottom: 5px; }
			.form-horizontal .control-label { width: 320px; }
			.form-horizontal .controls {  margin-left: 340px; }
			.optionalItems { margin-left: 0px !important; }
			.topArea .span5 { text-align: right; }
			.topArea .span6 { width: 50% !important; }
			.topArea .controls, ##CCInfo .controls { margin-left: 0px; }
			.topArea label { padding-top: 0px !important; font-size: 11pt !important; }
			.commentbox label { width: 100% !important; text-align: left !important; }
			.commentbox .controls { margin-left: 0px !important; }
			.vendorCategoryList .controls { margin-left: 0px !important; }
			.smallBox { padding: 5px; }
			.search-outer input { height: 40px !important; }
			input.tsAppBodyText {font-family: Verdana, Arial, Helvetica, sans-serif;color: ##666;}
			.frmText { font-size:11pt; }
			textarea { width: 216px; }
			.addressBlock span { display: inline-block; font-size: 11pt; }
			##paymentTable .controls { margin-left: 0px; }
			##paymentTable .controls label { font-size: 11pt; }
			.table th, .table td { border-top: 1px solid ##060606; word-break: break-word; }
			@media screen and (max-width: 979px){
				.input-xxlarge { width:100% !important; }
				.addressBlock span{margin-top: 10px !important;margin-bottom: 5px !important;}
			}
			@media screen and (max-width: 767px){
				.form-horizontal .control-label{width: 100%; text-align: left;}
				.form-horizontal .controls{margin-left:0px}
			} 
			.centerit { position: absolute; top: 50%; width: 100%; margin-left: 15%; }
			.centerit button { position: relative; top: -15px; height: 30px; } 
			.center-holder{ position: relative; } 
			.accntPopup > div:last-child > div.span12{margin-left: 2.564102564102564%;} 
			.accntPopup > div:first-child > div { left:40%; position:absolute; }
			@media only screen and (max-width:560px){ 
				.accntPopup > div:first-child > div { left:25%; }
			} 
			@media only screen and (max-width:400px){ 
				.accntPopup > div:first-child > div { left:12%; } 
			} 
			@media only screen and (max-width:345px){ 
				.accntPopup > div:first-child > div { left:1%; } 
				.accntPopup > div:first-child > div button{ font-size:11px; } 
			} 
			.acntLookUpMessage { margin-left: 48%!important; }
			@media screen and (max-width:767px){
				.span6,.span5{ display:inline!important; width:50%!important; float:left!important; }
			}
			@media screen and (min-width: 359px) and (max-width: 368px){ 
				.acntLookUpBtn button{ font-size:13px; } 
			} 
			@media screen and (max-width: 359px){ 
				.acntLookUpBtn button{ font-size:11px; }
			}
			.listServeTable > table {
				width:100%!important;
			}
		</style>
	</cfsavecontent>
	<!--- --------------------------------------------------------------------------------------------------------------- --->
	<cfsavecontent variable="local.pageJS">
		<script type="text/javascript">
			$(function() {
				alignAcntLookUpBox();
				$(window).on("resize load",function(){
					alignAcntLookUpBox();
				});
			});
			
			function alignAcntLookUpBox(){
				var _heightRight = $('.accntPopup > div:last-child').height();
				var _top = _heightRight / 2 - 10;
				$('.accntPopup > div:first-child').height(_heightRight);
				$('.accntPopup > div:first-child > div').css('top',_top+'px');
			}
			function _FB_hasValue(obj, obj_type){
				if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
				else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
				else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
				else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
				else{ return true; }
			}
			function _FB_withinResponseLimit(obj, obj_type, optRespLimit) {
				var cnt = 0;
				for (var i=0; i < obj.length; i++) {
					if (obj_type == 'SELECT' && obj.options[i].selected) cnt++;
					else if (obj_type == 'CHECKBOX' && obj[i].checked) cnt++;
				}					
				if (cnt <= optRespLimit) return true;
				else return false;
			}
			function formatCurrency(num) {
				num = num.toString().replace(/\$|\,/g,'');
				if(isNaN(num)) num = "0";
				num = Math.abs(num);	// added by tl 5/16/2006. force positive values only
				sign = (num == (num = Math.abs(num)));
				num = Math.floor(num*100+0.50000000001);
				cents = num%100;
				num = Math.floor(num/100).toString();
				if(cents<10) cents = "0" + cents;
				for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
				num.substring(num.length-(4*i+3));
				return (((sign)?'':'-') + '$' + num + '.' + cents);
			}
			function getSelectedRadio(buttonGroup) {
				if (buttonGroup[0]) {
					for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
				} else { if (buttonGroup.checked) return 0; }
				return -1;
			}
			function selectMember() {
				$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
			}
			function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
			function addMember(memObj) {
				$.colorbox.close();
				assignMemberData(memObj);
			}
		</script>
	</cfsavecontent>
	#local.pageJS#
	#local.pageCSS#
	<div id="customPage" class="row-fluid">
		<div class="TitleText c row-fluid">#local.Organization# - #local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<cfcase value="0">
				<cfset local.qryListSubData =  application.objCustomPageUtils.sub_getSubscriptionsDetailsFromTypeUID(subTypeUID='#local.customPageFields.ListSubscriptionAddons#',siteID=local.siteID)/>
				<cfset local.listServeCertAgreePrefFieldSet = application.objCustomPageUtils.renderFieldSet(uid=local.listServeCertAgreePrefFieldSetUID, mode="collection", strData={})>				
				<cfset local.affiliateLogoFieldSet = application.objCustomPageUtils.renderFieldSet(uid=local.affiliateLogoFieldSetUID, mode="collection", strData={})>
				<script type="text/javascript">
					function _FB_validateForm() {
						var _CF_this = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						if (!_FB_hasValue(_CF_this['companyName'], 'TEXT')) arrReq[arrReq.length] 			= 'Please enter a Company Name';
						if (!_FB_hasValue(_CF_this['contactName'], 'TEXT')) arrReq[arrReq.length] 			= 'Please enter a Primary Contact Name';
						if ($('##prefix').val().length == 0) arrReq[arrReq.length] 						= 'Please enter your name Prefix.';
						
						if (!_FB_hasValue(_CF_this['primaryPhone'], 'TEXT')){
							arrReq[arrReq.length] 	= 'Please enter a Phone number';
						}else if(!checkPhFormat(_CF_this['primaryPhone'])){
							arrReq[arrReq.length] = 'Enter phone number, formatted xxx-xxx-xxxx (e.g. ************)';

						}				
						if (!_FB_hasValue(_CF_this['email'], 'TEXT')) arrReq[arrReq.length] 				= 'Please enter an Email Address';
						if (!_FB_hasValue(_CF_this['address1'], 'TEXT')) arrReq[arrReq.length] 			= 'Please enter a Street Address';
						if (!_FB_hasValue(_CF_this['city'], 'TEXT')) arrReq[arrReq.length] 				= 'Please enter a City';
						if (!_FB_hasValue(_CF_this['state'], 'TEXT')) arrReq[arrReq.length] 				= 'Please enter a State';
						if (!_FB_hasValue(_CF_this['zip'], 'TEXT')) arrReq[arrReq.length] 				= 'Please enter a Zip';
						if (!_FB_hasValue(_CF_this['companyDescription'], 'TEXT')) arrReq[arrReq.length] 	= 'Please enter a Description of your Company';
						if ($('##gender').val().length == 0) arrReq[arrReq.length] 						= 'Please select your Gender.';
						if (!_FB_hasValue(_CF_this['vendorCategory'], 'CHECKBOX')) arrReq[arrReq.length] = 'Please select at least one Vendor Category';
						
						if( $('input[name=listServeNames]:checkbox:checked').length > 0 ) {						
							#local.listServeCertAgreePrefFieldSet.jsValidation#
						}
						#local.affiliateLogoFieldSet.jsValidation#
						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
					function checkPhFormat(x){
						var numberEntered = x.value.toString();
						var pattern = /\d{3}-\d{3}-\d{4}/;
						var match = pattern.test(numberEntered);
						
						if ( match == false ){
							return false;
						} else {
							return true;
						}
					}
					function assignMemberData(memObj){
						var _CF_this = document.forms["#local.formName#"];
						var er_change = function(r) {
							var results = r;
							if( results.success ){
								_CF_this['memberNumber'].value 	= results.membernumber;
								_CF_this['memberID'].value 			= results.memberid;
								_CF_this['companyName'].value 	= results.company;
								_CF_this['contactName'].value 	= results.firstname + ' ' + results.lastname;
								_CF_this['address1'].value 		= results.address1;
								_CF_this['city'].value 			= results.city;
								_CF_this['state'].value 			= results.statecode;
								_CF_this['zip'].value 			= results.postalcode;
								_CF_this['primaryPhone'].value 			= results.phone;
								_CF_this['email'].value 			= results.email;
								
								if ( results.prefix ){
									$('##prefix option[value="'+results.prefix+'"]').attr('selected',true);	
								}
								
								titleChange();
								
								if ( results.memberid > 1 ){ 
									AJAXCheckSubs(results.memberid , 'A');
								}
								
								document.getElementById('formToFill').style.display = '';
							}
							else{ /*alert('not success');*/ }
						};
						var objParams = { memberNumber:memObj.memberNumber};
						TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
					}
					function AJAXCheckSubs(member, status){
						$('##hasDuesSubs').hide();
						var _status = status;
						var stopProgress = function(r){
						var isActiveSubs= false;
						var isBilledSubs= false;
							if ( r.data.typename.length ){
								$.each(r.data.typename,function(x){
										if ( r.data.typename[x] == 'Affiliate Membership Dues' ){
											$('##formToFill').hide();
											if(_status == 'A'){
												isActiveSubs = true;
											} else {
												isBilledSubs = true;
											}
										}
								});
							}
							if(!isActiveSubs && _status == 'A' ) {
								AJAXCheckSubs(member,'O');
							} else if(!isBilledSubs && _status == 'O') {
								return true;
							} else if(isActiveSubs) {
								$('##hasDuesSubsMessage').html("#ReplaceNoCase(local.customPageFields.joinOnlineAffiliateActiveSubscriptionError,'"',"'","ALL")#");
								$('##hasDuesSubs').show();
								return false;
							} else if(isBilledSubs){
								$('##hasDuesSubsMessage').html("#ReplaceNoCase(local.customPageFields.joinOnlineAffiliateBilledSubscriptionError,'"',"'","ALL")#");
								$('##hasDuesSubs').show();
								return false;
							}
							
						};
						var params = { memberID:member, status:_status, distinct: true };
						TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,stopProgress);
					}
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}
					function checkURL (abc) {
						var string = abc.value;
						if (!~string.indexOf("http") && string.length > 0){
							string = "http://" + string;
						}
						abc.value = string;
						return abc
					}
					function countCategories(x){
						var count = 0;
						count = $('input[name="vendorCategory"]:checked').length;
						if ( count > 3 ){
							confirm('You may only select up to three(3) vendor categories.');
							$('input[name="vendorCategory"]:checked').attr("checked",false);
						}
					}
					function titleChange(){
						var selectedTitle = $("select[name='prefix']").val();
						if (selectedTitle === "Mr." || selectedTitle === ""){
							hideShowSubscribeMe('CAALA-WOMEN',true);					
						}else{
							hideShowSubscribeMe('CAALA-WOMEN',false);
						}
					}
					function hideShowSubscribeMe(val,isHide){
						var checkbox = $('input[name="listServeNames"][value="'+val+'"]');
						if(isHide){
							if (checkbox.is(':checked')) {
								checkbox.trigger("click");
							}
							checkbox.prop('checked', false).parent().parent().parent().hide();
							$("##listServeNameHolder").hide();
						}else{
							checkbox.parent().parent().parent().show();
							$("##listServeNameHolder").show();
						}
					}
					$(document ).ready(function() {	
						var $listServeAgreementBox = $('##listServeAgreementBox');
						$listServeAgreementBox.hide();
					
						$('input[name=listServeNames]').click(
							function() {
								if ($('input[name=listServeNames]:checkbox:checked').length > 0) {
									$listServeAgreementBox.show();									
								} else {
									$listServeAgreementBox.hide();						
								}
							}	
						);
						
						$(document).on('change','select[name="prefix"]',function(){
							titleChange();
						});
					});
				</script>
					
				<div class="r i frmText row-fluid">*Denotes required field</div>
				<cfform name="#local.formName#"  id="#local.formName#" class="form-horizontal" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();" enctype="multipart/form-data">
					<input type="hidden" name="isSubmitted" value="1" />
					<input type="hidden" name="memberID" value="#session.cfcUser.memberData.memberID#" />
					<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection row-fluid">
							<div class="CPSectionTitle BB row-fluid">Account Lookup / Create New Account</div>
							<div class="frmRow1 row-fluid" style="padding:10px;">
								<div class="row-fluid accntPopup">
									<div class="span5 c" style="position:relative">
										<div> 
											<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
										</div>
									</div>
									<div class="span6 frmText">
										<div class="span12">Click the <span class="b">Account Lookup</span> button to the left.</div>
										<div class="span12">Enter the search criteria and click <span class="b">Continue</span>.</div>
										<div class="span12">If you see your name, please press the <span class="b">Choose</span> button next to your name.</div>
										<div class="span12">If you do not see your name, click the <span class="b">Create an Account</span> link.</div>
									</div>
								</div>
							</div>
						</div>
					</cfif>
					<div id="hasDuesSubs" style="display:none;">
						<div class="CPSection row-fluid">
							<div class="CPSectionTitle BB row-fluid">Alert!</div>
							<div class="frmRow1 row-fluid" style="padding:10px;">
								<div class="row-fluid center-holder">
									<div class="span12" id="hasDuesSubsMessage"></div>
								</div>
							</div>
						</div>
					</div>
						<div id="formToFill" class="row-fluid" <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>style="display:none;"<cfelse>style="display:;"</cfif>>
						
						<div class="CPSection row-fluid topArea">
							<div id="sectionContent" class="tsAppBodyText frmRow1 frmText row-fluid">
								<div class="span12">
									<div class="span6 affiliateMember">
										<div class="row-fluid CPSectionTitle BB">#local.strDues.affiliateDues.txt#</div>
										<div class="row-fluid CPSectionContent">
											<div class="row-fluid">
												<div class="span7 b">
													#local.strDues.affiliateDues.txt#
												</div>
												<div class="span5 b">
													#DollarFormat(local.strDues.affiliateDues.amt)# Annually
												</div>
											</div>
											<div class="row-fluid"><em>Any person/company whose nature of business provides goods or services to plaintiff trial lawyers may join as an Affiliate</em></div>
										</div>
									</div>
									<div class="span6 optionalItems">
										<div class="row-fluid CPSectionTitle BB BL">Optional Items</div>
										<div class="row-fluid CPSectionContent BL">
										<div class="row-fluid">
											<div class="span7">
												<div class="control-group">
													<div class="controls">
														<label class="checkbox"><input type="checkbox" name="enhancedDirectoryListing" value="Yes" />&nbsp;<strong>#local.strDues.enhancedDirectoryListing.txt#</strong></label>
													</div>
												</div>
											</div>
											<div class="span5">
												<strong>#DollarFormat(local.strDues.enhancedDirectoryListing.amt)#  Annually</strong>
											</div>
										</div>
										<div class="row-fluid"><em>Includes company logo, placement at top of category, company description and links to vendor categories, web address display.</em></div>
									</div>
									</div>
								</div>
							</div>
						</div>
						<div class="CPSection row-fluid">
							<div class="CPSectionTitle BB row-fluid CPSectionContent">Company Information</div>
							<div id="sectionContent" class="tsAppBodyText frmRow1 frmText row-fluid CPSectionContent">
								<div class="control-group">
									<label class="control-label" for="companyName">* Company Name:</label>
									<div class="controls">
										<cfinput class="input-xxlarge tsAppBodyText" size="60" name="companyName" id="companyName" type="text" value="" />												
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="prefix">* Prefix:</label>
									<div class="controls">
										<cfif local.qryOrgMemberFields.hasPrefix is 1>
											<cfif local.qryOrgMemberFields.usePrefixList is 1>
												<cfselect class="tsAppBodyText" id="prefix" name="prefix" query="local.qryOrgPrefixes" value="prefix" display="prefix" selected="" queryPosition="below">
												<option value=""></option>
												</cfselect>
											<cfelse>
												<cfinput value="" class="tsAppBodyText largeBox" name="prefix"  id="prefix" type="text" size="5" />
											</cfif>
										</cfif>	
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="contactName">* Primary Contact Name:</label>
									<div class="controls">
										<cfinput class="input-xxlarge tsAppBodyText" size="13" maxlength="13" name="contactName" id="contactName" type="text" value="#session.cfcUser.memberData.firstname# #session.cfcUser.memberData.lastname#" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="primaryPhone">* Primary Contact Phone:</label>
									<div class="controls">
										<cfinput class="input-xlarge tsAppBodyText" size="13" maxlength="13" name="primaryPhone" id="primaryPhone" type="text" value="#local.data.phone.phone#" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="email">* Primary Contact Email:</label>
									<div class="controls">
										<cfinput class="input-xxlarge tsAppBodyText" size="60" name="email" id="email" type="text" value="#session.cfcUser.memberData.email#" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="address1">* Address Line 1:</label>
									<div class="controls">
										<cfinput class="input-xxlarge tsAppBodyText" size="60" name="address1" id="address1" type="text" value="#local.data.address.address1#" />
									</div>
								</div>									
								<div class="control-group">
									<label class="control-label" for="city">* City:</label>
									<div class="controls">
										<cfinput class="input-xlarge tsAppBodyText" size="25" name="city" id="city" type="text" value="#local.data.address.city#" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="state">* State:</label>
									<div class="controls">
										<cfinput class="input-small tsAppBodyText" size="2" maxlength="2" name="state" id="state" type="text" value="#local.data.address.stateCode#" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="zip">* Zip:</label>
									<div class="controls">
										<cfinput class="input-medium tsAppBodyText" size="10" maxlength="15" name="zip" id="zip" type="text" value="#local.data.address.postalCode#" />
									</div>
								</div>									
								<div class="control-group">
									<label class="control-label" for="website">Website:</label>
									<div class="controls">
										<cfinput value="" class="input-xxlarge tsAppBodyText" name="website"  id="website" type="text" size="50" validate="regular_expression" onblur="checkURL(this)" pattern="#application.regEx.url#" message="Please enter a valid website address.\r\n   Example: http://www.websitename.com" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="referredBy">Referred By:</label>
									<div class="controls">
										<cfinput class="input-xxlarge tsAppBodyText" size="60" name="referredBy" id="referredBy" type="text" value="" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="gender">* Gender:</label>
									<div class="controls">
										<select name="gender" id="gender">
											<option></option>
											<cfoutput><cfloop array="#local.qryGender.columnValueArr#" index="local.qryGender">
												<option value="#local.qryGender.valueid#">#local.qryGender.columnValueString#</option>
											</cfloop></cfoutput>
										</select>
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="referredBy">* Company 25 word Description:</label>
									<div class="controls">
										<textarea class="input-xxlarge" name="companyDescription" value="" cols="70" rows="3">&nbsp;</textarea>
										<span class="help-block"><em>must provide goods or service to plaintiff trial lawyers</em></span>
									</div>
								</div>
							</div>
						</div>
						
						<div class="CPSection row-fluid">
							<div class="CPSectionTitle BB row-fluid">Vendor Categories</div>
							<div class="subCPSectionArea1 row-fluid">
								<div class="subCPSectionText row-fluid">Select up to 3 categories to be listed under in the Affiliate Vendor Directory at caala.org. (<em>At least 1 category must be selected.</em>)</div>
							</div>
							<div id="sectionContent" class="tsAppBodyText frmText row-fluid vendorCategoryList CPSectionContent">
								<div class="span4">
									<div class="control-group">
										<div class="controls">
											<cfloop index="i" from="1" to="#local.strDues.categoryColumnCount#">
											<cfoutput>
												<label class="checkbox"><input type="checkbox" class="tsAppBodyText" name="vendorCategory" value="#local.strDues.categories[i]# " class="vendorCategory" onClick="countCategories(this);">#local.strDues.categories[i]#</label>
											</cfoutput>
											</cfloop>
										</div>
									</div>
								</div>
								<div class="span4">
									<div class="control-group">
										<div class="controls">
											<cfloop index="i" from="#(local.strDues.categoryColumnCount +1)#" to="#(local.strDues.categoryColumnCount*2)#">
											<cfoutput>
												<label class="checkbox"><input type="checkbox" class="tsAppBodyText" name="vendorCategory" value="#local.strDues.categories[i]# " class="vendorCategory" onClick="countCategories(this);">#local.strDues.categories[i]#</label>
											</cfoutput>
											</cfloop>
										</div>
									</div>
								</div>
								<div class="span4">
									<div class="control-group">
										<div class="controls">
											<cfloop index="i" from="#(local.strDues.categoryColumnCount*2)+1#" to="#local.qryCategories.recordcount#">
											<cfoutput>
												<label class="checkbox"><input type="checkbox" class="tsAppBodyText" name="vendorCategory" value="#local.strDues.categories[i]# " class="vendorCategory" onClick="countCategories(this);">#local.strDues.categories[i]#</label>
											</cfoutput>
											</cfloop>
										</div>
									</div>
								</div>									
							</div>
						</div>						
						
						<cfif NOT structIsEmpty(local.qryAffiliateLogo)>
						<div class="CPSection row-fluid">
							<div id="affiliateLogo" class="tsAppBodyText frmRow1 frmText CPSectionContent P">
								<div class="row-fluid">
									<span class="affiliateLogoFieldSetHolder">
										<div class="row-fluid">
											<div class="tsAppSectionHeading">#local.affiliateLogoFieldSet.fieldSetTitle#</div>
											<div class="tsAppSectionContentContainer">
												<input type="file" name="md_#local.qryAffiliateLogo.COLUMNID#" id="md_#local.qryAffiliateLogo.COLUMNID#" value="Select file">
											</div>
										</div>
									</span>
								</div>
							</div>
						</div>
						</cfif>
						
						<div class="CPSection row-fluid" id="listServeNameHolder" style="display:none">
							<div class="CPSectionTitle BB row-fluid">List Serves</div>
							<div id="sectionContent" class="tsAppBodyText frmRow1 row-fluid frmText CPSectionContent P">
								<!--- List serve subs --->
								<div class="row-fluid">
									<div colspan="2" class="bodyText row-fluid">
										#local.customPageFields.ListServeTopInfoText#
									</div>
									<br />
									<div class="row-fluid">#local.customPageFields.ListServeAddText#</div>
									<div class="row-fluid">
										<cfloop query="#local.qryListSubData#">
											<cfif ListFind(local.customPageFields.AvailableListSubscriptions, local.qryListSubData.subscriptionName)>
												<div class="control-group">
													<div class="controls span12">
														<br>
														<label for="listServeNames">
															<cfinput type="checkbox" name="listServeNames"  id="listServeNames" value="#local.qryListSubData.subscriptionName#"> Subscribe me to the #local.qryListSubData.subscriptionName# List Serve
														</label>
													</div>
												</div>
											</cfif>
										</cfloop>
									</div>		
								</div>	
								<div id="listServeAgreementBox" style="display: ruby;">
									#local.customPageFields.ListServeAgreement#
									<span class="listServeCertAgreePrefFieldSetHolder">
										<div class="row-fluid">
											<div class="tsAppSectionHeading">#local.listServeCertAgreePrefFieldSet.fieldSetTitle#</div>								
											<div class="tsAppSectionContentContainer">										
												#local.listServeCertAgreePrefFieldSet.fieldSetContent#									
											</div>
										</div>
									</span>
								</div>
							</div>
						</div>	
						<div class="CPSection row-fluid">
							<div id="sectionContent" class="tsAppBodyText frmRow1 frmText row-fluid CPSectionContent">
								<strong>Note:</strong> Consumer Attorneys Association of Los Angeles will conduct a periodic assessment to review the relationship between your company, 
								the association and its membership, including the quality of service provided. Affiliation with CAALA may be terminated at any time by 
								either Party with written notice. CAALA may terminate its affiliation with any affiliate solely at the discretion of CAALA's Executive 
								Director. Termination notification must be given in writing CAALA. Affiliates may not vote, hold office or have access to attorney member-only 
								List Serves, meetings or events.

								Contributions to CAALA (but not to CAALA-PAC) may be deductible in some circumstances as business expenses, but in no event as charitable contributions. 
								Consult your tax advisor regarding your situation.
							</div>
						</div>
						
						<div id="formButtons" class="row-fluid">
							<div class="row-fluid">
								<div align="center" class="frmButtons row-fluid">
									<input type="submit" value="Continue" name="submit" class="btn btn-default"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel" class="btn btn-default"/>
								</div>
							</div>
						</div>
					</div>
				</cfform>

				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
				</cfif>
			</cfcase>
			<cfcase value="1">
				<cfquery name="local.qryStateByStateCode" dbtype="query">		
					select stateID		
					from [local].USStates		
					where code = '#event.getValue("state","")#'		
				</cfquery>
				<cfset local.recordUpdated = false>	
				
				<!--- save member data --->			
				<cftry>
				
					<cfset local.newSaveMemberID=0>
					<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
					<cfif IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),local.useMID)>
						<cfset local.newSaveMemberID=local.useMID>
					</cfif>
					<cfscript>
						local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.event.getCollection(), memberID=local.newSaveMemberID);
						local.newSaveMemberID = local.strResult.memberID;
						local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.newSaveMemberID);
						local.objSaveMember.setDemo(prefix=event.getTrimValue('prefix',''), firstName=ListFirst(event.getTrimValue('contactName',''),' '),
											lastName=listRest(event.getTrimValue('contactName',''),' '), company=event.getTrimValue('companyName',''));
						local.objSaveMember.setRecordType(recordType='Organization');
						local.objSaveMember.setMemberType(memberType='User');
						local.objSaveMember.setEmail(type='Work Email', value=event.getTrimValue('email',''));
						local.objSaveMember.setPhone(addresstype='Office Address', type='Phone', value=event.getTrimValue('primaryPhone',''));
						local.objSaveMember.setAddress(type='Office Address', address1=event.getTrimValue('address1',''),
											city=event.getTrimValue('city',''), 
											stateID=local.qryStateByStateCode.stateID, postalCode=event.getTrimValue('zip',''));
						if(isValid("regex",event.getTrimValue('website',''),application.regEx.url))
							local.objSaveMember.setWebsite(type='Website', value=event.getTrimValue('website',''));
						
						if(event.getTrimValue('gender','') neq '')
							local.objSaveMember.setCustomField(field='Gender', valueid=event.getTrimValue('gender',''));
						
						if(event.getTrimValue('companyDescription','') neq '')
							local.objSaveMember.setCustomField(field='Vendor Bio', value=event.getTrimValue('companyDescription',''));

						if(event.getTrimValue('referredBy','') neq '')
							local.objSaveMember.setCustomField(field='Referred By', value=event.getTrimValue('referredBy',''));
						
						local.objSaveMember.setCustomField(field='Contact Type', value='Vendor');
						local.strResult = local.objSaveMember.saveData();
					</cfscript>
				
					<cfif local.strResult.success>
						<cfset local.recordUpdated = true>
						<cfset local.useMID = local.strResult.memberID>
						<cfif NOT structIsEmpty(local.qryAffiliateLogo) AND event.valueExists('md_#local.qryAffiliateLogo.COLUMNID#') AND len(trim(event.getValue('md_#local.qryAffiliateLogo.COLUMNID#','')))>
							<cfset arguments.event.setValue('fileToUpload','md_#local.qryAffiliateLogo.COLUMNID#')>
							<cfset application.objCustomPageUtils.saveMemberCustomSingleDocument(local.qryAffiliateLogo.COLUMNID,local.useMID)>
						</cfif>
					<cfelse>
						<cfthrow message="Unable to save member.">
					</cfif>
					
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch)>
						<cfset local.recordUpdated = false>
					</cfcatch>
				</cftry>
				
				<cfif NOT local.recordUpdated>
                    <cfoutput>
                        <div class="tsAppBodyText">
                            <b>An error occurred while setting up your membership.  We are unable to process your membership online at this time.</b>
                            <br>
                            Please contact customer support for assistance.
                            <br><br>
                            We apologize for the inconvenience. 
                        </div>
                    </cfoutput>
                <cfelse>
			
					<!---PAYMENT SECTION START--->
					<cfscript>
					local.profile_1.strPaymentForm = 	application.objPayments.showGatewayInputForm(
																				siteid=local.siteID,
																				profilecode=local.profile_1._profileCode,
																				pmid = local.useMID,
																				showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																				usePopupDIVName='paymentTable'
																			);
					</cfscript>
					<script type="text/javascript">
						function checkPaymentMethod() {
							var rdo = $('##payMethCC');
							if (rdo[0].checked) {//credit card
								document.getElementById('CCInfo').style.display = '';
								document.getElementById('CheckInfo').style.display = 'none';
							}  
						}
						function _validate() {
							var _CF_this = document.forms["#local.formName#"];
							var arrReq = new Array();
							if (!_FB_hasValue(_CF_this['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
							var MethodOfPaymentValue = 'CC';
							
							if( MethodOfPaymentValue == 'CC' )	{
								#local.profile_1.strPaymentForm.jsvalidation#
								var confirmation 	= 0;
								var statement			= _CF_this['confirmationStatement'];
								if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
								if(confirmation == 0) arrReq[arrReq.length] = 'Please accept the Confirmation Statement';
							}
							if (arrReq.length > 0) {
								var msg = 'The following fields are required:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								return false;
							}
							return true;
						}
						function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
					</script>
					<cfif len(local.profile_1.strPaymentForm.headCode)>
						<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
					</cfif>
					
					<div id="paymentTable" class="row-fluid">
					<cfoutput>
						<div id="payerrDIV" class="row-fluid" style="display:none;margin:6px 0;"></div>
						<div class="form row-fluid">
							<cfform name="#local.formName#"  id="#local.formName#" class="form-horizontal" method="POST" action="#local.customPage.baseURL#&mid=#local.useMID#" onSubmit="return _validate();">
								<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
								<cfloop collection="#event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
										and left(local.key,4) neq "fld_">
										<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#event.getValue(local.key)#">
									</cfif>
								</cfloop>
								<div class="row-fluid">
									<div class="CPSection row-fluid">
										<div class="CPSectionTitle BB row-fluid">*Method of Payment</div>
										<div class="row-fluid P">
											<div class="row-fluid">Please select your preferred method of payment from the options below.</div>
											<div class="row-fluid">
												<div class="control-group">
													<div class="controls">
														<label class="radio"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" id="payMethCC" type="radio" onClick="checkPaymentMethod();">Credit Card</label>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div id="CCInfo" style="display:none;" class="CPSection row-fluid">
										<div class="CPSectionTitle row-fluid">Credit Card Information</div>
										<div class="PL PR frmText paymentGateway BT BB row-fluid">
											<cfif len(local.profile_1.strPaymentForm.inputForm)>
												<div class="row-fluid">#local.profile_1.strPaymentForm.inputForm#</div>
											</cfif>
										</div>
										<div class="P row-fluid">
											<div class="PB row-fluid">* Please confirm the statement below:</div>
											<div class="row-fluid">
												<div class="control-group">
													<div class="controls">
														<label class="checkbox"><input name="confirmationStatement" id="confirmationStatement"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  />I confirm that I have full authority to make payment from the above credit card account for my contribution.</label>
													</div>
												</div>
											</div>
										</div>
										
										<div class="P row-fluid"><button type="submit" class="tsAppBodyButton btn btn-default" name="btnSubmit">AUTHORIZE</button></div>
									</div>
									<div id="CheckInfo" style="display:none;" class="CPSection row-fluid">
										<div class="CPSectionTitle row-fluid">Check Information</div>
										<div class="P row-fluid">
											Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
											<strong>Consumer Attorneys of Los Angeles</strong><br />
											800 W 6th St. Suite 700<br />
											Los Angeles, CA 90017
										</div>
										<div class="P row-fluid">
											<button type="submit" class="tsAppBodyButton" name="btnSubmit">CONTINUE</button><br />
											<br />
											<strong>NOTE:</strong> Your membership will not be active until payment is received.
										</div>
									</div>
								</div>
								<cfinclude template="/model/cfformprotect/cffp.cfm" />
							</cfform>
						</div>
					</cfoutput>
					</div>
				</cfif>
			</cfcase>
			<cfcase value="2">
				<cfif NOT local.objCffp.testSubmission(form)>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>
				<cfscript>
					local.membershipDues = local.strDues.affiliateDues.amt;
					
					if ( event.getValue('enhancedDirectoryListing','No') eq "Yes" ){
						local.membershipDues = local.membershipDues + local.strDues.enhancedDirectoryListing.amt;
					}
					
					local.totalAmount = local.membershipDues;
				</cfscript>

				<cfsavecontent variable="local.name">
					#event.getValue('firstName','')# #event.getValue('lastName','')#
				</cfsavecontent>

				<cfset local.genderName = "">
				<cfif len(event.getTrimValue('gender'))>
					<cfloop array="#local.qryGender.columnValueArr#" index="local.qryGender">
						<cfif local.qryGender.valueid eq event.getTrimValue('gender')>
							<cfset local.genderName = local.qryGender.columnValueString>
							<cfbreak> 
						</cfif>
					</cfloop>
				</cfif>

				<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=event.getTrimValue('mid'))>
				<cfset local.strResult = local.objSaveMember.saveData()>
				<cfset local.useMID = local.strResult.memberID>
				
				<cfset local.listServeNames = event.getValue('listServeNames', '')>
				<cfif len(local.listServeNames)>
					<cfif listFind(local.listServeNames, 'CAALA-WOMEN')>
						<cfset local.qryHistoryWomen = application.objCustomPageUtils.mh_getCategory(siteID=local.siteID, parentCode='JoinList', subName='CAALA-WOMEN')>
						<cfset application.objCustomPageUtils.mh_addHistory(memberID=local.strResult.memberID, categoryID=local.qryHistoryWomen.categoryID, 
							subCategoryID=local.qryHistoryWomen.subCategoryID, description="", enteredByMemberID=local.strResult.memberID, newAccountsOnly=false)>
					</cfif>			
				</cfif>

				<cfset local.listServeCertAgreePrefFieldSet = application.objCustomPageUtils.renderFieldSet(uid=local.listServeCertAgreePrefFieldSetUID, mode="confirmation", strData=arguments.event.getCollection())>
				
				<cfsavecontent variable="local.invoice">
					#local.pageCSS#
					<div class="row-fluid">
						<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
						<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage table">
							<tr class="msgHeader"><td colspan="2" class="b">Personal Information</td></tr>
							<!-- @membernumber@ -->
							<tr class="frmRow2"><td class="frmText b" width="50%">Company Name:</td><td class="frmText">#event.getValue('companyName')#&nbsp;</td></tr>						 
							<tr class="frmRow1"><td class="frmText b">Prefix:</td><td class="frmText">#event.getValue('prefix','')#&nbsp;</td></tr>
							<tr class="frmRow2"><td class="frmText b">Primary Contact Name:</td><td class="frmText">#event.getValue('contactName')#&nbsp;</td></tr>
							<tr class="frmRow1"><td class="frmText b">Primary Contact Phone:</td><td class="frmText">#event.getValue('primaryPhone')#&nbsp;</td></tr>
							<tr class="frmRow2"><td class="frmText b">Primary Contact Email:</td><td class="frmText">#event.getValue('email')#&nbsp;</td></tr>
							<tr class="frmRow1"><td class="frmText b">Address 1:</td><td class="frmText">#event.getValue('address1')#&nbsp;</td></tr>
							<tr class="frmRow1"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city')#&nbsp;</td></tr>
							<tr class="frmRow2"><td class="frmText b">State:</td><td class="frmText">#event.getValue('state')#&nbsp;</td></tr>
							<tr class="frmRow1"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip')#&nbsp;</td></tr>
							<tr class="frmRow2"><td class="frmText b">Website:</td><td class="frmText">#event.getValue('website')#&nbsp;</td></tr>
							<tr class="frmRow1"><td class="frmText b">Referred By:</td><td class="frmText">#event.getValue('referredBy','')#&nbsp;</td></tr>
							<tr class="frmRow2"><td class="frmText b">Gender:</td><td class="frmText">#local.genderName#&nbsp;</td></tr>
							<tr class="msgHeader"><td colspan="2" class="b">Vendor Categories</td></tr>
							<tr class="frmRow1"><td colspan="2" class="frmText"><strong>#event.getValue('vendorCategory','')#</strong>&nbsp;</td></tr>
							<tr class="msgHeader"><td colspan="2" class="b">Optional Items</td></tr>
							<tr class="frmRow1"><td class="frmText b">#local.strDues.enhancedDirectoryListing.txt#:</td><td class="frmText">#event.getValue('enhancedDirectoryListing','No')#&nbsp;</td></tr>
							<cfif len(event.getValue('listServeNames',''))>
							<tr class="frmRow2"><td class="frmText b">List Serves:</td><td class="frmText">#event.getValue('listServeNames','')#&nbsp;</td></tr>
							<tr class="frmRow1"><td class="frmText" colspan="2">#local.customPageFields.ListServeAgreement#</td></tr>
							<tr class="frmRow1"><td class="frmText listServeTable" colspan="2">#local.listServeCertAgreePrefFieldSet.fieldSetContent#</td></tr>
							</cfif>
							<tr><td colspan="2">&nbsp;</td></tr>
							<tr class="msgHeader"><td colspan="2">PAYMENT INFORMATION</td></tr>
							<tr><td class="frmText b frmRow1">Payment Type: </td><td class="frmText">
								<cfif event.getValue('payMeth','CC') EQ 'CC'>
									Credit Card
									<cfset event.setValue('p_#local.profile_1._profileID#_mppid',int(val(event.getValue('p_#local.profile_1._profileID#_mppid',0)))) />
									<cfif event.getValue('p_#local.profile_1._profileID#_mppid') gt 0>
										<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
												mppid     = event.getValue('p_#local.profile_1._profileID#_mppid'),
												memberID  = val(local.useMID),
												profileID = local.profile_1._profileID) />
										- #local.qrySavedInfoOnFile.detail#
									</cfif>
								<cfelse>
									Check by mail
								</cfif>
							</td></tr>
							<tr class="frmRow1"><td class="frmText b">#local.strDues.affiliateDues.txt#:</td><td class="frmText">#DollarFormat(local.strDues.affiliateDues.amt)#&nbsp;</td></tr>
							<cfif #event.getValue('enhancedDirectoryListing','No')# EQ "Yes">
								<tr class="frmRow1"><td class="frmText b">#local.strDues.enhancedDirectoryListing.txt#:</td><td class="frmText">#DollarFormat(local.strDues.enhancedDirectoryListing.amt)#&nbsp;</td></tr>
							</cfif>
							<tr class="frmRow2"><td class="frmText b">Payment Amount: </td><td class="frmText">#dollarFormat(local.totalAmount)#</td></tr>
						</table>
					</div>
				</cfsavecontent>
				<cfset local.invoiceForMember = replace(local.invoice,"<!-- @membernumber@ -->","")>
				<cfset local.invoiceForStaff = replace(local.invoice,'<!-- @membernumber@ -->','<tr class="frmRow1"><td class="frmText b">MemberNumber:</td><td class="frmText"><a href="#arguments.event.getValue("mc_siteinfo.scheme")#://#event.getValue("mc_siteinfo.mainhostname")#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#local.useMID#">#local.strResult.memberNumber#</a>&nbsp;</td></tr>')>

				<cftry>
					<!--- create pdf and put on member's record --->
					<cfset local.uid = createuuid()>
					<cfset local.currentDate = dateTimeFormat(now())>
					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.sitecode)>
					<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
						<cfoutput>
							<html>
							<body>
								<p>Here are the details of your application:</p>
								#local.invoiceForMember#
							</body>
							</html>
						</cfoutput>
					</cfdocument>
					<cfset local.strPDF = structNew()>
					<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
					<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(local.currentDate,'m-d-yyyy')#.pdf">
					<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
					<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(local.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
					<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
					<cfset application.objCustomPageUtils.mem_storeMembershipApplication(memberID=local.useMID, strPDF=local.strPDF, siteID=local.siteID)>
					<cfcatch type="Any">
						<cfset local.tmpCatch = { type="", message="Unable to add document to member record.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
						<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
					</cfcatch>
				</cftry>
				
				<cfset local.emailSentToUser = TRUE />
				
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>Thank you for submitting your application! Please print this page - it is your receipt.</p>	
						<hr />
						<cfif len(trim(local.customPageFields.confirmationEmailTxt))>
							#local.customPageFields.confirmationEmailTxt#	
							<hr />
						</cfif>
						#local.invoiceForMember#	
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=local.memberEmail.from },
                            emailto=[{ name="", email=local.memberEmail.to }],
                            emailreplyto=local.ORGEmail.to,
                            emailsubject=local.memberEmail.SUBJECT,
                            emailtitle="#event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
                            emailhtmlcontent=local.mailContent,
                            siteID=local.siteID,
                            memberID=val(local.useMID),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						  )>

				<cfset local.emailSentToUser = local.responseStruct.success />

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							<p style="color:red;">We were not able to send #local.name# an e-mail confirmation.</p>
						</cfif>
						#local.invoiceForStaff#
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
					local.toEmailArr = listToArray(local.ORGEmail.to,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
				
					local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.ORGEmail.from },
						emailto=local.arrEmailTo,
						emailreplyto=local.ORGEmail.from,
						emailsubject=local.ORGEmail.SUBJECT,
						emailtitle="#event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
						emailhtmlcontent=local.mailContent,
						siteID=local.siteID,
						memberID=event.getValue('mc_siteinfo.sysMemberID'),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					);
				</cfscript>

				<cfset session.invoice = local.invoiceForMember>
				
				<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
			</cfcase>

			<cfcase value="99">
				<div class="HeaderText row-fluid">Thank you for submitting your application!</div>
				<br/>
				<cfif isDefined("session.invoice")>
					<div class="row-fluid">A confirmation has been sent to the e-mail address on file. If you would like you could also print the page out as a receipt.</div>
					<hr />
					<cfif len(trim(local.customPageFields.screenConfirmationTxt))>
						#local.customPageFields.screenConfirmationTxt#	
						<hr />
					</cfif>
					<div class="BodyText row-fluid">
						#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
					</div>
				</cfif>				
				<cfset session.invoice = "" />
			</cfcase>
			<cfcase value="100">
					<div class="row-fluid">
						Error! you Can't Post Here.
					</div>
			</cfcase>
		</cfswitch>
	</div>
</cfoutput>

<cffunction name="getMemberAdditionalData" access="public" returntype="struct">
	<cfargument name="orgID" type="numeric" required="yes">
	<cfargument name="memberID" type="numeric" required="yes">
	<cfset var local = structNew()>
	<cfset local.objMember 								= CreateObject("component","model.admin.members.members") />
	<cfset local.xmlAdditionalData_Member = local.objMember.getMemberAdditionalData(memberid=arguments.memberid) />
	<cfset local.xmlAdditionalData 				= application.objCustomPageUtils.mem_getOrgAdditionalDataColumns(arguments.orgID) />
	<cfset local.returnStruct = StructNew()>
	<cfloop array="#local.xmlAdditionalData.data.XMlChildren#" index="local.column">
		<cfset local.memberColDataActualValue = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@actualColumnValue)")>
		<cfset local.memberColDataValue 			= XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@columnValue)")>
		<cfset local.memberColDataValueID 		= XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@valueID)")>
		<cfset local.columnName 							= local.column.xmlAttributes.columnName />
		<cfset local.returnStruct[local.columnName] = local.memberColDataValue>
	</cfloop>
	<cfreturn local.returnStruct />
</cffunction>

<cffunction name="getMemberData" access="private" returntype="struct">
	<cfargument name="orgID" type="numeric" required="true">
	<cfargument name="siteID" type="numeric" required="true">
	<cfargument name="memberID" type="numeric" required="true">
	
	<cfset var local = structNew() />
	<cfset local.memberData = structNew()>
	<cfset local.qryOrgAddresses = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgEmails = application.objOrgInfo.getOrgEmailTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgWebsites = application.objOrgInfo.getOrgWebsiteTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgPhones = application.objOrgInfo.getOrgPhoneTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgMemberFields = application.objCustomPageUtils.getOrgMemberFields(orgID=arguments.orgID)>

	<cfscript>
		local.returnStruct 								= structNew();
		
		local.objMember 				= CreateObject("component","model.admin.members.members");
		local.memberData.qryMember	=  application.objMember.getMemberInfo(int(val(arguments.memberID)),arguments.orgID);
		local.memberData.qryMemberEmails	=  application.objMember.getMemberEmails(memberID=int(val(arguments.memberID)),orgID=arguments.orgID);
		local.memberData.qryMemberWebsites	=  application.objMember.getMemberWebsites(memberID=int(val(arguments.memberID)),orgID=arguments.orgID);
		local.memberData.qryMemberAddresses	=  local.objMember.getMember_addresses(memberID=int(val(arguments.memberID)),orgID=arguments.orgID);

		local.customMemberData	= getMemberAdditionalData(orgID=arguments.orgID,memberID=arguments.memberID);
	</cfscript>
	
	<cfsavecontent variable="local.memberNamePrinted">
		<cfoutput>
		<cfif local.qryOrgMemberFields.hasprefix is 1 and len(local.memberData.qryMember.prefix)>#local.memberData.qryMember.prefix#</cfif> 
		#local.memberData.qryMember.firstname# 
		<cfif local.qryOrgMemberFields.hasmiddlename is 1 and len(local.memberData.qryMember.middlename)>#local.memberData.qryMember.middlename#</cfif> 
		#local.memberData.qryMember.lastname# 
		<cfif local.qryOrgMemberFields.hassuffix is 1 and len(local.memberData.qryMember.suffix)>#local.memberData.qryMember.suffix#</cfif> 
		<cfif local.qryOrgMemberFields.hasprofessionalsuffix is 1 and len(local.memberData.qryMember.professionalsuffix)>#local.memberData.qryMember.professionalsuffix#</cfif>
		</cfoutput>
	</cfsavecontent>
	
	<cfsavecontent variable="local.memberName">
		<cfoutput>
		#local.memberData.qryMember.firstname# #local.memberData.qryMember.lastname# 
		</cfoutput>
	</cfsavecontent>
	
	<!--- Emails --->
	<cfsavecontent variable="local.memberEmails">
		<cfoutput>
			<cfloop query="local.qryOrgEmails">
				<cfset local.tmpEmailTypeID = local.qryOrgEmails.emailTypeID>
				<cfquery name="local.qryEmailInfo" dbtype="query">
					select email
					from [local].memberData.qryMemberEmails
					where emailTypeID = #local.tmpEmailTypeID#
				</cfquery>		
				#local.qryOrgEmails.emailType#: <cfif len(local.qryEmailInfo.email)>#local.qryEmailInfo.email#<cfelse><span class="dim"><i>not specified</i></span></cfif><br/>
			</cfloop>
		</cfoutput>
	</cfsavecontent>

	<!--- Websites --->
	<cfsavecontent variable="local.memberWebsites">
		<cfoutput>
			<cfloop query="local.qryOrgWebsites">
				<cfset local.tmpWebsiteTypeID = local.qryOrgWebsites.WebsiteTypeID>
				<cfquery name="local.qryWebsiteInfo" dbtype="query">
					select Website
					from [local].memberData.qryMemberWebsites
					where WebsiteTypeID = #local.tmpWebsiteTypeID#
				</cfquery>		
				#local.qryOrgWebsites.WebsiteType#: <cfif len(local.qryWebsiteInfo.Website)>#local.qryWebsiteInfo.Website#<cfelse><span class="dim"><i>not specified</i></span></cfif><br/>
			</cfloop>
		</cfoutput>
	</cfsavecontent>
			
	<!--- addresses, phones --->
	<cfloop query="local.qryOrgAddresses">
		<cfset local.tmpAddressTypeID = local.qryOrgAddresses.addressTypeID />
		<cfset local.tmpAddressType 	= local.qryOrgAddresses.addressType />
		<cfset local.hasAttn 					= local.qryOrgAddresses.hasAttn />
		<cfset local.hasAddress2 			= local.qryOrgAddresses.hasAddress2 />
		<cfset local.hasAddress3 			= local.qryOrgAddresses.hasAddress3 />
		<cfset local.hasCounty 				= local.qryOrgAddresses.hasCounty />
		<cfquery name="local.qryAddressInfo" dbtype="query">
			select attn, address1, address2, address3, city, stateCode, postalcode, county, country
			from [local].memberData.qryMemberAddresses
			where addressTypeID = #local.tmpAddressTypeID#
		</cfquery>	
		<cfsavecontent variable="local.thisaddrFull">
			<cfoutput>
				<cfif local.hasAttn and len(local.qryAddressInfo.attn)>#local.qryAddressInfo.attn#<br/></cfif>
				<cfif len(local.qryAddressInfo.address1)>#local.qryAddressInfo.address1#<br/></cfif>
				<cfif local.hasAddress2 and len(local.qryAddressInfo.address2)>#local.qryAddressInfo.address2#<br/></cfif>
				<cfif local.hasAddress3 and len(local.qryAddressInfo.address3)>#local.qryAddressInfo.address3#<br/></cfif>
				<cfif len(local.qryAddressInfo.city)>#local.qryAddressInfo.city#</cfif> 
				<cfif len(local.qryAddressInfo.stateCode)>#local.qryAddressInfo.stateCode#</cfif> 
				<cfif len(local.qryAddressInfo.postalcode)>#local.qryAddressInfo.postalcode#</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfif local.hasCounty and len(local.qryAddressInfo.county)>
			<cfsavecontent variable="local.thisaddrFull">
				<cfoutput><cfif len(local.thisaddrFull)>#local.thisaddrFull#<br/></cfif>#local.qryAddressInfo.county#</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfif len(local.qryAddressInfo.country)>
			<cfsavecontent variable="local.thisaddrFull">
				<cfoutput><cfif len(local.thisaddrFull)>#local.thisaddrFull#<br/></cfif>#local.qryAddressInfo.country#</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfsavecontent variable="local.thisaddrPhonesFull">
			<cfoutput>
				<table>
				<cfloop query="local.qryOrgPhones">
					<cfset local.phoneTypeID = local.qryOrgPhones.phoneTypeID>
					<cfquery name="local.qryPhoneInfo" dbtype="query">
						select phone
						from [local].memberData.qryMemberAddresses
						where addressTypeID = #local.tmpAddressTypeID#
						and phoneTypeID = #local.phoneTypeID#
					</cfquery>	
					<cfif len(local.qryPhoneInfo.phone)>
						<tr valign="top">
							<td class="tsAppBodyText frmText">#local.qryOrgPhones.phoneType#: &nbsp;</td>
							<td class="tsAppBodyText frmText">#local.qryPhoneInfo.phone#</td>
						</tr>
					</cfif>
				</cfloop>
				</table>
			</cfoutput>
		</cfsavecontent>
	</cfloop>
	<cfset local.returnStruct['Email']							= local.memberData.qryMemberEmails.email />
	<cfset local.returnStruct['memberName'] 				= local.memberName />
	<cfset local.returnStruct['memberNamePrinted'] 	= ReReplace(trim(local.memberNamePrinted),"\s{2,}"," ","ALL")>
	<cfset local.returnStruct['company']						= local.memberData.qryMember.company />
	<cfset local.returnStruct['memberEmails'] 			= local.memberEmails />
	<cfset local.returnStruct['memberWebsites'] 		= local.memberWebsites />
	<cfset local.returnStruct['tmpAddressType']			= local.tmpAddressType />
	<cfset local.returnStruct['thisaddrFull'] 			= local.thisaddrFull />
	<cfset local.returnStruct['thisaddrPhonesFull'] = local.thisaddrPhonesFull />
	<cfset local.returnStruct['customData']					= local.customMemberData />
	<cfset local.returnStruct['qryOrgMemberFields']	= local.qryOrgMemberFields />
	<cfreturn local.returnStruct />
</cffunction>