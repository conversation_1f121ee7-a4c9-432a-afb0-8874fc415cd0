<cfform class="form-horizontal" name="frmLocator" id="frmLocator" action="#arguments.searchPostURL#" method="post" onsubmit="return false;">
<cfinput type="hidden" name="fsid_s"  id="fsid_s" value="#val(local.qryFieldsetInfo.fieldsetID)#">
<div class="tsAppBodyText"><div id="AL_err_div" style="display:none;"></div></div>
<cfset local.showSearchOption = false>
<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
	<div class="control-group">
		<cfoutput>
		<label class="control-label" for="#local.thisfield.xmlattributes.fieldCode#">
			<cfif local.thisfield.xmlattributes.isRequired is 1>*&nbsp;</cfif>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#:
			<cfif local.qryFieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
				<img src="/assets/common/images/help-icon.jpg" onMouseOver="Tip('#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#');" onMouseOut="UnTip();" />
			</cfif>
		</label>
		</cfoutput>
		<div class="controls">
			<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
			<cfcase value="TEXTBOX">
				<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>
					Within 
					<cfselect class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#_radius" id="#local.thisfield.xmlattributes.fieldCode#_radius" >
						<cfloop list="5,10,25,50,100" index="local.thisrad">
							<cfoutput><option value="#local.thisrad#">#local.thisrad#</option></cfoutput>
						</cfloop>
					</cfselect>
					miles of ZIP code 
					<cfinput type="text" id="#local.thisfield.xmlattributes.fieldCode#" name="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="tsAppBodyText" size="8">
				<cfelse>
					<cfinput type="text" id="#local.thisfield.xmlattributes.fieldCode#" name="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="tsAppBodyText">
					<cfset local.showSearchOption = true>
				</cfif>
			</cfcase>
			<cfcase value="RADIO,SELECT,CHECKBOX">
				<cfif ReFindNoCase('ma_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
					<cfset local.qryStates = application.objCommon.getStates()>
					<cfselect class="tsAppBodyText" id="#local.thisfield.xmlattributes.fieldCode#" name="#local.thisfield.xmlattributes.fieldCode#">
						<option value=""></option>
						<cfoutput query="local.qryStates" group="countryID">
							<optgroup label="#local.qryStates.country#">
							<cfoutput>
								<option value="#local.qryStates.statecode#">#local.qryStates.stateName# &nbsp;</option>
							</cfoutput>
							</optgroup>
						</cfoutput>
					</cfselect>
				<cfelse>
					<cfset local.isMultiSelect = local.thisfield.xmlAttributes.dbObjectAlias EQ 'vwmd' AND local.thisfield.xmlAttributes.dataTypeCode NEQ 'BIT'>
					<cfoutput><select class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" <cfif local.isMultiSelect>multiple="multiple"</cfif>></cfoutput>
						<cfif NOT local.isMultiSelect><option value=""></option></cfif>
						<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
							<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
							<cfcase value="STRING">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
								<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueString>
							</cfcase>
							<cfcase value="DECIMAL2">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
								<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueDecimal2>
							</cfcase>
							<cfcase value="INTEGER">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
								<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueInteger>
							</cfcase>
							<cfcase value="DATE">
								<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"m/d/yyyy")>
								<cfset local.thisOptColDisplay = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"m/d/yyyy")>
							</cfcase>
							<cfcase value="BIT">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
								<cfset local.thisOptColDisplay = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
							</cfcase>
							<cfdefaultcase>
								<cfset local.thisOptColValue = "">
								<cfset local.thisOptColDisplay = "">
							</cfdefaultcase>
							</cfswitch>
							<cfoutput><option value="#local.thisfield.xmlAttributes.dbObjectAlias EQ 'vwmd' ? local.thisOpt.xmlAttributes.valueID : local.thisOptColValue#">#local.thisOptColDisplay#</option></cfoutput>
						</cfloop>
					</select>
					<cfif local.isMultiSelect>
						<cfsavecontent variable="local.jQueryMultiselect">
							<cfoutput>
							<script type="text/javascript">
								$(function(){
									setTimeout(function(){
										$("###local.thisfield.xmlattributes.fieldCode#").multiselect({ header: "Choose options below", selectedList: 10, minWidth: 200 });
									},300);
								});
							</script>	
							</cfoutput>
						</cfsavecontent>
						<cfhtmlhead text="#application.objCommon.minText(local.jQueryMultiselect)#">
					</cfif>
				</cfif>
			</cfcase>
			<cfcase value="DATE">
				<cfinput type="text" id="search_#local.thisfield.xmlattributes.fieldCode#" name="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="tsAppBodyText" size="12">
				<cfoutput><a href="javascript:mca_clearDateRangeField('search_#local.thisfield.xmlattributes.fieldCode#');" title="Clear Date"><i class="icon-remove-cirle" style="vertical-align:text-bottom; margin: 0 0 -3px 7px;"></i></a></cfoutput>
				<cfsavecontent variable="local.datejs">
					<cfoutput>											
					<script type="text/javascript">
						setTimeout(function(){ loadCalendar('search_#local.thisfield.xmlattributes.fieldCode#')() },300);
					</script>
					<style type="text/css">
					###local.thisfield.xmlattributes.fieldCode# { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
					</style>
					</cfoutput>
				</cfsavecontent>
				<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">										
			</cfcase>
			</cfswitch>
	<cfif local.thisfield.xmlattributes.isRequired is 1>
		<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
		<cfcase value="TEXTBOX">
			<cfsavecontent variable="local.jsValidation">
				<cfoutput>
				#local.jsValidation#
				if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>";
				</cfoutput>
			</cfsavecontent>
		</cfcase>
		<cfcase value="RADIO">
			<cfsavecontent variable="local.jsValidation">
				<cfoutput>
				#local.jsValidation#
				if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "RADIO", false)) locateErr += '<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>';
				</cfoutput>
			</cfsavecontent>
		</cfcase>
		<cfcase value="SELECT,CHECKBOX">
			<cfsavecontent variable="local.jsValidation">
				<cfoutput>
				#local.jsValidation#
				if (_CF_this['#local.thisfield.xmlattributes.fieldCode#'].options[_CF_this['#local.thisfield.xmlattributes.fieldCode#'].selectedIndex].value.length == 0) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>";
				</cfoutput>
			</cfsavecontent>
		</cfcase>
		<cfcase value="DATE">
			<cfsavecontent variable="local.jsValidation">
				<cfoutput>
				#local.jsValidation#
				if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "DATEFIELD", false) || !_CF_checkdate(_CF_this['#local.thisfield.xmlattributes.fieldCode#'].value, true)) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required and must be a valid date.<br/>";
				</cfoutput>
			</cfsavecontent>
		</cfcase>
		</cfswitch>
		<cfset local.showReqFlag = true>
	</cfif>
		</div>
	</div>

</cfloop>
<br />
<cfif local.showSearchOption>
	<style>
		._accountLocator label{display:block;}
	</style>
	<div class="control-group span12 _accountLocator" role="radiogroup" aria-labelledby="fs_match_label">
		<div style="padding-left:25px;padding-top:5px;">
				Find matches
				<select name="fs_match" id="fs_match">
					<option value="s" <cfif local.qrySettings.defaultSearchOption EQ 's'>selected</cfif>>beginning with</option>
					<option value="c" <cfif local.qrySettings.defaultSearchOption EQ 'c'>selected</cfif>>containing</option>
					<option value="e" <cfif local.qrySettings.defaultSearchOption EQ 'e'>selected</cfif>>exactly matching</option>
				</select>
				the terms I entered.
		</div>
	</div>
	<br>
</cfif>
<cfoutput>
	<div style="padding-left:25px;padding-top:5px;">
		<button name="btn_st_s" id="btn_st_s" type="button" class="btn" onClick="doALSearch()">#arguments.buttonText#</button>
	</div>	
</cfoutput>

<cfif local.showReqFlag>
	<div style="padding-left:25px;padding-top:5px;">
		<br/><i>* required field</i>
	</div>	
</cfif>
</cfform>
