<cfcomponent extends="Bucket">

	<cfset variables.thisBucketTypeID = 52>
	<cfset variables.thisBucketType = "AIDepositionAnalysis">
	<cfset variables.thisBucketCartItemTypeID = 0>

	<cffunction name="showHeader" access="private" output="false" returntype="string">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="bucketSettings" type="xml" required="no" default="<settings/>">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>

		<cfsavecontent variable="local.header">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AIDepositionAnalysis/header.cfm">
		</cfsavecontent>

		<cfreturn local.header>
	</cffunction>

	<cffunction name="showSearchForm" access="public" output="false" returntype="string">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchID" required="no" type="numeric" default="0">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>

		<!--- load search if passed in --->
		<cfset local.strSearchForm = prepSearchForSearchForm(searchID=arguments.searchID, bucketID=arguments.bucketID)>

		<!--- show common JS --->
		<cfset showCommonJS(bucket=variables.thisBucketType, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/search/commonSearchFormJS.cfm">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AIDepositionAnalysis/searchForm.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="prepSearchForSearchForm" access="private" returntype="struct" output="no" hint="parses the searchXML and populates search form">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = structNew()>

		<cfscript>
		local.returnStruct = StructNew();

		if (arguments.searchID gt 0) {
			// convert origin searchXML to bucket searchXML
			local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);

			// read/clean from xml
			local.returnStruct.s_fname = replace(local.searchXML.search["s_fname"].xmlText,chr(34),'','ALL');
			local.returnStruct.s_lname = replace(local.searchXML.search["s_lname"].xmlText,chr(34),'','ALL');
		} else {
			local.returnStruct.s_fname = '';
			local.returnStruct.s_lname = '';
		}

		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="prepSearch" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID)>

		<cfreturn prepSearchFromXML(siteID=arguments.siteID,bucketID=arguments.bucketID,searchXML=searchXML)>
	</cffunction>

	<cffunction name="prepSearchFromXML" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchXML" required="yes" type="xml">

		<cfset var local = structNew()>

		<cfscript>
		// get bucket info to get any restrictions
		local.qryBucketInfo = getBucketInfo(arguments.bucketID);

		// read/clean from xml
		local.s_fname = preparePhraseString(arguments.searchXML.search["s_fname"].xmlText);
		local.s_lname = preparePhraseString(arguments.searchXML.search["s_lname"].xmlText);

		//prepare expertname keywords
		local.keywordsexpertname = "";
		if (Len(local.s_fname))
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_fname,chr(7));
		if (Len(local.s_lname))
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_lname,chr(7));
		local.keywordsexpertname = Replace(local.keywordsexpertname,chr(7)," and ","ALL");

		// return search struct
		local.returnStruct = structNew();
		structInsert(local.returnStruct,"expertFName",replace(local.s_fname,chr(34),"","ALL"));
		structInsert(local.returnStruct,"expertLName",replace(local.s_lname,chr(34),"","ALL"));
		structInsert(local.returnStruct,"keywords","");
		structInsert(local.returnStruct,"expertname",local.keywordsexpertname);
		structInsert(local.returnStruct,"strSettings",{});

		// do i have enough criteria to run a search?
		structInsert(local.returnStruct,"searchAccepted",len(local.keywordsexpertname) gt 0);
		</cfscript>

		<cfset local.fulltextsql = "">
		<cfset local.rankExpressionList = "">
		<cfset local.CRLF = chr(13) & chr(10)>
		<cfif len(local.returnStruct.expertname)>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "inner join containstable(trialsmith.dbo.depodocuments,expertname,@expertsearchterms) as expertsearch on expertsearch.[key] = docs.documentid">
			<cfset local.rankExpressionList = listappend(local.rankExpressionList,"expertsearch.[rank]")>
		</cfif>

		<cfif len(local.fulltextsql)>
			<cfsavecontent variable="local.fulltextsql">
				select distinct docs.documentid, (<cfoutput>#replacenocase(local.rankExpressionList,","," + ","all")#</cfoutput>) as rank
				from dbo.depoDocuments as docs
				<cfoutput>#local.fulltextsql#</cfoutput>;
			</cfsavecontent>
		</cfif>

		<cfset structInsert(local.returnStruct,"fulltextsql",local.fulltextsql)>
		<cfset structInsert(local.returnStruct,"rankExpressionList",local.rankExpressionList)>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResultsCount" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["itemcount"] = 'N/A'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>

		<cfif
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResultsCount(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = StructNew()>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
			<cfset saveBucketCount(searchID=arguments.searchID, bucketID=arguments.bucketID, itemCount=-1)>
		<cfelse>
			<cfset local.cachedItemCount = getCachedBucketCount(searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfif local.cachedItemCount gte 0>
				<cfset StructInsert(local.returnStruct,"itemcount",local.cachedItemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			<cfelse>
				<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID,bucketID=arguments.bucketID,strSearch=local.strSearch)>
				<cfset saveStats(searchID=arguments.searchID, metric='#variables.thisBucketType#.getResultsCount', ms=local.returnStruct.ExecutionTime, itemCount=local.returnStruct.itemCount)>
				<cfset saveBucketCount(searchID=arguments.searchID,bucketID=arguments.bucketID,itemCount=local.returnStruct.itemCount)>
				<!--- Remove structkeys not expected by caller --->
				<cfset structDelete(local.returnStruct, "ExecutionTime")>
			</cfif>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResultsCountForSearchIndex" access="public" output="no" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="firstName" required="true" type="string">
		<cfargument name="lastName" required="true" type="string">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.searchXML">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid>#val(arguments.bucketID)#</bid>
				<s_fname>#xmlFormat(trim(replace(arguments.firstName,chr(34),'','ALL')))#</s_fname>
				<s_lname>#xmlFormat(trim(replace(arguments.lastName,chr(34),'','ALL')))#</s_lname>
			</search>
			</cfoutput>
		</cfsavecontent>
		<cfset local.searchXML = XMLParse(local.searchXML)>

		<cfset local.strSearch = prepSearchFromXML(siteID=arguments.siteID, bucketID=arguments.bucketID, searchXML=local.searchXML)>
		<cfset local.returnStruct = StructNew()>
		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
		<cfelse>
			<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID, bucketID=arguments.bucketID, strSearch=local.strSearch)>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="runResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="strSearch" required="yes" type="struct">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.returnStruct = StructNew()>

		<cfset local.memberGroups = variables.cfcuser_TSgroups>
		<cfquery name="local.qJoinedAndPublicGroups" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select groupid
			from dbo.depogroups
		</cfquery>
		<cfset local.stringJoinedAndPublicGroups = listprepend(valuelist(local.qJoinedAndPublicGroups.groupid),0)>
		<cfset local.stringJoinedGroupsOnly = listprepend(local.memberGroups,0)>

		<cfset local.strResults = createObject("component","Depositions").getSearchResultsCount(strSearch=arguments.strSearch, stringJoinedAndPublicGroups=local.stringJoinedAndPublicGroups, bucketType="AIDepositionAnalysis")>
		<cfset StructInsert(local.returnStruct,"executionTime",local.strResults.qryStat.ExecutionTime)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResults.qryResults.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveSearchForm" access="public" output="no" returntype="numeric" hint="saves the form vars to a search and returns the searchid">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="formvars" required="yes" type="struct">

		<cfset var local = structNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfsavecontent variable="local.xmlSearch">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid><cfif StructKeyExists(arguments.formvars,"bid")>#val(arguments.formvars.bid)#</cfif></bid>
				<s_fname><cfif StructKeyExists(arguments.formvars,"s_fname")>#xmlFormat(trim(replace(arguments.formvars.s_fname,chr(34),'','ALL')))#</cfif></s_fname>
				<s_lname><cfif StructKeyExists(arguments.formvars,"s_lname")>#xmlFormat(trim(replace(arguments.formvars.s_lname,chr(34),'','ALL')))#</cfif></s_lname>
			</search>
			</cfoutput>
		</cfsavecontent>

		<cfset local.searchID = saveSearchXML(val(arguments.formvars.bid),local.xmlSearch)>

		<cfreturn local.searchID>
	</cffunction>

	<cffunction name="getResults" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">
		<cfargument name="viewDirectory" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["errmsg"] = 'Invalid Request'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>

		<cfif
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID)) OR
			(NOT StructKeyExists(arguments, "viewDirectory") OR NOT ListFind(variables.viewDirectories, arguments.viewDirectory))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResults(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResults" access="private" returntype="struct" output="no" hint="searches and returns a query result">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="viewDirectory" required="yes" type="string">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<cfif NOT local.strSearch.searchAccepted>
			<cfreturn showSearchNotAccepted(searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT variables.cfcuser_isSiteAdmin AND val(local.qryBucketInfo.restrictToGroupID) GT 0 AND local.qryBucketInfo.isMemberInRestrictedGroup NEQ 1>
			<cfreturn showNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName,
				accessDeniedMessage=local.qryBucketInfo.accessDeniedMessage, viewDirectory=arguments.viewDirectory)>
		<cfelse>
			<cfset local.noAccessMsg = trim(checkUserRights(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory))>

			<cfset local.executionTime = getTickCount()>
			<cfset local.totalCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID).itemCount>
			<cfset local.executionTime = getTickCount() - local.executionTime>
			<cfset saveStats(searchID=arguments.searchID, metric='#variables.thisBucketType#.getResultsCount', ms=local.executionTime)>

			<!--- return content --->
			<cfsavecontent variable="local.stResults">
				<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AIDepositionAnalysis/results.cfm">
			</cfsavecontent>

			<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>
			<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
			<cfset StructInsert(local.returnStruct,"numTotalPages",1)>
			<cfset StructInsert(local.returnStruct,"numCurrentPage",1)>
			<cfset StructInsert(local.returnStruct,"thisBucketCartItemTypeID",variables.thisBucketCartItemTypeID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.totalCount)>
			<cfset StructInsert(local.returnStruct,"success",true)>

			<cfreturn local.returnStruct>
		</cfif>
	</cffunction>

	<cffunction name="checkUserRights" access="private" output="false" returntype="string">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.data = "">

		<!--- checks.
			1. logged in?
			2. TrialSmithAllowed?
			3. TrialSmithDisabled?
			4. TrialSmithPending?
			5. TrialSmithExpired?
			6. TrialSmithNoPlan?
			7. Economy, Pro and Firm Plan
			--->

		<cfif NOT variables.cfcuser_isLoggedIn>
			<cfset local.data = showNotLoggedInResults(viewDirectory=arguments.viewDirectory)>
		<cfelseif variables.cfcuser_TrialSmithAllowed is not 1>
			<cfset local.data = getUserNoAccessResults(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>
		<cfelseif variables.cfcuser_TrialSmithDisabled is 1>
			<cfset local.data = showTrialsmithDisabled(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID).resulthtml>
		<cfelseif variables.cfcuser_TrialSmithPending is 1
					OR variables.cfcuser_TrialSmithExpired is 1
					OR variables.cfcuser_TrialSmithNoPlan is not 0
					OR variables.cfcuser_TSRights LT 2>
			<cfset local.data = getUserNoAccessResults(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="showNotAllowed" access="private" output="false" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" required="yes" type="string">
		<cfargument name="accessDeniedMessage" required="yes" type="string">
		<cfargument name="includeBucketCount" required="no" type="boolean" default="true">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AIDepositionAnalysis/notAllowed.cfm">
		</cfsavecontent>

		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfif arguments.includeBucketCount>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.strCount.itemCount)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		</cfif>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getUserNoAccessResults" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="searchID" type="numeric" required="yes">
		<cfargument name="bucketID" type="numeric" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var noAccessMsg = "">
		<cfset var qryBucketInfo = getBucketInfo(arguments.bucketID)>

		<cfsavecontent variable="noAccessMsg">
			<cfif variables.cfcuser_TrialSmithAllowed is not 1>
				<cfoutput>#showTrialsmithNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TrialSmithPending is 1>
				<cfoutput>#showTrialsmithPending(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TrialSmithExpired is 1>
				<cfoutput>#showTrialsmithExpired(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TrialSmithNoPlan is not 0>
				<cfoutput>#showTrialsmithNoPlan(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TSRights LT 2>
				<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AIDepositionAnalysis/upgradeUserPlan.cfm">
			</cfif>
		</cfsavecontent>

		<cfset noAccessMsg = ReReplace(noAccessMsg,'\s{2,}',' ','ALL')>

		<cfreturn noAccessMsg>
	</cffunction>

	<cffunction name="handleOneClickPurchase" access="public" output="false" returntype="string">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.searchID = arguments.event.getValue('sid',0)>
		<cfset local.bucketID = arguments.event.getValue('bid',0)>
		<cfset local.expStep = arguments.event.getValue('expStep','expertCaseInfo')>

		<cfset local.strSearch = prepSearch(siteID=arguments.event.getValue('mc_siteinfo.siteID'), searchID=local.searchID, bucketID=local.bucketID)>
		<cfset local.noAccessMsg = trim(checkUserRights(siteID=arguments.event.getValue('mc_siteinfo.siteID'), searchID=local.searchID, bucketID=local.bucketID, viewDirectory=arguments.event.getValue('viewDirectory')))>

		<cfif len(local.noAccessMsg)>
			<cfreturn local.noAccessMsg>
		</cfif>

		<cfset local.dspFile = "expertCaseInfo">

		<cfquery name="local.qryActiveBillingPlan" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT billingPlanID, annualFee, feePerQuestion, feePerUpload
			FROM dbo.depomemberDataCaseBillingPlans
			WHERE isActive = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif NOT local.qryActiveBillingPlan.recordCount>
			<cfreturn "No Active Billing Plan Found.">
		</cfif>

		<cfswitch expression="#local.expStep#">
			<cfcase value="expertCaseInfo">
				<cfquery name="local.qryCaseRef" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT caseReference
					FROM dbo.depomemberdataCases
					WHERE depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.depoMemberDataID#">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfcase>
			<cfcase value="userAgreement">
				<cfset local.dspFile = "userAgreement">
			</cfcase>
			<cfcase value="confirmation">
				<cfquery name="local.qryCaseRef" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @depoMemberDataID int, @billingPlanID int, @caseID int, @caseReference varchar(200),
							@first_name varchar(200), @last_name varchar(200), @expertSlug varchar(400), @expertID int;


						SET @depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.depoMemberDataID#">;
						SET @billingPlanID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryActiveBillingPlan.billingPlanID#">;
						SET @caseReference = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('expCaseName')#">;
						SET @first_name = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSearch.expertFName#">;
						SET @last_name = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSearch.expertLName#">;
						SELECT @expertSlug = membercentral.dbo.fn_Slugify(@first_name + ' ' + @last_name);

						SELECT @caseID = caseID
						FROM dbo.depomemberdataCases
						WHERE depoMemberDataID = @depoMemberDataID
						AND caseReference = @caseReference;

						BEGIN TRAN;
							IF @caseID IS NULL BEGIN
								INSERT INTO dbo.depomemberdataCases (depomemberdataid, caseReference, dateCreated, dateLastUpdated)
								VALUES (@depoMemberDataID, @caseReference, GETDATE(), GETDATE());

								SET @caseID = SCOPE_IDENTITY();
							END

							INSERT INTO dbo.depomemberdataCaseExperts (caseID, agentUserUID, first_name, last_name, expertSlug, billingPlanID)
							VALUES (@caseID, NEWID(), @first_name, @last_name, @expertSlug, @billingPlanID);
							SET @expertID = SCOPE_IDENTITY();
						COMMIT TRAN;

						SELECT @caseID as caseID, @expertID as caseExpertID
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
				<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode)>
				<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName="myDocuments",siteID=local.siteInfo.siteID)>
				<cfset local.dspFile = "confirmation">
			</cfcase>
		</cfswitch>

		<cfif len(local.dspFile)>
			<cfsavecontent variable="local.data">
				<cfinclude template="/views/search/#arguments.event.getValue('viewDirectory')#/buckets/AIDepositionAnalysis/#local.dspFile#.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "No rights.">
		</cfif>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>