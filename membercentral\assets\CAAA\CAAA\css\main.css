/* tsApps styles and overrides */
@import url("/assets/common/css/tsApps.css");

.TitleText {
    font-size: 43px;
    font-weight: 700;
    line-height: 1.3;
    text-align: left;
    color: #1F2A44;
}
.HeaderText {
    color: #1F2A44;
    width: 100%;
    font-size: 37px;
    line-height: 1.2;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 20px;
}
.HeaderTextSmall {
    color: #1F2A44;
    width: 100%;
    font-size: 22px;
    font-weight: 700;
    margin-top: 0;
}
.OrderedListHeading {    
    color: #1F2A44;
    width: 100%;
    font-size: 28px;
    font-weight: 700;
    margin-top: 0;
}
.HighlightHeading {
    color: #1F2A44;
    width: 100%;
    font-size: 22px;
    font-weight: 700;
    margin-top: 0;
}
.ColumnHeader {
    font-size: 22px;
    font-weight: 700;
    color: #404041;
    line-height: 1.2;
    margin-bottom: 0;
    margin-top: 0;
}

.BodyText {
    font-style: normal;
    font-weight: 500;
    font-size: 17px;
    line-height: 1.6;
    color: var(--gray);
}
.BodyTextLarge { font-size: 19px; }
.InfoText { font-size: 13px; }