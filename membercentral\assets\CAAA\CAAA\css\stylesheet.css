

@charset "utf-8";

:root {
    --gray: #1F2A44;
    --lightGray: #636B70;
    --primerycaaa: #C4112F;
    --primeryRCBA: #E9944B;
    --primeryHCBA: #0083A9;
    --montserrat : "Montserrat", sans-serif;
}


body, html {
    margin: 0;
    padding: 0;
    background-color: #fff;
    font-weight: 500;
    font-family: var(--montserrat);
    font-style: normal;
    font-size: 17px;
    line-height: 1.5;
    color: #1D2338;
}
*, input[type="search"] { -moz-box-sizing: border-box; -ms-box-sizing: border-box; -o-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
input { outline: none; }
/* img { max-width: 100%; } */
a {color: #254677;text-decoration: none;}
a:hover, a:focus {color: #b5a886;}


.SectionHeader {
    font-size: 20px;
    font-weight: 700;
    color: var(--gray);
    line-height: 1.2;
}
.SectionHeader b {
    font-weight: 900;
}
.HeaderText {
    color: #1F2A44;
    width: 100%;
    font-size: 37px;
    line-height: 1.2;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 20px;
}
.HeaderText:after {
    display: block;
    content: "";
    height: 3px;
    background: #83B7DE;
    width: 200px;
    margin: 10px 0 0;
}
.HeaderText b {
    font-weight: 900;
}
.HighlightHeading {
    color: #1F2A44;
    width: 100%;
    font-size: 22px;
    font-weight: 700;
    margin-top: 0;
}
.HeaderTextSmall {
    color: #1F2A44;
    width: 100%;
    font-size: 22px;
    font-weight: 700;
    margin-top: 0;
}
.HeaderTextSmall:after {
    display: block;
    content: "";
    height: 3px;
    background: #83B7DE;
    width: 200px;
    margin: 10px 0 0;
}

.HeaderTextSmall a {
    color: inherit;
    text-decoration: underline;
}


.OrderedListHeading {    
    color: #1F2A44;
    width: 100%;
    font-size: 28px;
    font-weight: 700;
    margin-top: 0;
}
.OrderedListHeading:after {
    display: block;
    content: "";
    height: 3px;
    background: #83B7DE;
    width: 200px;
    margin: 10px 0 0;
}


.SubHeading {
    font-size: 14px;
    color: var(--gray);
    font-weight: 700;
    border-bottom: 2px solid #516774;
    padding-bottom: 10px;
}   
.SubHeading b {
    font-weight: 900;
}
.ColumnHeader {
    font-size: 22px;
    font-weight: 700;
    color: #404041;
    line-height: 1.2;
    margin-bottom: 0;
    margin-top: 0;
}

h1, h2, h3, h4, h5, h6 {
    color: #1F2A44;
    line-height: 1.4;
}

p, .BodyText {
    font-style: normal;
    font-weight: 500;
    font-size: 17px;
    line-height: 1.6;
    color: var(--gray);
}
p.BodyTextLarge, .BodyTextLarge { font-size: 19px; }
p.InfoText, .InfoText { font-size: 13px; }

.MontserratMedium{font-family: var(--montserrat) !important; font-weight: 500 !important;}

.TitleText {
    font-size: 43px;
    font-weight: 700;
    line-height: 1.3;
    text-align: left;
    color: #1F2A44;
}
.CAAAButton {
    border: 2px solid #BE6846;
    background-color: #BE6846;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 30px;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 15px;
    font-weight: 700;
    letter-spacing: 1.4px;
    text-transform: uppercase;
    border-radius: 0px;
    color: #ffffff;
    line-height: 20px;
    border-radius: 25px;
}
.CAAAButton:hover,
.CAAAButton.hover {
    background: #DF7D56;
    border-color: #DF7D56;
    color: #ffffff !important;
    text-decoration: none;
}

.CTAButton {
    border: 2px solid #ffffff;
    background-color: #E9944B;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 30px;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 15px;
    font-weight: 700;
    letter-spacing: 1.4px;
    text-transform: uppercase;
    border-radius: 0px;
    color: #ffffff;
    line-height: 20px;
}
.CTAButton:before {
    content: "\f06a";
    font-weight: 900;
    font-family: "Font Awesome 6 Pro";    
    margin-right: 5px;
}
.CTAButton:hover,
.CTAButton.hover {
    background: #E9944B;
    border-color: #E9944B;
    color: #ffffff !important;
    text-decoration: underline;
}

.TextButton {
    color: #BE6846;
    font-size: 15px;
    font-weight: 700;
    text-transform: uppercase;
    display: inline-block;
    padding: 0px 0px;
    border-radius: 10px;
    border: 1px solid rgb(181 168 134 / 0%);
    transition: all 0.3s ease;
}

.TextButton::before {
    content: "\f061";
    margin-right: 7px;
    font-family: "Font Awesome 6 Pro";
    font-weight: 900;
    text-decoration: none;
}

.TextButton:hover, .TextButton.hover {
    color: #e9944b !important;
    text-decoration: underline;
}

.WhiteTextButton {
    color: #fff;
    font-size: 15px;
    font-weight: 700;
    display: inline-block;
    padding: 0px 0px;
    border-radius: 10px;
    border: 1px solid rgb(181 168 134 / 0%);
    transition: all 0.3s ease;
}

.WhiteTextButton::before {
    content: "\f061";
    margin-right: 7px;
    font-family: "Font Awesome 6 Pro";
    font-weight: 900;
    text-decoration: none;
}

.WhiteTextButton:hover, .WhiteTextButton.hover {
    color: #e9944b !important;
    text-decoration: none;
}

.WhiteBorder {
    border: 2px solid #fff;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 30px;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 15px;
    font-weight: 700;
    letter-spacing: 1.4px;
    text-transform: uppercase;
    border-radius: 24px;
    color: #ffffff;
    line-height: 20px;
}
.WhiteBorder:hover,
.WhiteBorder.hover {
    text-decoration: none;
    background: #DF7D56;
    border-color: #DF7D56;
    color: #ffffff !important;
}

.OutLineButton {
    border: 2px solid #E9944B;
    background-color: #ffffff;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 30px;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 15px;
    font-weight: 700;
    letter-spacing: 1.4px;
    text-transform: uppercase;
    border-radius: 0px;
    color: #E9944B;
    line-height: 20px;
}
.OutLineButton:before {
        content: "\f03d";
        font-weight: 900;
        font-family: "Font Awesome 6 Pro";    
        margin-right: 5px;
}
.OutLineButton:hover, .OutLineButton:focus {
    text-decoration: underline;
    color: #E9944B;
}
.default-link {
    color: inherit;
    text-decoration: underline;
}

header .navbar .nav li form .btn-flex {
    align-items: center;
}
.memberSection  .member-boxthree ul {
    margin: 0;
    display: block;
}

.memberSection  .member-boxthree ul li {
    display: block;
    margin-bottom: 5px;
}

.memberSection  .member-boxthree ul li a {
    color: #ffffff;
}
.memberSection  .member-boxthree ul li a.RedButton {
    margin-top: 10px;
}
.memberSection  .member-boxthree ul li a.RedButton:before {
    display: none;
}
.RedButton:hover, 
.memberSection  a.RedButton:hover {
    background: #eca94f;
    border-color: #eca94f;
    color: #ffffff;
    text-decoration: none;
}

.header-drop-title a.learnMoreButton {
    padding: 0 !important;
    color: #05A3C7 !important;
}

.header .navbar .nav li.dropdown .megaMenuSection .header-drop-title h2 {
    color: #2d2d2d;
    margin-top: 0;
}

.memberSection  .member-boxthree .mainMenu .droptitle {
    color: #ffffff;
    margin-top: 50px;
}

.memberSection  .member-boxthree .mainMenu li>a {
    font-size: 18px;
}

.header-drop-title {
    display: table-cell;
    width: 55%;
    padding-right: 50px;
}

.mainMenuMob {
    display: flex;
    width: 100%;
    justify-content: center;
}
.mainMenuMob .mainMenuMob-col {
        display: inline-block;
        float: left;
        width: 25%;
        padding: 0 20px;
}
.mainMenuMob .mainMenuMob-col-50 {
    display: inline-block;
    float: left;
    width: 50%;
    padding: 0 20px;
}
.mainMenuMob-list li .SubHeading {
    margin-bottom: 15px;
    display: block;
    color: #516774;
    text-transform: uppercase;
}
.pd_40 { padding: 40px 0px; }
.pd_50 { padding: 50px 0px; }
.pd_60 { padding: 60px 0; }
.pd_70 { padding: 70px 0; }
.pd_30 { padding: 30px 0; }
.mb-30 { margin-bottom: 30px; }

.gray-bg { background: #DDD8D3; }
.clearfix::before, .clearfix::after { content: ""; display: table; width: 100%; clear: both; }
.container {padding: 0;}
*::-webkit-input-placeholder {  }
*::-moz-placeholder {  }
*:-ms-input-placeholder {  }
*:-moz-placeholder {  }

.xs979 { display: none !important; }
.xs767, .xsVisible { display: none !important; }
.xsHidden979 { display: block !important; }
.xsHidden767, .xsHidden { display: block !important; }
.textUnderline { text-decoration: underline; }
/***Header***/
.printHeader, .printFooter { display: none; }
.header {background: #ffffff;position: fixed;width: 100%;top: 0;z-index: 999;border-bottom: 1px solid #CACBCD;/* display: none; */}
.headerSpace {width: 100%;height: 90px;background-color: transparent;}
.header .navbar {margin-bottom: 0;}
.header .navbar-inner {border: none;-moz-border-radius: 0;-ms-border-radius: 0;-o-border-radius: 0;-webkit-border-radius: 0;border-radius: 0;-moz-box-shadow: none;-ms-box-shadow: none;-o-box-shadow: none;-webkit-box-shadow: none;box-shadow: none;padding: 0;min-height: inherit;background: #ffffff;}
.header .navbar-brand {
    margin-left: 0px;
    float: left;
    max-height: 100%;
    padding: 12px 0px;
    width: 300px;
    /* text-align: center; */
}
.header .navbar .nav li>a {
    font-style: normal;
    font-weight: 600;
    font-size: 15px;
    line-height: 20px;
    text-align: center;
    color: var(--gray);
    text-shadow: none;
    padding: 35px 10px;
}
.header .navbar .nav>li:hover>a, .header .navbar .nav>li>a:hover {color: #BE6846;}

.header .navbar .nav>li.dropdown>a:after {width: 0;height: 0;content: "\f078";position: absolute;top: 35px;z-index: 9;left: auto;right: 20px;margin: 0 auto;bottom: 0px;font-family: "Font Awesome 6 Pro";font-weight: 900;font-size: 75%;}



.header .navbar .nav li:nth-last-child(3) a img,
.header .navbar .nav li:nth-last-child(5) a img { width: 52px; height: 28px; object-fit: contain; display: block; margin: 0 auto; margin-bottom: 15px; }
.header .navbar .nav li:nth-last-child(3) a span { display: block; text-decoration: underline; }
.header .navbar .nav li:nth-last-child(3) {max-width: none;}
.header .navbar .nav li:nth-last-child(3) ul.memberSection ul.mainMenu li { display: block; width: 100%; margin-bottom: 10px; }
.header .navbar .nav li:nth-last-child(3) ul.memberSection ul.mainMenu li a { color: #fff; }
.memberSection li, .memberSection li p, .memberSection li>a { 
    color: #fff; display: inline-block; padding: 0; font-weight: 300; font-size: 16px;
}
.memberSection li p {margin-bottom: 20px;font-size: 18px;}
.memberSection li>a {text-decoration: underline;}
.memberSection li label { font-weight: 300; font-size: 16px;  }
.memberSection li input {
    background-color: #fff;
    border: 0;
    height: 45px;
    border-radius: 0;
    width: 100%;
    margin-bottom: 15px;
    color: #2d2d2d;
    font-weight: 400;
    padding: 0 10px;
}
.memberSection li input:focus {
    box-shadow: none;
}
.memberSection li form a.btn { color: #fff; background: transparent; border: 2px solid #fff; font-size: 13px; font-weight: 500; height: 50px; min-width: auto; text-transform: uppercase; border-radius: 0px;   line-height: 46px; padding: 0; margin: 0; box-shadow: none; text-shadow: none; padding: 0 25px; display: inline-block; width: auto; text-decoration: none; }
.memberSection li form a.btn:hover { background: #fff; color: #2d3e55; }
.memberSection li form .WhiteBorder {/* width: 106px; */float: left;}
font .navbar .nav li.dropdown .memberSection li form a.WhiteBorder { border: 2px solid; font-size: 18px; font-weight: 700; height: 52px; min-width: 120px; text-transform: capitalize; border-radius: 0px;   display: inline-block; vertical-align: middle; line-height: 46px; margin: 0;  box-shadow: none; text-shadow: none; padding: 0 25px; text-align: center; width: auto; font-weight: 400; text-decoration: none; }
.memberSection li form a.WhiteBorder:hover, .memberSection li form a.WhiteBorder:focus { background: #BA0C2F; color: #ffffff; border-color: #BA0C2F; }
.memberSection li form a:last-child {font-size: 11px;text-align: left;padding: 0px;margin-left: 15px;margin-top: 5px;text-transform: inherit;color: #ffffff;text-decoration: underline;}
.header .navbar .nav li.dropdown li>a:hover, .header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li> a:hover, .header .navbar .nav li.dropdown li>a:focus, .header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li >a:focus, .header .navbar .nav li .dropdown-menu>li:hover>a { background: transparent; }
.dropdown li {/* padding: 0 0px; */}
.mainMenuMob-list li {
    margin: 5px 0;
}
.member-boxtwo .btn-wrap .WhiteBorder:not(:last-child) {margin-right: 15px;}
.member-boxtwo .btn-wrap .WhiteBorder {
    margin-bottom: 10px;
}
.header .navbar .nav > li.searchBtnFn > a {margin-right: 20px;}
.header .navbar .nav li.active a { color: #0BBA97; background-color: #ffffff; border-color: #eeeeee; box-shadow: none; }
.header .navbar .nav>li {display: inline-block;padding: 0;position: static;}
.header .navbar .nav li>a:hover, .header .navbar .nav li>a:focus {
     background: transparent;
     color: #BE6846;
     box-shadow: none;
}
.header .nav-collapse.collapse {margin: 0;width: calc(100% - 300px);position: static;display: flex;align-items: center;}
.header .nav-collapse .nav {
    margin: 0;
    float: right;
    width: auto;
    position: static;
    display: flex;
    /* align-items: center; */
    margin-top: 0;
    justify-content: space-around;
    width: 100%;
    padding-left: 1%;
    padding-right: 1%;
    }
.header .navbar .nav>li>.dropdown-menu::after, .header .navbar .nav>li>.dropdown-menu::before { display: none; }
.header .dropdown-menu>li>a {color: #3b3b3c;}
.header .navbar .nav li .dropdown-menu>li>a { padding: 7px 10px; font-size: 11px; line-height: 16px; border-right: none; text-align: left; white-space: normal; }
.header .dropdown-menu { -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; }
.header .dropdown-menu { width: 215px; }

.header .dropdown-submenu>.dropdown-menu { border: none; padding: 0; margin: 0; }

.header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a { border: none; background: rgba(0, 0, 0, 0.1); }
.header .navbar .nav li.dropdown.open>.dropdown-toggle, .navbar .nav li.dropdown.active>.dropdown-toggle, .navbar .nav li.dropdown.open.active>.dropdown-toggle, .navbar .nav li.active>.dropdown-toggle 
{ color: #2d2d2d; background-color: #ffffff;text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
font-weight: 600; border-color: #eeeeee; box-shadow: none; }
 [data-toggle="dropdown"] {
 display: none;
}
.header .navbar .nav li.dropdown .megaMenuSection li a.active {
    color: #354F73;
    font-weight: 700;
}
.dropdown-menu {border-radius: 0;}
.header .navbar .nav li.dropdown li>a { padding: 3px 20px; border: none; margin-bottom: 0px; color: #3b3b3c; line-height: 1.42857; font-size: 14px; font-weight: 700; }
.header .navbar .nav li.dropdown .megaMenuSection .heading { max-width: 215px; margin: 0; top: 50%; transform: translateY(-50%); position: absolute; }
.header .navbar .nav li.dropdown .megaMenuSection .searchHeading { text-transform: uppercase; font-weight: 500; width: 100%; max-width: 308px; text-align: right; }
.header .navbar .nav li.dropdown .megaMenuSection .searchHeading p.TitleText {
    color: #fff;
    border: 0;
    text-shadow: none;
}
.header .navbar .nav li.dropdown .megaMenuSection .heading .TitleText { line-height: normal; color: #fff; line-height: normal; font-size: 38px; font-weight: 500; margin: 0; text-transform: capitalize; border:0; box-shadow: none; }
.formframe {width: 100%;padding: 0;border-radius: 5px;}
.formframe input {float: left;background: #f7f7f7 url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAOCAYAAAD0f5bSAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFmSURBVHgBjVI7TsNAEJ3ZJYoRjTtERbjBigQBnXMDSjrgBk46qsAFgnMCUkLJCeIuIH7mBqYBSlcoKNkdZjc/yzESI+1vdt6bL8JMKipUUsorAlII4CNgQkS90Uu3DwVBu3n11glfVz5n0h89d8/yCumpsAZCxFMG6gHSuRbUEwYfCCFg1oO1rUOcfA7jhSev3r7m87SM0WuEAZAYEEC2rs1OlkSZ1QtegbPQ5rIY1+gpYnaMbY7fUgZzvQXVnEESpVAiRObNGRL5C5B1bS++Cv0ykEDctqdBzJY6Lq3zJERYBNgiMemRM9Q6WYaHepoLQqe62w5zgACkGLgQge7y4U/71Ghf8E9nkQeHbJPPv40wzfFj5LxJu00+hjH34p2viml4GsAjYiDCDQNSfiskPK5s7t9Ovu4zLOZR2QuVPTfGkM77whPT56B4aiDl1jRXQH9Jtd565aJZwlT8F/SjqckFSWyCv0wrhb9anqj3AAAAAElFTkSuQmCC);border: 0;color: #33383A;background-position: left 20px center;width: calc(100% - 140px);background-repeat: no-repeat;font-size: 15px;height: auto;display: inline-block;margin: 0;height: 40px;box-shadow: none;outline: none;padding: 0 15px 0 50px;font-weight: 300;font-style: italic;}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input::-webkit-input-placeholder {color: #33383A; }
.header .navbar .nav li.dropdown .megaMenuSection .formframe input::-moz-placeholder {color: #33383A;}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input:-ms-input-placeholder {color: #33383A; }
.header .navbar .nav li.dropdown .megaMenuSection .formframe input:-moz-placeholder {color: #33383A;}
.formframe .btnWhite {float: right;color: #fff;background: #be6846;border: 2px solid #be6846;font-size: 14px;font-weight: 900;height: 40px;min-width: auto;text-transform: uppercase;line-height: 34px;margin: 0;box-shadow: none;text-shadow: none;padding: 0 25px;display: inline-block;width: auto;border-radius: 50px;}
.formframe a:hover {background: var(--lightGray) !important;color: #fff !important;border: 2px solid var(--lightGray);text-decoration: none;}
#searchbox11 #s_key_all::placeholder{color: #fff !important;}
.header .navbar .nav li.dropdown .megaMenuSection .heading .btn { color: #fff; background: transparent; border: 2px solid #fff; font-size: 13px; font-weight: 500; height: 50px; min-width: auto; text-transform: uppercase; border-radius: 0px;   line-height: 46px; padding: 0; margin: 0; box-shadow: none; text-shadow: none; padding: 0 25px; margin-top: 20px; display: inline-block; width: auto; }
.header .navbar .nav li.dropdown .megaMenuSection .heading .btn:hover { background: #fff; color: #2d3e55; border-color: #fff; }
.header .navbar .nav li.dropdown .megaMenuSection li a {
    color: #30313C;
    text-decoration: none;
    text-align: left;
    font-weight: 500;
    font-size: 15px;
    text-transform: capitalize;
    padding: 5px 0px;
    display: block;
}
.memberSection  .member-boxthree p.HeaderText {
    font-size: 22px;
    margin: 55px 0 10px;
    text-transform: none;
    font-weight: 500;
    border-bottom: 1px solid rgb(255 255 255 / 50%);
    display: block;
    padding-bottom: 10px;
    margin-bottom: 15px;
}
.header .nav-collapse .nav .dropdown.headerlogin>ul.memberSection {
    padding: 25px 40px 35px 425px;
    background: #354F73;
}
.header .navbar .nav li.dropdown .megaMenuSection li a:hover,
.header .navbar .nav li.dropdown .megaMenuSection li a:focus {color: #BE6846;text-decoration: underline;}
.header .navbar .nav li.dropdown .megaMenuSection li .subMenu { padding-left: 20px; }

header .navbar .nav li form p {
    font-size: 14px;
    color: #ffffff;
    text-transform: uppercase;
    width: 100%;
    font-weight: 700;
    margin: 0 0 2px;
}
header .navbar .nav li form .pwd-input,
header .navbar .nav li form .login-input {
    border: 1px solid #aeaeae;
    height: 33px;
    flex: 0 0 calc(50% - 50px);
    max-width: calc(50% - 50px);
    margin: 0;
    background-color: transparent;
    border-radius: 0;
    border-color: #ffffff;
    color: #ffffff;
    box-shadow: none;
}
.searchBtnFn .default { display: block; }
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection {
    display: inherit;
    margin: 0 !important;
}
body .formDiv {
    /* display: inherit; */
    /* width: 100%; */
    /* max-width: 100%; */
    /* margin: 0; */
    /* padding: 32px 80px 25px 20px; */
    /* position: relative; */
}
.memberSection  .member-boxone {
    width: 30%;
    /* background: #ffffff; */
    /* position: absolute; */
    /* top: 0; */
    /* left: 0; */
    /* height: 100%; */
    /* text-align: center; */
    /* display: flex !important; */
    /* align-items: center; */
    /* justify-content: center; */
    /* padding: 0; */
    margin: 0 !important;
}
.megaMenuSection.model-bg {
    position: absolute;
    width: calc(100% - 370px);
    height: 100%;
    top: 0;
    right: 0;
    z-index: -1;
}

.megaMenuSection.model-bg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.15;
    position: absolute;
    z-index: 1;
}
.memberSection  .member-boxtwo {
    width: 40%;
    margin-left: 0 !important;
}
.memberSection  .member-boxthree {
    width: 30%;
    margin-left: 0 !important;
    padding-left: 5%;
}
.memberSection  .member-boxthree ul li>a {
    padding: 5px 0;
}

.memberSection  .member-boxthree ul {
    margin: 15px 0;
}
.memberSection li.member-boxtwo a.SANButton {
  margin: 20px 15px 0 15px;
}
.close-form {
    position: absolute;
    top: 25px;
    right: 30px;
    z-index: 1;
    color: #ffffff;
    padding: 0;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
}
.toggle-form i.fa.fa-times {
    font-size: 25px;
    vertical-align: middle;
    margin-left: 5px;
}
.header .navbar-inner .headerlogin .dropdown-menu {
    display: none;
    padding: 40px 80px;
    border: none;
    border-top: 5px solid #c4112f;
    top: 68px;
}

.header .headerlogin.show-form .dropdown-menu {
    display: flex;
}


.social-mobile, .mobile-links { display: none; }
.btns-wrap {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}
header .navbar .nav li form a.SATLAButton:hover {
    background: #ffffff;
    border-color: #ffffff;
}

.formframe form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin: 0;
}
.navbar .container {
    width: 100%;
    position: relative;
    max-width: 1440px;
    padding: 0 40px;
}
header .navbar .nav li.headerlogin {
    background: #05A3C7;
    width: 185px;
    min-height: 116px;
    padding: 0;
}


.header .navbar-inner .row-fluid {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.header .navbar-inner .row-fluid:before, .header .navbar-inner .row-fluid:after {
    display: none;
}
header .navbar .nav li form input.login-input {
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAVCAYAAABLy77vAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEbSURBVHgBpZQBEcIwDEVTDgGTUAdMAhImAQk4AAdIYA44FAwHDAUFBeCgJPDLhW7rVvh3vdyW9PWTlBlKyHtfcCjx2BpjHpQjBlheje9qD/hkyB0bJZ4j6HkSjIscNtR6Aw4Iue0YZIlCl3AbnH65mkW1S8RjH4ibfeXQ8hKITYGCUtMJueE+sd0VrDcDeauangQVamKbntwhXAMak3L1ciYT4rVTBzhxRlMEmPNdNaMQjH6vizlWcLSWvHrXoNbGkE108ipx4CGqrUJCT0JOL0ecW7ivseceEuvJk+hCHfaWciEt3l8oXy2iFdACD1fK102Dij9An7/LnN4/SRos0zhRnqqPCf/9nflFtZBMQPv3hbOUJ/mOvxr+BDf719dvV9PeAAAAAElFTkSuQmCC');
    background-repeat: no-repeat;
    background-position: center left 8px;
    padding-left: 35px;
}

header .navbar .nav li form input.pwd-input {
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADqSURBVHgBnZIBEYMwDEXTKaiESaiDgYM5KA42B+BgErgpQMIkgIPhgDnIwvi79VivDfy7f21peE3aECnEzFZci5/800NchXFGAXIydGIrvosHzOfvXjyKS2PMmAMdkU0/Z5faJ0VWLYJt5kBelxwLzActcd18h6kAB5ijPKwSTwfKayKdbAo2Yiwpr5N4SMEuGF+JmM8DyFDQ0jbRgFpz+WjmHi9+VINkfcZFO/gKyGaQ539N6MM8CKf7ANSgrAJ78UaOZYQyvmpII01GpNX6B5nfNme0glXB2u8CBbCWl2evd4MCGO8uLdAb5j6QM8wR6hAAAAAASUVORK5CYII=');
    background-repeat: no-repeat;
    background-position: center left 8px;
    padding-left: 35px;
}
form a:last-child {
    font-weight: 500;
}
.searchBtnFn>.dropdown-toggle {
    padding: 10px;
    color: var(--gray);
    font-size: 16px;
}
li.searchBtnFn {
    display: inline-block;
    position: initial;
}

li.searchBtnFn  .dropdown-menu.row-fluid {
    display: none;
}
li.searchBtnFn .dropdown-menu.row-fluid .formDiv {
    margin: 0 auto;
    float: none;
    width: 100%;
    padding: 0 70px 0 0;
}
.rightMenus {
    display: inline-block;
}
li.headerlogin {
    position: initial;
    display: inline-block;
}

.rightMenus>ul {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    gap: 20px;
}
.header-top {
    background-color: var(--gray);
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 100%;
    padding: 0 0 0 70px;
}

.header-top .headerleft ul {
    display: flex;
    gap: 0;
    list-style: none;
    padding: 0;
    margin: 0;
}

.header-top .headerleft ul li a {
    color: #ffffff;
    padding: 6px 10px;
    font-size: 13px;
    font-weight: 500;
    border: 1px solid #636B70;
    margin-left: -1px;
    display: inline-block;
}
.header-top .container:before, .header-top .container:after {
    display: none;
}

.header-top .headerright {
    display: inline-flex;
    justify-content: flex-end;
    color: #ffffff;
    align-items: center;
    gap: 15px;
}

.header-top .headerright p {
    color: #DCDDDE;
    margin: 0;
}

.header-top .headerright  .btn-logout {
    display: inline-block;
    padding: 5px 15px;
    color: #ffffff;
    font-size: 15px;
    font-weight: 700;
    text-transform: uppercase;
    width: 200px;
    border-left: 1px solid #DCDDDE;
}
.header-top .headerright .btn-logout i {
    margin-right: 5px;
}

/*-------heroBanner-----***/
.heroBanner {position: relative;}
.heroBanner {background-repeat: no-repeat;background-size: cover;background-position: center;min-height: 100%;position: relative;padding: 0;background: #1F2A44;overflow: hidden;min-height: 480px;}

.heroBanner img {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
    object-fit: cover;
    top: 0;
    left: 0;
    opacity: 0.1;
}
.heroBanner .owl-dots {position: absolute;left: 50%;transform: translateY(-50%);display: flex !important;flex-direction: row;margin: 0;bottom: 0;max-width: 1170px;justify-content: flex-end;}
.owl-theme .owl-dots .owl-dot {outline: none;background: transparent;margin: 5px;}
.owl-theme .owl-dots .owl-dot span {
    background: #ffffff;
    margin: 0;
}
.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
    background: #ffffff;
}
.carousel-caption {background: transparent;max-width: 1230px;margin: 0 auto;position: relative;top: auto;left: 0;padding: 0;z-index: 2;bottom: 0;min-height: 340px;display: flex;justify-content: start;align-items: flex-end;}
.captionFrame {padding: 75px 0 0;color: #ffffff;flex: 1 1 47%;width: 47%;max-width: 47%;}
.captionFrame ul li:nth-child(3) {line-height: 1.5;font-weight: 500;margin: 0 0 15px;font-size: 17px;}
.captionFrame ul li:nth-child(1) {
        border: 0;
        text-shadow: none;
        text-align: left;
        display: inline-block;
        padding: 0;
        position: absolute;
        z-index: 1;
        left: 46%;
        transform: translate(-50%, 0px);
        border-radius: 50%;
}
.captionFrame ul li:nth-child(1) .banner-img-wrap {
    width: 880px;
    height: 880px;
    left: -15px;
}

.captionFrame ul li:nth-child(1):before {content: "";width: 900px;height: 900px;border: 3px solid #ffffff;display: block;position: absolute;left: -29px;top: -38px;border-radius: 50%;opacity: 0.21;}
.captionFrame ul li:nth-child(1) img {
    opacity: 1;
    position: relative;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    height: 55%;
    z-index: 0;
}
.captionFrame ul li:nth-child(2) {
        /* font-size: 43px;
        font-weight: 700;
        line-height: 1.3;
        border: 0;
        text-shadow: none;
        text-align: left;
        width: auto;
        display: inline-block;
        padding: 0; */
        color: #ffffff;
        margin-bottom: 15px;
}
.captionFrame ul li:nth-child(2) .TitleText {
    color: #ffffff;
}
.captionFrame .WhiteBorder {
    display: inline-flex;
    padding: 12px 30px;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
}
.captionFrame ul li:nth-child(4) {
    display: flex;
    align-items: center;
    gap: 15px;
}
.captionFrame li {
    line-height: 1.4;
}
.captionBtnBox {position: absolute;right: 0;width: auto;width: 100%;height: 100%;top: 0;right: 0;left: auto;margin: 0 auto;}
.captionBtnBox .container {
    position: relative;
    width: 100%;
    max-width: 1500px;
    display: flex;
    justify-content: end;
    padding: 0;
    height: 100%;
}

.captionBtnBox .captionBtnFrame ul {
    margin: 0px;
}
.captionBtnFrame {
    background-color: rgb(35 44 55 / 60%);
    position: relative;
    right: 0;
    width: 425px;
    height: 100%;
    padding: 25px;
    top: 0;
    z-index: 1;
}
.captionBtnBox ul li {width: 100%;overflow: hidden;position: relative;margin-bottom: 25px;}
.captionBtnBox ul li:last-child { margin-bottom: 0px; }
.captionBtnBox ul li a {
    padding: 10px 22px;
    display: flex;
    align-items: center;
    width: 100%;
    background: rgba(255, 255, 255, 0.0);
    border-radius: 10px;
    border: 1px solid #FFF;
    min-height: 77px;
    color: #FFF;
    font-size: 20px;
    
    font-weight: 600;
    line-height: 110%;
}
.captionBtnBox ul li a:hover {background: var(--lightblue);border-color: var(--lightblue);color: #ffffff;}
.captionBtnBox ul li a .iconBox { width: 50px; float: left; margin: 0px 0px; text-align: center; }
.captionBtnBox ul li a .iconBox img {margin: 0 auto;padding-top: 2px;filter: contrast(0)brightness(100);width: 40px;height: 40px;object-fit: contain;}
.captionBtnBox ul li a .iconBox svg path {
    fill: #ffffff;
}

.captionBtnBox ul li a .iconBox img.default { display: block; }
.captionBtnBox ul li a .textBox {
    position: absolute;
    left: 100px;
    top: 50%;
    transform: translateY(-50%);
    max-width: 200px;
    overflow: hidden; 
}
.captionBtnBox ul li a .textBox h2 {
    margin: 0;
    padding: 0;
    color: #fff;
    font-size: 20px;
    font-weight: 500;
    line-height: 1.3;
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
.captionBtnBox ul li a .arrow { 
    float: right;
    padding: 19px 0;
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    color: #ffffff;
    font-size: 20px;
 }
 .captionBtnBox ul li a:hover .textBox h2,
 .captionBtnBox ul li a:hover .arrow  {
    color: #ffffff;
} 
.captionBtnBox ul li a:hover .iconBox svg path {
    fill: #2d2d2d;;
}
.captionBtnBox ul li a:hover .iconBox img {
    filter: none;
}
.captionBtnBox.captionBtnBox-mb { display: none; }

/* QUick Links  */
.caaa-tabs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
}
.caaa-tabs .btn {
    font-size: 15px;
    font-weight: 700;
    color: var(--gray);
    padding: 9px 20px;
    background: transparent;
    border-style: none;
    box-shadow: none;
}
.caaa-tabs .btn:first-child {
    padding-left: 0;
}
.caaa-tabs .btn:last-child {
    padding-right: 0;
    border: none;
}
.caaa-tabs span {
    height: 40px;
    background: #B1B5B7;
    display: inline-block;
    width: 1px;
}
.caaa-tabs .btn.active {
    color: var(--primeryRCBA);
}

.tab-wrap {
    display: flex;
    align-items: center;
}

.tab-wrap .img-wrap {
    flex: 0 0 48%;
    max-width: 48%;
    position: relative;
    align-self: flex-start;
}
.tab-wrap .img-wrap img {
    width: 100%;
}
.tab-wrap .content-wrap {
    flex: 0 0 52%;
    max-width: 52%;
    padding-left: 80px;
}
.tab-inner-wrap {
    border-left: 5px solid #E1E3E5;
    padding-left: 40px;
}

.tab-wrap .img-wrap .TitleText {
    position: absolute;
    bottom: 0;
    z-index: 0;
    color: #fff;
    left: 0;
    width: 100%;
    text-align: center;
}
.tab-inner-wrap p {
    /* font-size: 17px; */
}
.tab-inner-wrap .CAAAButton {
    margin-top: 10px;
}

.left-icon-title {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 40px;
}
.left-icon-title img {
    flex: 0 0 42px;
    max-width: 42px;
    height: 42px;
    object-fit: contain;
    margin-right: 10px;
}
.left-icon-title .HeaderTextSmall,
.left-icon-title .HeaderText {
    margin: 0;
    padding-left: 5px;
    align-self: center;
}

.all-post-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 15px;
    font-weight: 700;
    text-decoration: none;
}
.all-post-link:hover {
    text-decoration: none;
}
.flex-1 {
    flex: 1 1 auto;
}
.left-icon-title .d-flex .HeaderText {
    flex: 0 0 auto;
    width: auto;
    display: inline-block;
}
.left-icon-title .d-flex.flex-1 {
    gap: 20px;
}
.quick-link-box span.left-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 70px;
    height: 70px;
}


.quick-link-box {
    flex: 0 0 100%;
    max-width: 100%;
    position: relative;
    padding-left: 85px;
}
.quick-link-box p {
    line-height: 1.3;
    font-size: 15px;
}
.quick-link-box .HeaderTextSmall {
    margin: 0 0 5px;
}
.quick-link-box:hover {
    text-decoration: none;
}

.quick-links-wrapper {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
}
.quick-links-wrapper li {
    flex: 0 0 50%;
    max-width: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 20px 45px;
    border: 1px solid #E1E3E5;
    margin: -1px -1px 0px 0px;
    min-height: 260px;
}


.quick-link-box:hover span.left-icon img:first-child {
    filter: brightness(1);
    opacity: 1;
}

.quick-link-box:hover span.left-icon img:nth-child(2) {
    opacity: 1;
}
.quick-link-box span.left-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    margin: 0;
    filter: brightness(0);
    opacity: 0.6;
}
.quick-link-box:hover .HeaderTextSmall {
    color: #BE6846;
}
.quick-link-right-box {
    position: relative;
    padding-left: 100px;
}

.quick-link-right-box:before {
    content: "";
    position: absolute;
    width: 4px;
    height: 100%;
    background: #516774;
    left: 50px;
}

.quick-link-right-box .HeaderText {
    margin: 0 0 30px;
    line-height: 1.1;
}
.quick-link-right-box p {
    margin: 0 0 30px;
}
.quick-links-sec {
    padding-top: 1px;
}
.quick-links-sec .container {
    padding: 0 15px;
}


.quick-links-sec .container .span8 {
    flex: 0 0 66%;
    max-width: 66%;
    margin: 0;
}

.quick-links-sec .container .span4 {
    flex: 0 0 34%;
    max-width: 34%;
    margin: 0;
    align-self: center;
}
.d-flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.d-flex .left-icon-title {
    flex: 1 1 auto;
}

.d-flex .left-icon-title img {
    width: 50px;
    height: 50px;
    flex: 0 0 30px;
}

.important-dates-sec {
    background: #BE6846;
}
.important-dates-sec .btn-wrap {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 30px;
    gap: 15px;
}

/* QUick Links  */


/* importantDates  */

.date-card {
    background: #BE6846CC;
    padding: 20px;
    box-shadow: 0 0 0px rgb(106 116 124 / 50%);
    height: 100%;
    border: 1px solid #ffffff;
}

.date-card-wrap .date-card-col>a:hover {
    text-decoration: none;
}
.date-card-wrap .date-card-col>a .date-card:hover .dc-date {
    color: #FFFFFF;
}

.date-card-wrap .date-card-col>a .date-card:hover {
    background: #ffffff;
}

.date-card .dc-date {
    text-align: center;
    font-size: 19px;
    font-weight: 900;
    color: #fff;
    line-height: 1.2;
    font-family: 'Montserrat';
    text-align: left;
    margin: 0 0 10px;
    text-transform: uppercase;
}

.date-card .dc-date span {
    display: block;
    font-size: 30px;
    font-weight: 700;
}

.date-card p {
    margin: 0;
    color: #fff;
    font-size: 18px;
    font-weight: 700;
    line-height: 1.4;
}
.date-card:hover .dc-content p:not(.InfoText) {
    text-decoration: underline;
}
.date-card-wrap {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -12px;
}

.date-card-wrap .date-card-col {
    flex: 0 0 25%;
    max-width: 25%;
    margin-bottom: 40px;
    padding: 0 12px;
}
.date-card .dc-content {padding: 10px 0 0;}
.date-card-wrap .date-card-col>a .date-card:hover .dc-date {
    color: #be6846;
}

.date-card-wrap .date-card-col>a .date-card:hover p {
    color: #BE6846;
}

.important-dates-sec .span8 {
    margin: 0;
    flex: 0 0 61%;
    max-width: 61%;
    padding-top: 30px !important;
    padding-bottom: 30px !important;
    align-self: center;
}

.important-dates-sec .span4 {
    margin: 0;
    width: 39%;
    flex: 0 0 39%;
}

.important-dates-sec .row.d-flex-wrap>div {padding: 0 15px;}
.important-dates-sec .HeaderText {
    color: #ffffff;
    margin: 0;
}
.date-card p.InfoText {
    margin-top: 20px;
    font-size: 11px;
    font-weight: 700;
    display: flex;
    gap: 10px;
}
.date-card p.InfoText i {
    font-size: 12px;
    margin-top: 2px;
}
.img-card {
    padding: 40px;
    background-color: #ffffff;
}
.img-card .SubHeading {
    margin: 0 0 10px;
}

.img-card img {
    margin-bottom: 15px;
}

.img-card .CAAAButton {
    margin-top: 10px;
}
.mr-5 {
    margin-right: 5px;
}
.date-card:hover {box-shadow: 0 0 12px rgb(106 116 124 / 50%);}


/* ENd importantDates  */

/* PRESIDENT */
.img-left-card {
    display: flex;
    flex-wrap: wrap;
    background: #E9944B;
}
.img-left-card .ilc-img {
    flex: 0 0 270px;
    max-width: 270px;
    position: relative;
    z-index: 1;
    background: #ffffff;
    display: flex;
    align-items: flex-end;
}
.img-left-card .ilc-img img {
    width: 100%;
}
.img-left-card .ilc-content {
    flex: 0 0 calc(100% - 270px);
    max-width: calc(100% - 270px);
    align-self: center;
    padding-left: 45px;
    padding-top: 30px;
    padding-bottom: 30px;
}
.img-left-card .ilc-content .ilc-content-inner {
    border-left: 5px solid #ffffff;
    padding-left: 40px;
    padding-right: 30px;
    color: #ffffff;
}
.img-left-card .ilc-content .ilc-content-inner .HeaderTextSmall,
.img-left-card .ilc-content .ilc-content-inner p {
    color: #ffffff;
    margin: 0 0 15px;
}
.mt-15 {
    margin-top: 15px;
}
.img-left-card .ilc-img:before {
    content: "";
    background: #F1BE93;
    position: absolute;
    right: 0;
    width: 47%;
    height: 100%;
    z-index: -1;
}
/* END PRESIDENT */
/* adv sec  */
.adv-sec .w-100 {
    width: 100%;
}

/* adv sec  */

/* numberInfo */
.numberInfo {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    margin: 0;
}

.numberInfo>li {
    font-size: 50px;
    flex: 0 0 50%;
    max-width: 50%;
    text-align: center;
    line-height: 1;
    color: #2B2F32;
    padding: 40px 20px;
}

.numberInfo>li:nth-child(1), .numberInfo>li:nth-child(3) {
    border-right: 1px solid rgb(99 107 112 / 50%);
}

.numberInfo>li:nth-child(1), .numberInfo>li:nth-child(2) {
    border-bottom: 1px solid rgb(99 107 112 / 50%);
}

.numberInfo>li .InfoText {
    display: block;
    font-weight: 700;
}
.left-img-card {
    display: flex;
}
.left-img-card .li-img {
    flex: 0 0 380px;
    max-width: 380px;
}
.left-img-card .li-content {
    flex: 0 0 calc(100% - 380px);
    max-width: calc(100% - 380px);
    padding-left: 40px;
    align-self: center;
}
/* numberInfo */


/* border-card  */

.border-card {
} 
.border-card .date-box {
    font-size: 14px;
    color: #516774;
    letter-spacing: 0.04em;
    font-weight: 700;
    margin: 0 0 15px;
    display: block;
}
.border-card .SectionHeader {
    margin: 0 0 20px;
}
.mb-20 {
    margin-bottom: 20px;
}
.border-card ~ .border-card {
    margin-top: 30px;
}
.border-card .ColumnHeader {
    margin-bottom: 10px;
}
.border-card:hover {
    text-decoration: none;
}
.border-card:hover .ColumnHeader {
    text-decoration: underline;
    color: #BE6846;
}
.border-card .news-img {
    height: 195px;
    width: 100%;
    position: relative;
    margin-bottom: 20px;
}
.border-card .news-img .read-more-btn {
    display: inline-flex;
    gap: 10px;
    align-items: center;
    position: absolute;
    top: 55%;
    left: 50%;
    background: #BE6846;
    padding: 8px 15px;
    color: #ffffff;
    border-radius: 50px;
    font-weight: 800;
    font-size: 15px;
    transform: translate(-50%, -50%);
    width: max-content;
    opacity: 0;
    transition: all 0.3s ease;
}
.border-card:hover .news-img .read-more-btn {
    opacity: 1;
    top: 50%;
}
.border-card .news-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.border-card:hover .news-img img {
    opacity: 0.2;
}

.news-sec {
    background: radial-gradient(101.31% 173.23% at 98.89% 101.31%, rgba(81, 103, 116, 0.05) 0%, #FFFFFF 79.93%)

}
.news-sec .row.d-flex-wrap {
    margin-left: -25px;
    margin-right: -25px;
}

.news-sec .row.d-flex-wrap .span3 {
    flex: 0 0 25%;
    max-width: 25%;
    margin: 0;
    padding-left: 25px;
    padding-right: 25px;
}
/* border-card  */

/* anchore-list-sec  */
.anchore-list-sec {
    
}
.anchore-list {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 80px;
}
.anchore-list li a {
    text-transform: uppercase;
    color: #2B2F32;
    font-weight: 500;
    font-size: 17px;
}
.anchore-list li a:hover,
.anchore-list li a:focus {
    color: #C4112F;
    text-decoration: underline;
}
.left-bordered-box {
    border-left: 5px solid #C4112F;
    padding-left: 40px;
}
.quicklink {
    display: block;
    position: relative;
}
.quicklink:hover {
    text-decoration: none;
}
.quicklink .SectionHeader {
    text-transform: uppercase;
    font-size: 14px;
    margin: 15px 0 5px;
}
.quicklink:hover .SectionHeader {
    color: #E9944B;
}
.quicklink .ql-icon  {
    position: relative;
}
.quicklink .ql-icon  .hover-icon {
    opacity: 0;
    position: absolute;
    left: 0;
    top: 0;
}
.quicklink:hover .ql-icon  .hover-icon {
    opacity: 1;
}
.quicklink:hover .ql-icon  img:not(.hover-icon) {
    opacity: 0;
}
.login-form input[type="text"], .login-form input[type="password"] {
    background: #F2F2F2;
    border-color: #F2F2F2;
    border-radius: 0;
    padding: 8px 15px;
    height: auto;
    font-size: 13px;
    color: #2B2F32;
    margin-bottom: 15px;
    display: block;
    width: 100%;
}
.login-form input:focus {
    box-shadow: none;
}
.forgot-link {
    color: #2B2F32;
    font-size: 13px;
    line-height: 1.2;
    margin: 0 0 20px;
    display: block;
}

.not-a-member {
    text-align: center;
    padding: 40px 20px;
    background: rgb(225 227 229 / 20%);
}

.not-a-member .SubHeading {
    margin: 0 0 10px;
}
.header-top .headerleft ul li a.active {
    background: #c4112f;
}
.mb-0 {
    margin-bottom: 0 !important;
}
.anchore-list .openList {
    display: block;
}
.anchore-list.open-droupdown  .openList {
    display: none;
}












/* anchore-list-sec */
.owl-carousel .owl-nav button.owl-prev span, .owl-carousel .owl-nav button.owl-next span {color: #e3d9c5;font-size: 40px;padding: 0;width: 30px;display: inline-block;line-height: 1;height: 30px;margin-top: 0;}
.owl-carousel .owl-nav button.owl-prev, .owl-carousel .owl-nav button.owl-prev:hover {
    position: absolute;
    top:50%;
    margin: -12px 0 0 0;
    left:0;
    background: #ffffff;
    height: auto;
    display: inline-block;
    z-index: 9;
}
.owl-carousel .owl-nav button.owl-next, .owl-carousel .owl-nav button.owl-next:hover {
    right: 0;
    position: absolute;
    top: 50%;
    margin: -12px 0 0 0;
    background: #ffffff;
    height: auto;
    display: inline-block;
    z-index: 9;
}
/*--------Become A Member---------***/
.member-boxleft {
    display: inline-block;
    vertical-align: middle;
    width: 67%;
}
.member-boxright {
    display: inline-block;
    vertical-align: top;
    padding-left: 18px;
    margin-top: 70px;
}
.member-right {
    border-left: 1px solid #0C1F4F;
    padding-left: 50px;
}
.member-boxleft h3, .member-boxright h3 {
    margin: 0 0 15px 0;
}

/* ________Footer________ */
.contact-links ul li i{color: #83B7DE;font-size: 22px;flex: 0 0 50px;width: 50px;text-align: center;}
.copyright{text-align:center;}
.footer-links h3{font-size: 22px;color:#83B7DE;margin:0 0 15px 0;font-weight: 700;line-height:110%;font-family:var(--montserrat);}
.footer-links ul li{position:relative;padding-left:0;margin-bottom: 8px;color:#ffffff;font-size:16px;line-height:1.25;font-weight:400;font-family:var(--montserrat);}
.footer-links ul li a,.contact-links ul li a{font-size: 15px;color:#ffffff;line-height:1.25;background:transparent;text-shadow:none;font-family:var(--montserrat);font-weight: 500;display: block;}

.footer-links ul li a:before {
    content: "\f105";
    font-family: "Font Awesome 6 Pro";
    position: absolute;
    font-weight: 900;
    left: 0;
    top: 0px;
    opacity:0;
    font-size: 0;
}
.footer-links ul li a:hover:before {
    opacity:1;
    font-size: 14px;
}
.footer-links ul li a:hover {
    padding-left: 15px;
}
.footer-links ul li a:hover,.footer-links ul li a:focus,.contact-links ul li a:hover,.contact-links ul li a:focus{text-decoration:underline;}
.footer{background: #1F2A44;background-repeat:no-repeat!important;background-size:cover!important;padding:40px 0 0;overflow: hidden;} 
.footer .row.d-flex-wrap>div.col1{-webkit-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;position:relative;z-index:1;padding-right:15px;padding-left:15px;}
.footer .row.d-flex-wrap>div.col2{-webkit-flex: 1 1 75%;flex: 1 1 75%;}
.footer .footer-links{padding:0 20px;position:relative;flex:0 0 25%;max-width:25%;}
ul.social-list li a:hover{background:#83B7DE;text-decoration:none;padding-left: 6px !important;}

.copyright-block{padding:20px 0;margin-top:20px;}
.footer-links-wrap{display:flex;flex-wrap:wrap;padding-left:20px;border-left: 1px solid #FFFFFF;margin:0 -20px;}
.footer-title{font-size:30px;font-weight:600;line-height:115%;color: #83B7DE;font-family:var(--montserrat);margin:0 0 15px;}
.contact-links li{color:#ffffff;margin-bottom:20px;list-style:none;font-size:14px;font-family:var(--montserrat);font-weight:600;line-height:1.4;letter-spacing:0.56px;}
.contact-links ul{margin:0;padding:0;}
.contact-links li{display:flex;gap:12px;}
.contact-links li a:hover {}
.contact-links li i{flex:0 0 30px;width:30px;}
.copyright ul{margin:.0;color:#ffffff;list-style:none;padding:0;display:flex;flex-wrap:wrap;justify-content:center;font-size:16px;font-family:var(--montserrat);font-weight:400;line-height:125%;letter-spacing:0.56px;}
.copyright a{color:#ffffff;}
.copyright li:not(:last-child):after{content:"|";margin:15px;}
.social-list{width:auto;z-index:1;list-style:none;margin:0;padding:0;}
.footer .footer-links ul{list-style:none;margin:0;padding:0;}
ul.social-list{display:flex;}
ul.social-list li a{display:inline-flex;align-items:center;width: 50px;height: 50px;justify-content:center;align-items:center;text-align:center;color: #ffffff;border-radius: 50%;}
.contact-links ul.social-list li a i {color: #ffffff;}
.contact-links ul.social-list li a:hover i {color: #1f2a44;}
ul.social-list li{margin-right:15px;}
.footer .footer-links ul + h3{margin-top:30px;}

.row.d-flex-wrap {
    margin-left: -15px;
    margin-right: -15px;
}

.d-flex-wrap {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.row.d-flex-wrap:before,
.row.d-flex-wrap:after {
    display: none;
}

/*-----------------------------Inner Page CSS----------------------------***/

/*--- Banner Inner --**/

.fixed-bg {
    width: 100%;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.08;
    z-index: -1;
    object-fit: cover;
}
.bannerInner {
    position: relative;
    z-index: 2;
    background: #1F2A44;
    overflow: hidden;
}
.bannerInner .TitleText {
    color: #ffffff;
}
.bannerInner p {
    color: #ffffff;
}
.bannerInner .btn-wrap {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 20px;
    gap: 15px;
}

.bannerInner .breadcrumb {
    background: transparent;
    text-decoration: none;
    padding: 0;
    margin-left: -2px;
}

.bannerInner .breadcrumb .breadcrumb-separator {
    text-shadow: none;
    color: #ffffff;
}
.bannerInner .breadcrumb .breadcrumb-separator i {
    font-size: 14px;
    color: #83B7DE;
}
.bannerInner .breadcrumb .breadcrumb-item {
    text-shadow: none;
    font-size: 14px;
    letter-spacing: 0.04em;
    margin: 0 2px;
}
.bannerInner .breadcrumb .breadcrumb-item:first-child a {
    display: inline-flex;
}
.bannerInner .span6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    margin: 0px;
    padding-left: 15px;
}

.circle-wrap {
    position: relative;
    display: block;
    width: 100%;
    left: -30px;
}
.circle-wrap:before {
    content: "";
    width: 750px;
    height: 750px;
    border: 3px solid #FFFFFF;
    display: inline-block;
    position: absolute;
    top: -36px;
    left: -26px;
    z-index: 2;
    border-radius: 50%;
    opacity: 0.3;
}

.banner-img-wrap {
    border: 0;
    text-shadow: none;
    text-align: left;
    display: inline-block;
    padding: 0;
    position: absolute;
    z-index: 1;
    opacity: 1;
    top: -25px;
    left: -15px;
    width: 735px;
    height: 735px;
    border-radius: 50%;
    overflow: hidden;
}
.banner-img-wrap .banner-img {
    position: absolute;
    width: 100%;
    height: 370px;
    object-fit: cover;
    top: -40px;
    left: 0;
    z-index: 1;
    opacity: 1;
}


.flex-sec .flex-row>.col {
    padding: 0 25px;
    -webkit-flex: 0 0 33.33%;
    -moz-flex: 0 0 33.33%;
    -ms-flex: 0 0 33.33%;
    flex: 0 0 33.33%;
}
.cntnr1070 .container {
    max-width: 1070px;
}
.eventbox-info .eventbox-item:not(:last-child):after {
    background: #ffffff;
    height: 1px;
    display: block;
    margin: 10px 0;
    content: "";
}
.sec-pd40 {
    padding-top: 40px;
    padding-bottom: 40px;
}
.sec-pd70 {
    padding-top: 70px;
    padding-bottom: 70px;
}
.fb-wid-logo-wrap {
    display: flex;
    align-items: center;
    flex: 1 1 auto;
}
.fb-wid-logo-wrap .fb-right-wrap {
    flex: 0 0 calc(100% - 70px);
    max-width: calc(100% - 70px);
    padding-left: 14px;
    text-align: left;
}
.fb-wid-logo-wrap img {
    flex: 0 0 70px;
    -webkit-flex: 0 0 70px;
    max-width: 70px;
}
.fb-wid .fb-wid-body .fb-wid-wrap img {
    width: 100%;
}
.flex-sec .flex-row {
    flex-wrap: nowrap;
    margin: 0 -25px;
}

.fb-wid .fb-wid-body {
    height: 500px;
    overflow: auto;
    padding: 0 10px;
}
.fb-wid .fb-wid-body::-webkit-scrollbar {
    width: 4px;
    background-color: #f3f3f3;
}
.fb-wid .fb-wid-body::-webkit-scrollbar-track {
    box-shadow: inset 0 0 2px #b4b4b4;
  }
.fb-wid .fb-wid-body::-webkit-scrollbar-thumb {
    background-color: #b4b4b4;
  }
.fb-wid .fb-wid-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30px 22px 20px;
}
.follow-us-btn {
    display: inline-block;
    padding: 8px 20px;
    border: 2px solid #333C59;
    color: #ffffff;
    background: #333C59;
    border-radius: 0px;
    font-size: 15px;
    font-weight: 700;
    text-transform: uppercase;
    text-decoration: auto;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
}
.follow-us-btn:hover {
    color: #ffffff;
    text-decoration: none;
    background: #932e26;
    border-color: #932e26;
}
.fb-wid {
    box-shadow: 10px 10px 30px rgb(0 0 0 / 16%);
    border: 1px solid #E3D9C5;
    background: #ffffff;
}

.fb-wid .fb-wid-body .fb-wid-wrap {
    margin-bottom: 0;
}

.fb-wid-logo-wrap .fb-right-wrap h4, .fb-wid-logo-wrap .fb-right-wrap p {
    margin: 0;
    color: #2D3550;
}

.fb-wid-logo-wrap .fb-right-wrap h4 {
    font-size: 19px;
    font-weight: 700;
}

.fb-wid-logo-wrap .fb-right-wrap p {
    font-size: 16px;
    padding: 0 !important;
}
.flex-row .col-4 {
    flex: 0 0 33.33%;
    max-width: 33.33%;
    padding: 0 15px;
}

/********************/
.quicklink-mobile { display: none; }
.events {margin-top: 40px;display: block;}
.sponsors-box {
    background: #CFD2D9;
    text-align: center;
    padding: 23px 20px;
}
.sponsors-box span {
    background: #fff;
    padding: 4px 12px;
    color: #6C6C6C;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    position: relative;
    margin-top: 28px;
    display: inline-block;
}
.sponsors-box span small {
    font-size: 24px;
    color: #000;
    font-weight: 500;
    position: absolute;
    bottom: -22px;
    left: 0;
    right: 0;
    margin: 0 auto;
}
.sponsors-boxtwo {
    display: block;
    text-align: center;
    margin: 50px 0 30px 0;
    padding: 0 25px;
}
.sponsors-boxthree {
    background: #E9E9E9;
    display: block;
    text-align: center;
    padding: 38px 20px;
    margin:0 25px;
}
.sponsors-link ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}
.sponsors-link ul a {
    display: inline-block;
    padding: 15px 40px;
    background: #E3D9C5;
    border: 1px solid #B5AB97;
    margin-left: -1px;
    font-size: 18px;
    font-weight: 700;
    color: #41464b;
    text-decoration: none;
    position: relative;
    min-width: 210px;
}

.sponsors-link ul li.active a {
    background: #FFFFFF;
    border-color: #B5AB97;
}       

.sponsors-link {
    margin-bottom: 70px;
}
.sponsor-with-fb-wid .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
    max-width: 1530px;
    margin: 0 auto;
}
.sponsor-with-fb-wid .left-tabwrap {
    width: 100%;
    max-width: 630px;
    margin: 0 auto;
}
.sponsor-with-fb-wid .left-col {
    flex: 0 0 calc(100% - 425px);
    max-width: calc(100% - 425px);
    padding: 0 15px;
}
.sponsor-with-fb-wid .right-col{
    flex: 0 0 425px;
    max-width: 425px;
    padding: 0 15px;
}
.sponsors-sec.sponsor-with-fb-wid p {
    padding: 0 8%;
}
.rightMenus .logout-btn a {
    display: inline-block;
    background: #be6846;
    padding: 2px;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.friendsheroBannerBox ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
}

.friendsheroBannerBox ul li {
    margin: 0 20px 15px;
    text-align: center;
}
.sponsor-with-fb-wid .fb-wid .fb-wid-body {
    height: 380px;
}

.events .friendsLogoBox {
    display: none;
}
.inner-content-area > p {
    margin: 15px 0 20px 0;
}
.inner-page-content .inner-content-area .container {
    max-width: 100%;
    width: 100%;
    padding: 0 !important;
}
.Highlight .HeaderTextSmall {margin-bottom: 10px;}

.BulletList ul {margin-bottom: 35px;margin-left: 0;list-style: none;}
.BulletList ul li {position: relative;padding-left: 25px;margin-bottom: 10px;font-size: 18px;position: relative;/* background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAADNQTFRFJy0zJy0zJy0zJy0zAAAA7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpDeINaQgAAABF0Uk5Te1BTTwChOEK0KHGnJq2oVU/PX2cTAAAAPUlEQVR4nGNgYGRCAowMDMwsKICZgQlVgIl4AVY2NAF2Dk40LVzcqAI8vOyoAnz8aGYIkO4OhACG59C9DwAFgQPSfsrqegAAAABJRU5ErkJggg=='); */background-position: left center;background-size: contain;background-repeat: no-repeat;color: #2D2D2D;background-size: 16px;background-position: top 2px left;}
.BulletList ul li::before {
    content: '';
    position: absolute;
    top: 7px;
    left: 0;
    width: 7px;
    height: 7px;
    border: 1px solid #354F73;
    border-radius: 0;
    background: transparent;
    transform: rotate(45deg);
}
.BulletList ul ul {
    margin-top: 12px;
    margin-bottom: 5px;
}
.BulletList-row .BulletList ul li {
    margin-bottom: 18px;
}
/*---Main Content Div----***/
.header .navbar .nav li.dropdown .megaMenuSection .TitleText span {
    
    color: #E3D9C5;
    margin-right: 10px;
    font-weight: 700;
}
/* sbm Clases */
.sponsors-sec {
    position: relative;
} 
.inner-content-area .sponsors-sec {
    background: #eee8dc;
    margin-right: -50px;
}
.inner-content-area .sponsors-sec:before {
    content: "";
    background: #eee8dc;
    position: absolute;
    top: 0;
    left: -65px;
    height: 100%;
    width: 66px;
}
.sponsors-img-list ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
}
.sponsors-img-list ul li {
    padding: 15px;
}
.infoicon-sec {

width: 100%;
}
.flex-row {
    display: flex;
    flex-wrap: wrap;
    width: auto;
    margin: 0 -15px;
}
.flex-row:before,.flex-row:after {
    display: none;
    margin: 0;
}
.pt-0 {
    padding-top: 0px !important;
}
.pb-0 {
    padding-bottom: 0px !important;
}
.upcoming-event-sec .flex-row {
    justify-content: space-between;
    margin-left: -15px;
    margin-right: -15px;
}
.mt-40 {
    margin-top: 40px !important;
}

.header .navbar .nav>li.searchBtnFn:hover .dropdown-toggle:after {
    display: none !important;    
}

body .header .searchBtnFn.show-search-bar>ul {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 999;
}

.searchBtnFn.dropdown>ul.dropdown-menu {
    margin: 0;
    background: #ffffff;
    left: auto;
    right: 236px;
    width: calc(100% - 590px);
    padding: 0;
    height: 66px;
    top: 10px;
    border-style: none;
}

.searchBtnFn .megaMenuSection {
    padding: 15px 75px 15px 15px;
    width: 100%;
}

.header .navbar .nav li a.nav-member-center {
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 116px;
    margin-left: auto;
    margin-right: 0;
    padding: 0;
    position: relative;
    z-index: 1;
}
.headerlogin .nav-member-center {
    display: inline-flex;
    width: auto;
    align-items: center;
    font-weight: 500;
    gap: 10px;
    color: #ffffff;
    background: #BE6846;
    min-width: 140px;
    justify-content: center;
    padding: 10px 15px;
    border-radius: 25px;
}
.headerlogin .nav-member-center:hover, .rightMenus .logout-btn a:hover {
    background: #DF7D56;
}
.headerlogin .nav-member-center:hover ,
.headerlogin .nav-member-center:active,
.headerlogin .nav-member-center:focus {
    text-decoration: none;
}
.headerlogin .nav-member-center p {
    color: #ffffff;
    font-weight: 800;
    font-size: 15px;
    margin: 0;
    line-height: 1.2;
    text-transform: uppercase;
    font-family: "Merriweather Sans", sans-serif;
}
.memberSection  .member-boxthree .nav-member-center p {
    display: block !important;
    font-weight: 500 !important;
    letter-spacing: 0.05em !important;
    font-size: 14px !important;
}
.headerlogin .memberSection 
 .nav-member-center p {
    margin: 0 0 0 10px !important;
}
.searchnav-logo {
    padding: 24px 40px 14px 40px;
}
.nav-member-center p {
    color: #ffffff;
    margin: 10px 0 0;
}
.nav-member-center img {
    width: 32px;
}
.memberSection  .member-boxthree .nav-member-center img {
    width: 22px;
    filter: contrast(0)brightness(100);
}

.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.member-center-wrap {
    width: 215px;
} 
.formframe a.searchclose {
    background: transparent !important;
    color: #9A8D83;
    padding: 0;
    border: none;
    display: inline-flex;
    align-items: center;
    position: absolute;
    right: 0;
    top: 50%;
    right: 30px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 1;
    text-transform: none;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a.searchclose svg {
    margin-left: 8px;
}
header .top-strip ul li:not(:last-child):after {
    content: "|";
    display: inline-block;
    color: #05A3C7;
}
header .top-strip ul li {
    font-size: 12px;
    line-height: 1.2;
}
.inner-page-content {
    position: relative;
    margin: 0 auto;
    padding: 60px 20px;
}
.inner-page-content .inner-content-area {
    padding: 0px 15px 0 35px;
    margin-bottom: 0;
    width: 100%;
    flex: 0 0 calc(100% - 300px);
    max-width: calc(100% - 300px);
    margin: 0;
}
.inner-page-content .sidebar {
    flex: 0 0 400px;
    width: 400px;
    padding: 20px;
    left: 0;
    top: 0;
    margin: 0;
    bottom: 0;
}
.sidebar .eventbox-item-in {
    border-radius: 0;
    border-style: none;
    padding: 15px 10px;
}
.inner-page-content>.row-fluid {
    display: flex;
    flex-flow: row-reverse;
    flex-wrap: wrap;
}

.inner-page-content>.row-fluid {
    flex-flow: row-reverse;
    width: auto;
    margin: 0 -20px;
}
.quicklink-desktop h3, .events h3 {
    text-transform: uppercase;
    color: #fff;
    font-size: 22px;
    letter-spacing: 0.1em;
    margin: 0 0 25px 0;
}
.sidebar .eventbox-row {
    flex-direction: column;
}
.sidebar .eventbox-col {
    width: 100%;
    margin: 0 0 20px 0;
}
.events { margin-top: 40px; }
.sidebar-iconbox {
    display: flex;
    margin: 10px 0;
    align-items: center;
    position: relative;
    padding: 0 0 0 20px;
    border: 1px solid #ffffff;
    border-radius: 10px;
    color: #616161;
    font-weight: 500;
    font-size: 15px;
    font-family: var(--montserrat);
}
.sidebar-iconbox:last-child {
    margin-bottom: 5px;
}
.sidebar-iconbox:hover {
    background: #ffffff;
    border-color: #ffffff;
    text-decoration: none;
}
.sidebar-iconbox .iconBox {
    margin-right: 20px;
    filter: brightness(100);
    opacity: 1;
}
.sidebar-iconbox:hover .iconBox {
    filter: brightness(0);
    opacity: 0.5;
}
.sidebar-iconbox .iconBox img {
    height: 35px;
}
.eventbox-col.eventthree .eventbox-info {
    /* padding: 0 15px; */
}
.sidebar-iconbox .textBox h2 {
    font-size: 19px;
    color: #ffffff;
    font-weight: 600;
    line-height: 100%;
    margin: 0;
    
}
.sidebar-iconbox:hover .textBox h2 {
    color: var(--blue);
}
.sidebar-iconbox .arrow {
    position: absolute;
    right: 20px;
}
.sidebar .eventbox-item p {
}
.sidebar .eventbox-item ul li {
    font-size: 14px;
    text-transform: inherit;
}
.sidebar .eventbox-item ul li a {
    font-size: initial;
}

/*Left content*/
.content-info p {
    margin: 20px 0;
}
.Highlight {
    background: #83B7DE26;
    padding: 25px 30px;
    font-size: 22px;
    color: #1F2A44;
}
.Highlight a {
    color: #194b97;    
}

.Highlight h3 {
    margin:0 0 10px 0;
}
.Highlight a:hover {
    color: #0054A5;
}
.membership-headlinebox h5 {
    text-decoration-line: underline;
    color: #BA0C2F;
    font-size: 22px;
    font-weight: 600;
}
.Highlight .btns-wrap {
    display: flex;
    gap: 15px;
}
.membership-headlinebox p {
    margin:20px 0;
}
.forgot-mb {
    display: none;
}
.primary-btnmb { display: none; }
.headtitle { display: none; }

/*-------eventbox Css----------*/
.eventbox {
    padding: 40px 0;
    background: #DBD5CD;
}
.eventbox-row {
    display: flex;
    flex-direction: column;
}
.eventbox-col {
    margin-right: 0;
    width: 100%;
    border: 1px solid #D5D5D5;
    margin-bottom: 30px;
}
.eventbox-col:last-child {
    margin-right: 0px;
}
.eventone {
    height: 100%;
    border-radius: 10px;
    background: #2311B6;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25);
    overflow: hidden;
}
.eventone .event-head {
    background: #2311B6;
}
.eventtwo {
    border-radius: 10px;
    background: #1B365D;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25);
    height: 100%;
}
.eventtwo .event-head {
    background: #1B365D;
}
.eventthree {
    border-radius: 10px;
    background: #05A3C7;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25);
    height: 100%;
}
.eventthree .event-head {
    /* background: #E3D9C5; */
}
.eventbox-img {
    height: 85px;
    overflow: hidden;
    position: relative;
}
.eventbox-img span img {
    width: auto;
    height: 100%;
    max-width: inherit;
}
.event-head {
    position: absolute;
    z-index: 9;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    top: 0;
    left: 0;
    height: 85px;
    border-bottom: 1px solid #D5D5D5;
}

.event-head h4 {
}
.eventone .event-head h4, .eventtwo .event-head h4, .eventthree .event-head h4, .eventone .event-head .ColumnHeader {
    color: #ffffff;
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
.event-head h4 b {
    font-weight: 900;
}
.event-head h4::before {
    content: '';
    position: absolute;
    width: 100px;
    height: 3px;
    background: #fff;
    bottom: -20px;
    left: 0;
    right: 0;
    margin: 0 auto;
}
.eventbox-info {
    padding: 10px 30px 20px;
}
.eventbox-item {
    margin: 0;
}
.eventtwo .eventbox-item {
    border-bottom: 1px solid #2E3752;
}
.eventtwo .eventbox-item ul li:before,
.eventtwo .event-link i {
    color: #ffffff;
}
.eventbox-item:hover .eventbox-item-in {background: #ffffff;}
.eventbox-item-in {
    padding: 15px 15px;
    margin-top: 0;
    border: 1px solid #ffffff;
    border-radius: 6px;
}
.eventbox-item-in p {
    padding: 0 15px;
}
.eventbox-item ul {
    display: flex;
    list-style: none;
    margin: 0 0 8px;
    justify-content: center;
}
.eventbox-item ul li {
    font-size: 14px;
    color: #FFFFFF;
    position: relative;
    padding: 0 20px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.56px;
    opacity: 0.85;
}
.eventbox-item ul li:first-child {
    padding-left:0;
}
.eventbox-item ul li:last-child {
    padding-right: 0;
}
.eventbox-item ul li i {margin-right: 5px;font-size: 12px;vertical-align: middle;}
.eventbox-item ul li:before {
    content: '|';
    position: absolute;
    height: 25px;
    right: -2px;
    top:0;
    color: #ffffff;
}
.eventbox-item ul li:last-child:before {
    display: none;
}
.eventbox-item ul li img { margin-right: 5px; }
.eventbox-item ul li img.hover-img { display: none; }
.eventbox-item:hover ul li img.active-img { display: none; }
.eventbox-item:hover ul li img.hover-img { display: inline-block; }
.eventbox-item p {
    color: #fff;
    font-size: 19px;
    margin: 5px 0 0 0;
    font-weight: 600;
    line-height: 1.3;
    text-align: center;
    
}
.eventbox-item p a {
    color: inherit;
    text-decoration: none;
}
.eventbox-item:hover ul li {color: #333C59;}
.eventbox-item:hover p {color: #333C59;}
.eventbox-item.eventbox-item-link {
    border: 0;
    padding: 20px 0px 0;
}
.event-link {
    color: #fff;
    margin: 0;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 900;
    letter-spacing: 1.4px;
}
.event-link i {
    color: #ffffff;
    margin-left: 5px;
    vertical-align: middle;
}
.event-link:hover,
.event-link:focus {
    color:#fff;
    text-decoration: none;
}
.weight-500  {
    font-weight: 500;
}
.mb-20 {
    margin-bottom: 20px;
}
.side-title-center {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(87, 96, 102, 0.30);
}
.owl-theme .owl-dots .owl-dot span {
    background-color: transparent;
    width: 12px;
    height: 12px;
    border: 2px solid #ffffff;
}
.info-iconbox h2>a {
    color: inherit;
    text-decoration: none;
}

.info-iconbox:hover span img {
    -webkit-filter: brightness(100);
    filter: brightness(100);
}
.d-inline-block {
    display: inline-block   ;
}
blockquote, blockquote.pull-right {
    padding: 20px 0px;
    font-size: 23px;
    font-weight: 400;
    color: #1F2A44;
    position: relative;
    border: 1px solid #516774;
    border-style: solid none;
    font-family: "Merriweather", serif;
    letter-spacing: 0.04em;
}
blockquote p {
    outline: none;
    box-shadow: none;
    opacity: 1;
    font-size: inherit;
    font-weight: inherit;
    font-style: inherit;
    color: inherit;
}
blockquote:after {
    content: "";
    position: absolute;
/*     font-size: 43px;
    line-height: 0;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAnCAMAAABzEUvRAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAR1QTFRF7jpD7jpD7jpDAAAA7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD5+3DlQAAAF90Uk5TECACAAgMgP9AYA9TUDhwHNPy/rBD7bo53MonG8viFU2y+o+RVtkG7G+1P4Ux9vwLFNtKPOvHHtD5IpJuEnXxriu/DXT4ZaSvARG+CuQkqB9ijAnwk9esL+G4gwUYHSNEeUGlAAABM0lEQVR4nO3T51bCQBAF4DVriKIoIAo2FEWxEUGxi6DYFQv2+v6PoUJmlszuyAvk/ry530nZEyG6aCxJ0q1NbBFyaARlPdqkN2ABCxhJuDPrMzCqDKxfRAa88GwQJlHF8GLMq+IawwwBS+hsmGcjwJLILKhSPBuFzRiycagmeDYJmzSyKaimWZWGScZCNgPdLMuyMJmTwOZz0C2wDL/IIjJ1bEucSuJkGdhKHip3lVGFIrI1j62XsNpg1OYWTrbDTbaz62Ll7BnRfvlATSpSimroMK4aJ3dkeLxa5bht4kR+GfkDTgy3In/AqdTY2XlHlrrQ2OWV6cV87LouKbsxKh+L3krCSndG1M7c+4b0s2LZZhSyh8c6VH8s//Rce+FMi2Ve394TH6oSn1+Nf0Qz9rd2mD+0SDzF1VlHkwAAAABJRU5ErkJggg==');
    background-repeat: no-repeat;
    background-size: contain;
    display: inline-block;
    width: 55px;
    height: 55px;    
    bottom: 18px;
    right: -36px;
    z-index: 1; */
}
blockquote:before {
    content: "";
    position: absolute;
/*     font-size: 43px;
    line-height: 0;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAnCAMAAABzEUvRAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAARdQTFRFAAAA7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD7jpD1msM4AAAAF10Uk5TAA0jDAkdGAVDg7jh+v9AL3Ss1/aAJJPwdduM/GLrH9MKqOS+EdwBr6RlOPiuFOz+vyvxEm6SIg/50B7HPEoLMYU5P3C1um8G2VaRjwIVsk3iyxsnyu3ysFAcEDADmVdMZQAAATJJREFUeJzt1tlOAkEQheFGEFEpBRkURAQVURHBHXdE3AXcd9//OTTQVT3pmra98HLqjj/nSwgDCUJoF+gL6km/UH/Y9WogMjg0HAUY+UWMxuJjCQCHQnJ8AnpnZMFUWk6QhSYzABY2laWJZNM5AAvLz7gmPZacBRvLz4HOCvNgY8UFYGwRrGwJGCu5w3I5tsJVoOKaVFedtZ9WVmU9teH5eWyqSWZru5tqO5R29zyREPs0yR7IdEgp7fH2undEDzVRx3ZMrGFQ4oQmTWqnmM5MSpzjpHJB7RLblZFd4+SGUiuKrW1kHZzcUmpjujMqcY+bB0qPmJ7MjL58LUoN+ycinuXkRaUIslczq3L2hizXlPfOmHps8kqK0fFfAJs4f2EfPvOZz/6H1T71Y/8Uvtgk/A3VRmI7Ed0c7QAAAABJRU5ErkJggg==');
    background-repeat: no-repeat;
    background-size: contain;
    display: inline-block;
    width: 55px;
    height: 55px;    
    left: -18px;
    top: 20px; */
}

blockquote.pull-right:before {
    left: auto;
    right: -19px;
}

blockquote.pull-right:after {
    right: auto;
    left: -35px;
}

blockquote.pull-right:before,
blockquote.pull-right:after {
    margin: auto;
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1);
}

blockquote.pull-right {
    /* text-align: right; */
    /* border-left: none; */
    /* border-right: 5px solid #c4112f; */
    /* padding: 10px 30px 10px 10px; */
}
.fs22 {
    font-size: 22px;
}
.BulletList-row {
    display: flex;
    flex-wrap: wrap;
}
.BulletList-row .BulletList {
    -webkit-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
}
.mr-10 {
    margin-right: 10px;
}
.Highlight .btns-wrap .SATLAButton {
    margin-right: 10px;
}

.my-10 {
    margin-top: 10px;
    margin-bottom: 10px;
}
.textLine-sec p {
    text-align: center;
    color: #33383A;
}
.header .navbar .nav li.open-droupdown a {
    font-weight: 700;
    color: #083372;
}
.upcoming-event-sec .flex-row>div {
    margin: 0 !important;
    width: 290px;
    padding: 0 15px;
}
.upcoming-event-sec .row>.span-12 {
    padding-left: 15px;
    width: 100%;
    margin-left: 0px;
}

.footer img.bg-img {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -1;
    left: 0;
    top: 0;
}

.footer .for-mobile {
    position: relative;
    z-index: 2;
    padding: 30px 15px 20px;
    margin-top: 55px;
}

.footer .for-mobile h2 {
    color: #ffffff;
    font-size: 22px;
    margin: 0 0 15px;
    font-weight: 400;
    line-height: 1.3;
}

.footer .for-mobile  .row-flex {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.footer .for-mobile .row-flex .col12 h2 {
    text-align: center;
}

.footer .for-mobile .row-flex .col6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-left: 15px;
    padding-right: 15px;
}

.footer .for-mobile .row-flex .col12 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-top: 30px;
}

.footer .for-mobile .row-flex .sbmrow {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    padding: 0 15px;
}

.footer .for-mobile .row-flex .sbmrow ul {
    flex: 0 0 calc(50% - 15px);
    max-width: calc(50% - 15px);
}

.footer .for-mobile ul li , .footer .for-mobile ul li a {
    color: #ffffff;
    font-size: 15px;
    font-weight: 700;
}

.footer .for-mobile ul li {
    border: 1px solid rgb(255 255 255 / 30%);
    border-style: solid none;
    margin: -1px 0 0;
    padding: 4px 0;
    display: flex;
    align-items: center;
    min-height: 40px;
}
.footer .for-mobile ul li a:hover {
    color: #E1C783;
    text-decoration: none;
}

.footer .for-mobile ul li:before {
    content: "\f101";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin-right: 10px;
    color: #A2D5ED;
}
.footer .for-mobile .copyright a {
    color: #ffffff;
    text-decoration: underline;
}
.footer .for-mobile .copyright p {
    font-weight: 900;
    padding: 0 015px;
}
.footer .for-mobile .copyright p>span {
    margin: 0 5px; 
}
.copyright ul {
    margin: .0;
    color: #ffffff;
    list-style: none;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    font-size: 14px;
    font-weight: 500;
}

.copyright a {
    color: inherit;
    font-weight: inherit;
}

.copyright li:not(:last-child):after {content: "|";margin: 15px;}

.social-list {
    width: auto;
    /* position: absolute; */
    /* top: 15px; */
    /* z-index: 1; */
    /* right: 240px; */
    list-style: none;
    /* margin: 0; */
    padding: 0;
}
.social-list ul {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
}
.social-list ul li:first-child {
    color: #ffffff;
    flex: 1 1 auto;
    font-size: 13px;
    font-weight: 900;
    
}
.footer .for-mobile .social-list>ul li a {
    display: inline-block;
    font-size: 18px;
    color: #ffffff;
    text-decoration: none;
    text-align: center;
}
.footer .for-mobile .social-list>ul li:before {
    display: none;
}
.footer .for-mobile .social-list li {
    border-style: none;
    padding: 0;
    text-transform: uppercase;
}
.footer .for-mobile .social-list li:not(:first-child) {
    margin-left: 25px;
}
.footer .for-mobile .row-flex .col6:first-child ul>li:before {
    display: none;
}
.footer .for-mobile ul li:hover:before {
    color: #E1C783;
}

.sponsors-img-list ul li {
    min-width: auto;
}
.foot-logo:after {
    width: 126px;
}
.loggedinBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    margin: 0 auto;
    max-width: 250px;
    padding: 5px 0;
}
.header .navbar .nav li .loggedinBox a.SATLAButton {
    padding: 12px 23px;
    min-height: auto;
    color: #ffffff;
    background: #472103;
    border-color: #472103;
    font-size: 18px;
}

.header .navbar .nav li .loggedinBox>span p {
    color: #ffffff;
    margin: 8px 0 0;
    font-weight: 500;
    font-size: 20px;
}

.header .navbar .nav li .loggedinBox>span {
    display: block;
    text-align: center;
}

.header .navbar .nav>li.show-form.dropdown>.dropdown-menu {
    display: block !important;
    visibility: visible !important;
    z-index: 999 !important;
    opacity: 1 !important;
    top: 0;
    left: 0;
    margin: 0;
    width: 100%;
}

.header .navbar .nav li a.nav-member-center:after {
    display: none;
}

textarea, 
input[type="text"], 
input[type="password"], 
input[type="datetime"], 
input[type="datetime-local"], 
input[type="date"], 
input[type="month"], 
input[type="time"], 
input[type="week"], 
input[type="number"], 
input[type="email"], 
input[type="url"], 
input[type="search"], 
input[type="tel"], 
input[type="color"],
.uneditable-input {
    min-height: 30px;
}

.footer .for-mobile ul, .captionFrame ul, .friendsheroBannerBox .item ul, .footer .footer-info ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.event-mobile, .news-mobile {
    display: none;
}
.d-none {
    display: none !important;
}

ul.social-list {
    display: flex;
    margin: 0;
}

ul.social-list li a {
    display: inline-flex;
    align-items: center;
}

ul.social-list li {
    margin-right: 0px;
}
.foot-logo-wrap {
    display: flex;
    justify-content: space-between;
}
.my-20 {
    margin-top: 20px;
    margin-bottom: 20px;
}
.my-30 {
    margin-top: 30px;
    margin-bottom: 30px;
}
.row.row-flex {
    margin: 0 -15px;
    display: flex;
}
.row.row-flex>.span4 {
    flex: 0 0 25%;
    -webkit-flex: 0 0 25%;
    max-width: 25%;
    width: 25%;
    padding: 0 15px;
}
.row.row-flex>.span8 {
    flex: 0 0 75%;
    -webkit-flex: 0 0 75%;
    max-width: 75%;
    width: 75%;
    margin-left: 0;
    padding: 0 15px;
}
.event-list .sbm-event .sbm-e-head span:first-child:after {
    display: inline-block;
    content: "|";
    color: #33383A;
    opacity: 0.3;
    position: absolute;
    right: 0;
    top: 0;
}
.event-list .sbm-event .sbm-e-head {
    display: flex;
    justify-content: space-between;
    color: #A8462B;
    font-weight: 400;
    text-align: center;
}
.event-list .sbm-event .sbm-e-head span {
    min-width: 45%;
    text-align: center;
    position: relative;
}
.btn-flex {
    display: flex;
    width: 100%;
    align-items: center;
}
.header .navbar .nav>li.dropdown>a {
    position: relative;
    padding-right: 23px;
}
.droptitle {font-size: 19px;font-weight: 600;margin-bottom: 10px;display: inline-block;color: #3A3D3F;border-bottom: 1px solid #3A3D3F;}
.header .navbar .nav li.dropdown .megaMenuSection li a:before {
    /* content: "\f101";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    opacity: 0;
    font-size: 0; */
    }
    
    .header .navbar .nav li.dropdown .megaMenuSection li a:hover:before {
    /* font-size: 100%; */
    /* opacity: 1; */
    /* margin-right: 5px; */
    }

    .header .nav-collapse .nav .dropdown.headerlogin>ul.memberSection span.menu-arrow {
        display: none !important;
    }
    .content-info .btns-wrap .SATLAButton {
        margin-bottom: 15px;
    }
    .quicklink-mobile a {
        display: block;
        padding: 6px 0;
        color: #333C59;
        font-size: 16px;
    }
    .serving-time-sec {
        background: #F5F5F5;
        position: relative;
        z-index: 1;
    }
    .serving-time-sec p {
        color: #3A3D3F;
    }.serving-time-sec p>a {
    color: inherit;
    text-decoration: underline;
}
    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0 15px;
    }
    .friendsheroBannerBox .owl-item li {
        text-align: center;
        margin: 0;
        padding: 15px;
    }
    
    .friendsheroBannerBox .owl-item li > a {
        display: inline-block;
        margin: 0 auto;
    }
    
    
    .friendsheroBannerBox .owl-stage {
        display: flex;
        align-items: center;
    }
    
    .friendsheroBannerBox .owl-stage:before, .friendsheroBannerBox .owl-stage:after {
        display: none;
    }
    .my-40 {
        margin-top: 40px;
        margin-bottom: 40px;
    }
    .mob-social-list span.HeaderTextSmall {
        width: 100%;
        text-align: center;
        display: block;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .mob-social-list {
        padding: 0px 0 15px;
    }

    .hbf-design {

    }

    .hbf-design header .navbar .nav li.headerlogin {
        background: #316900;
    }
    
    .hbf-design header .navbar .nav li.headerlogin p {
        color: #ffffff;
    }
    
    .hbf-design header .navbar .nav li.headerlogin .nav-member-center img {
        filter: brightness(100);
    }
    
    .hbf-design .header .nav-collapse .nav {
        justify-content: end;
    }
    .hbf-design .header .navbar .nav li.dropdown .megaMenuSection .formframe a {
        background: #316900;
        border-color: #316900;
    }
    .hbf-nav-sec {
        position: relative;
        z-index: 1;
        background: #000000;
        padding: 50px 0;
        width: 100%;
    }
    .hbf-nav-sec img {
        width: 100%;
        opacity: 0.6;
    }
    .hbf-nav-sec h2 {
        color: #ffffff;
        text-align: center;
        font-size: 45px;
        font-family: 'Playfair Display', serif;
        font-style: normal;
        font-weight: 600;
        line-height: 115%;
        display: flex;
        justify-content: center;
        text-transform: uppercase;
    }
    .hbf-nav-wrap {padding: 5px 0;}

    .hbf-nav-wrap .dropdown.open .dropdown-menu {
        display: block !important;
    }
    .hbf-nav-wrap .nav>li {
        display: inline-block;
    }
    
    .hbf-nav-wrap .nav>li>a {
        padding: 20px 8px;
        font-size: 13px;
        font-weight: 800;
        color: #ffffff;
        text-transform: uppercase;
        display: inline-flex;
        text-align: center;
    }
    .hbf-nav-wrap .nav>li>a:hover {
        color: white;
        background: transparent;
    }
    
    .hbf-nav-sec .container {
        max-width: 1500px;
    }
    
    .hbf-nav-sec ul.nav {
    margin: 0;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    }
    .hbf-design .header .navbar-brand {
        background-color: #ffffff;
        padding: 22px 25px 15px 25px;
    }
    .hbf-design .header .navbar-inner {
        background-color: #ffffff;
    }
    .hbf-nav-wrap .nav>li .dropdown-menu {
        margin-top: -1px;
        background: #ffffff;
        width: auto;
        min-width: 180px;
        border-style: none;
        box-shadow: 0 5px 10px rgb(0 0 0 / 10%);
    }
    .hbf-nav-wrap .nav>li .dropdown-menu .megaMenuSection>.mainMenu {position: absolute;left: 100%;min-width: 180px;background: #ffffff;margin: 0 0 0 -1px;padding: 5px 0;top: 0;list-style: none;display: none;box-shadow: 0 5px 10px rgb(0 0 0 / 10%);}
    .hbf-nav-wrap .nav>li .dropdown-menu .megaMenuSection>.mainMenu>li>a {
        display: block;
        padding: 5px 15px;
        color: #000000;
    }
    .hbf-nav-wrap .nav>li .megaMenuSection:hover>ul.mainMenu {
        display: block;
    }
    .hbf-nav-wrap .nav>li .megaMenuSection {
        display: block;
    }
    .hbf-nav-wrap .nav>li .dropdown-menu .megaMenuSection > a {
        padding: 5px 15px;
    }
    .hbf-nav-wrap .nav>li .dropdown-menu .megaMenuSection:hover>a, .hbf-nav-wrap .nav>li .dropdown-menu .megaMenuSection>a:hover, .hbf-nav-wrap .nav>li .dropdown-menu .megaMenuSection>.mainMenu>li:hover>a, .hbf-nav-wrap .nav>li .dropdown-menu .megaMenuSection>.mainMenu>li>a:hover {
        background: #316900;
        color: #ffffff;
    }
    .hbf-nav-wrap .nav>li:not(:last-child)>a:after {
        content: "|";
        color: #ffffff;
        display: inline-block;
        margin-left: 15px;
    }

    .hbf-nav-sec ul.nav {
    }
    
    .hbf-nav-sec ul.nav>li {
    }
    
    .hbf-nav-wrap .nav>li>a {
        display: block;
        text-align: left;
    }
    .hbf-nav-wrap .dropdown .dropdown-menu {
        position: absolute;
    }
    .hbf-nav-wrap .nav>li>a:focus {
        background: transparent;
        outline: none;
        box-shadow: none;
    }

    .bottom-line-0:after {
    display: none;
}
.section-left-img {
    position: relative;
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.member-sec {

}

.member-card {
    background-color: #BE6846;
}
.member-card.bg-gray {
    background: #516774;
}
.member-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.member-card ul li:first-child {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #D5D5D5;
    background: #ffffff;
}
.member-card ul li:first-child .member-icon {
        overflow: hidden;
        position: relative;
        flex: 0 0 85px;
        max-width: 85px;
        height: 85px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #D5D5D5;
}
.member-card ul li:first-child .member-icon img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}
.member-card ul li:first-child .ColumnHeader {
    flex: 0 0 calc(100% - 85px);
    max-width: calc(100% - 85px);
    margin: 0;
    padding-left: 20px;
    text-transform: uppercase;
    text-align: left;
}
.member-card ul li:nth-child(2) img {
    width: 100%;
    margin: 0;
}
.member-card ul li:nth-child(3),
.member-card ul li:nth-child(4),
.member-card ul li:nth-child(5) {
    padding-left: 60px;
    padding-right: 60px;
    margin-bottom: 15px;
}
.member-card ul li:nth-child(3) .ColumnHeader, 
.member-card ul li:nth-child(4) p, 
.member-card ul li:nth-child(5) {
    color: #ffffff;
}
.member-card ul li:nth-child(3) {
    padding-top: 80px;
}
.member-card ul li:last-child {
    padding-bottom: 80px;
    display: inline-flex;
    align-items: center;
    gap: 15px;
}
.member-card .owl-carousel .owl-nav button.owl-prev, .member-card .owl-carousel .owl-nav button.owl-prev:hover {
    top: 470px;
    margin-top: 0;
    width: 30px;
    height: 30px;
    line-height: 1;
    padding: 0 !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid #ffffff;
    background: transparent;
    color: #ffffff;
    font-size: 14px;
    left: 15px;
}

.member-card .owl-carousel .owl-nav button.owl-next {
    top: 470px;
    margin-top: 0;
    display: inline-flex;
    line-height: 0;
    width: 30px;
    height: 30px;
    padding: 0 !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid #ffffff;
    background: transparent;
    color: #ffffff;
    font-size: 14px;
    right: 15px;
}
.member-card .owl-carousel .owl-nav button.owl-prev:hover,
.member-card .owl-carousel .owl-nav button.owl-next:hover {
    background-color: #BE6846;
    border-color: #BE6846;
}
.member-card .owl-dots {
    position: absolute;
    width: 100%;
    top: 448px;
    height: 70px;
    z-index: 1;
    background: #BE684699;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 35px;
}
.member-card .owl-dots span {
    border: 2px solid #ffffff;
    width: 10px;
    height: 10px;
    display: inline-block;
    border-radius: 50%;
}

.member-card .owl-dots .active span {
    background: #ffffff;
}
.member-card.bg-gray .owl-dots {
    background-color: #51677480;
}
.member-sec .span6 {
    margin: 0 0;
    flex: 0 0 50%;
    max-width: 50%;
    padding-left: 15px;
    padding-right: 15px;
}

.footer .footer-mobile-menus {
    padding: 0 15px;
}





.sidebar-iconbox:before {
    content: "\f105";
    position: absolute;
    left: 0;
    top: 0;
    font-weight: 900;
    font-family: "Font Awesome 6 Pro";
    color: #BE6846;
    font-size: 16px;
}
.sidebar-iconbox:hover:before,
.sidebar-iconbox:focus:before {
	/* opacity: 1; */
}
.sidebar-iconbox:hover,
.sidebar-iconbox:focus {
    text-decoration: underline;
    color: #BE6846;
}
.sidebar-iconbox .textBox h2 {
	font-size: 14px;
	color: #333333;
	font-weight: 600;
	line-height: 100%;
	margin: 0;
	line-height: 1.4;
	font-family: "Montserrat", sans-serif;
}

.sidebar-iconbox:hover .textBox h2 {
    color: #263A9A;
}


.innerContent {
	display: flex;
	padding-top: 70px;
	background: linear-gradient(181.86deg, #FFFFFF 79.86%, #F7F7F7 100.42%);
}
.innerContent .rightInner {
	background: #fff;
	padding: 0 165px 0 80px;
	margin: 0;
	width: calc(100% - 340px);
}
.innerContent .leftInner {
	 margin: 0;
	 width: 340px;
	 padding-left: 40px;
}
.innerContent .leftInner h3 { margin-bottom: 30px; color: #242424; }




.content .row.d-flex-wrap .span8 {
    flex: 0 0 60%;
    max-width: 60%;
}

.content .row.d-flex-wrap .span4 {
    flex: 0 0 40%;
    max-width: 40%;
}
.event-head .event-icon {
    flex: 0 0 85px;
    max-width: 85px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #D5D5D5;
    height: 85px;
}

.event-head .ColumnHeader {
    flex: 0 0 calc(100% - 85px);
    max-width: calc(100% - 85px);
    padding-left: 20px;
    padding-right: 20px;
    color: #1F2A44;
}
.inner-page-content .row.d-flex-wrap {
    flex-direction: row-reverse;
}
.event-head .event-icon img {
    width: 38px;
    height: 38px;
    object-fit: contain;
}
.quicklinks-submenu {
    list-style: none;
    margin: 0 0 15px;
    display: none;
    padding-left: 22px;
}

.quicklinks-submenu li a {
    display: block;
    padding: 5px 0px;
    font-family: var(--montserrat);
}
.quicklinks-submenu li a:hover {
    color: #BE6846;
    font-weight: 500;
    font-size: 15px;
}
.adv-box h2 {
    margin: 15px 0;
    font-size: 13px;
    font-weight: 700;
    text-transform: uppercase;
    color: #9E9E9E;
    text-align: center;
}

.eventbox-col.adv-box {
    padding: 0 30px 30px;
    text-align: center;
}
.sidebar-event {padding: 20px 0;border-bottom: 1px solid #888B8D;}
.sidebar-event .sidebar-event-title {
    font-size: 16px;
    font-weight: 500;
    font-family: var(--montserrat);
    color: #1F2A44;
    margin: 0;
    padding-right: 10px;
}
.sidebar-event .sidebar-event-date {
    color: #888B8D;
    font-size: 14px;
    font-weight: 700;
    font-family: var(--montserrat);
    margin: 0 0 6px;
}
.sidebar-event:not(:last-child) {
    border-bottom: 1px solid #888B8D;
}
.sidebar-event:hover .sidebar-event-date,
.sidebar-event:hover .sidebar-event-title a {
    color: #BE6846;
}
.inner-page-content .leftInner.widget {
    margin: 0;
    width: 300px;
}
.inner-page-content .container {
    padding: 0 32px;
}
.img-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.img-row .img-col {
    flex: 0 0 50%;
    padding: 0 15px;
}

.img-box {
    display: flex;
}

.img-box img {
    flex: 0 0 40%;
    max-width: 40%;
    object-fit: cover;
}

.img-box .img-box-coltent {
    flex: 0 0 60%;
    max-width: 60%;
    padding-left: 30px;
    align-self: center;
}
.mb-10 {
    margin-bottom: 10px;
}
.Highlight p {
    font-size: 22px;
    font-family: var(--montserrat);
}
.img-row .img-col:first-child {
    border-right: 1px solid #DCDDDE;
}
.bannerInner .container {
    padding: 0 15px;
}

.top-action-wrapper {
    display: flex;
    padding: 0px 0;
}
.top-action-wrapper .logo-mobile {
    flex: 0 0 120px;
    max-width: 120px;
    text-align: center;
    border-right: 1px solid #ffffff;
}
.top-action-wrapper .navigation-close {
    flex: 0 0 80px;
    max-width: 80px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-left: 1px solid #ffffff;
}
.top-action-wrapper .centered-btn-wrap {
    flex: 0 0 calc(100% - 200px);
    max-width: calc(100% - 200px);
    text-align: center;
    align-self: center;
}
.top-action-wrapper .centered-btn-wrap .CAAAButton {
    font-size: 13px;
}
.top-action-wrapper .navigation-close .close-menu {
    color: #ffffff;
    font-size: 26px;
}
.mobile-adv {
    padding: 0 20px;
}
.hidden{
    display: none !important;
}
#zoneToolBar form,#zoneToolBar select{margin:0;}
.ZoneQMargin{
    margin-top: 20px;
}

.captionFrame ul li.secondImageBanner:before{
    content: unset !important;
    width: unset !important;
    height: unset !important;
    border: unset !important;
    display: none !important;
    position: unset !important;
    border-radius: unset !important;
    opacity: unset !important;
}
.zoneBRightOnly{
    padding-top: 20px;
    padding-bottom: 20px;
}
.zoneBRightOnly .quick-link-right-box:before{
    background: unset !important;
}

.bcSection a{
    color:#fff; font-weight:600; text-transform:uppercase;
}

.span12.removeFlex.inner-content-area{
    padding: unset !important;
    margin-bottom: 0;
    width: 100%;
    flex: unset !important;
    max-width: unset !important;
    margin: unset !important;
}
.noSearchText{
    border-color: rgba(82, 168, 236, 0.8) !important;
    outline: 0 !important;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6) !important;
    -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075),0 0 8px rgba(82,168,236,0.6) !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6) !important;
}
.searchLg{
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none !important;
}