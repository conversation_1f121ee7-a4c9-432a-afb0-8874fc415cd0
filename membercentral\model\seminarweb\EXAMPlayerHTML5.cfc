<cfcomponent extends="model.system.utility.CFCRemoteDocumenter">
	<cfsetting showdebugoutput="false"/>	
	<cfset variables.sendDebugEmail = 0 />
	
	<!--- *********************** --->
	<!--- seminar level functions --->
	<!--- *********************** --->
	<cffunction name="getSeminarforPlayer" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="Yes">
		<cfargument name="enrollmentid" type="string" required="no" default="">
		<cfargument name="uniqueCode" type="string" required="no" default="">
		<cfargument name="seminarID" type="string" required="no" default="">

		<cfset var local = StructNew()>
		<cfparam name="local.SWLoadArray" default="#ArrayNew(1)#">
		<cfif isdefined("arguments.uniqueCode") AND arguments.uniqueCode neq ''>
			<cfscript>		
				// create objects
				local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars");
				local.objSWP = CreateObject("component","model.seminarweb.SWParticipants");
				local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars");

				// setup return structure and variables
				structInsert(local,"returnStruct",structNew());
				structInsert(local.returnStruct,"returnData",structNew());
				local.missingFilesArray = ArrayNew(1);
				
				// shortcut used in sets below
				local.rsrd = local.returnStruct.returnData;		
				local.mc_siteInfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode);
				local.memberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=local.mc_siteInfo.orgID);
				// is user logged in?
				structInsert(local.returnStruct,"userLoggedIn",1);
				local.verifyReg = local.objSWL.verifySWLCode(arguments.uniqueCode);

				// set to false and overwrite with true upon completion
				structInsert(local.rsrd,"loadSuccess",0);
				structInsert(local.rsrd,"isAdminMode",0);
			
				local.qrySWP = local.objSWP.getAssociationDetails(arguments.mcproxy_siteCode).qryAssociation;
					
				// if bad enrollmentid, set logged in to false
				if (isdefined("arguments.enrollmentid") and isnumeric(arguments.enrollmentid) and val(local.verifyReg.isCodeValid)) {
					local.strSeminar = local.objSWL.getEnrollmentByEnrollmentID(arguments.enrollmentid);
					local.strAccessIDs = setupLogAccessIDs(arguments.enrollmentID);
					structInsert(local.returnStruct.returnData,"enrollmentID",val(local.strSeminar.enrollmentID));
					structInsert(local.returnStruct.returnData,"depomemberdataID",local.strSeminar.depomemberdataid,true);
					arrayAppend(local.SWLoadArray,"#now()# - SWLEnrollmentID #arguments.enrollmentid# - enrollmentID #local.strSeminar.enrollmentID#");
					if (local.returnStruct.returnData.enrollmentID is 0)
						reportErrorToAdmin(arguments.mcproxy_siteCode,0,"getSWLEnrollment: enrollmentID set to 0 - getEnrollmentByEnrollmentID returned 0");
					
					
					local.memberInfo = application.objMember.getMemberInfo(memberID=local.strSeminar.MCMemberID);
					local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=local.strSeminar.orgcode, membernumber=local.memberInfo.membernumber);
					local.strSeminarProgress =local.objSWL.getSeminarProgress(enrollmentID=local.strSeminar.enrollmentID);
					structInsert(local.rsrd,"logAccessID",local.strAccessIDs.seminarAccessID);
					// load enrollment progress
					structInsert(local.rsrd,"isOpen",local.strSeminarProgress.qrySeminarSettings.isOpen);
					structInsert(local.rsrd,"offerCertificate",local.strSeminar.offerCertificate);
					structInsert(local.rsrd,"startDate",'');
					structInsert(local.rsrd,"memberKey",local.memberKey);
					structInsert(local.rsrd,"seminarTime",'');
					structInsert(local.rsrd,"allPreTestCompleted",local.strSeminarProgress.qrySeminarProgress.allPreTestCompleted);
					structInsert(local.rsrd,"allPostTestCompleted",local.strSeminarProgress.qrySeminarProgress.allPostTestCompleted);
					structInsert(local.rsrd,"allEvaluationCompleted",local.strSeminarProgress.qrySeminarProgress.allEvaluationCompleted);
					structInsert(local.rsrd,"completionCheckObj",checkSeminarforCompletion(arguments.mcproxy_siteCode,arguments.enrollmentid));
					structInsert(local.rsrd,"seminarLoadPointsObj",getSeminarLoadPointsObj(local.strSeminar.seminarID));
					structInsert(local.rsrd,"topRightLogo","");
					structInsert(local.rsrd,"topLeftLogo",local.objSWP.getAssociationAds(orgcode=arguments.mcproxy_siteCode).primaryLogo);
					structInsert(local.rsrd,"enrollmentCreditObj",getEnrollmentCreditJSon(arguments.enrollmentid));
					structInsert(local.rsrd,"isFastForwardPermitted",local.objSWOD.checkStaffCanForward(memberID=local.memberID,siteID=local.mc_siteInfo.siteID,orgID=local.mc_siteInfo.orgID));
					local.meetingUrl = '';
					if (local.strSeminar.providerID is 3)
						local.meetingUrl = local.strSeminar.ZoomWebinarJoinURL;
					structInsert(local.rsrd,"meetingUrl",local.meetingUrl);		
					
					local.parsedTime = local.objSWL.parseTimesFromWDDX(seminarWDDXTimeZones=local.strSeminar.wddxTimeZones, orgWDDXTimeZones=local.qrySWP.wddxTimeZones, ifErrStartTime=local.strSeminar.dateStart, ifErrEndTime=local.strSeminar.dateEnd);
					local.startTime = '';
					local.endTime = '';
					local.tz = '';
					if(structKeyExists(local.parsedTime,'STARTDATE')){						
						local.rsrd.startDate = DateFormat(local.parsedTime.STARTDATE,"mmmm dd, yyyy");
						local.startTime = DateTimeFormat(local.parsedTime.STARTDATE,"hh:nn tt");
					}
					if(structKeyExists(local.parsedTime,'ENDDATE')){						
						local.endTime = DateTimeFormat(local.parsedTime.ENDDATE,"hh:nn tt");
					}
					if(structKeyExists(local.parsedTime,'TIMEZONE')){						
						local.tz = local.parsedTime.TIMEZONE;
					}
					local.rsrd.seminarTime = local.startTime &' - '&local.endTime&' '&local.tz;

					structInsert(local.rsrd,"titleArray",ArrayNew(1));
					// figure out loadPoint				
					local.currentLoadPoint = '';
					
					if (local.strSeminarProgress.qrySeminarSettings.isOpen is 1) {
					
						// any pre tests to take?
						if (local.strSeminarProgress.qrySeminarProgress.allPreTestCompleted is not 1)
							local.currentLoadPoint = 'preTest';

					// sem closed
					} else {
						// any post tests to take?
						if (local.strSeminarProgress.qrySeminarProgress.allPostTestCompleted is not 1)
							local.currentLoadPoint = 'postTest';

						// any evaluations to take?
						else if (local.strSeminarProgress.qrySeminarProgress.allEvaluationCompleted is not 1)
							local.currentLoadPoint = 'evaluation';
					}
						
					structInsert(local.rsrd,"currentLoadPoint",local.currentLoadPoint);
					

					// load seminar metadata
					structInsert(local.rsrd,"seminarID",local.strSeminar.seminarID);
					structInsert(local.rsrd,"seminarName",local.strSeminar.seminarName);

					// load orgcode branding and details
					structInsert(local.rsrd,"sponsor",local.qrySWP.description);
					structInsert(local.rsrd,"seminarBrand",local.qrySWP.brandSWLTab);
					structInsert(local.rsrd,"supportPhone",local.qrySWP.supportPhone);
					structInsert(local.rsrd,"supportEmail",local.qrySWP.supportEmail);
					structInsert(local.rsrd,"supportHours",local.qrySWP.supportHours);

					// load enrollee information
					structInsert(local.rsrd,"firstName",local.strSeminar.firstname);
					structInsert(local.rsrd,"lastName",local.strSeminar.lastName);
					structInsert(local.rsrd,"email",local.strSeminar.email);

					// set to true upon completion
					structInsert(local.rsrd,"loadSuccess",1,true);
					structInsert(local.rsrd,"step",0);
					structInsert(local.rsrd,"showSurvey",local.strSeminarProgress.qrySeminarProgress.showSurvey);
					
					if(local.verifyReg.SWLUserType eq "speaker"){
						structInsert(local.rsrd,"userType",'speaker');
						structInsert(local.rsrd,"attended",0);
						structInsert(local.rsrd,"hasNotMetDateRequirements",0);
					}else{
						structInsert(local.rsrd,"userType",'user');
						structInsert(local.rsrd,"attended",local.strSeminarProgress.qrySeminarProgress.attended);
						local.hasNotMetDateRequirements = local.objSWL.hasNotMetDateRequirements(enrollmentID=arguments.enrollmentid);
						structInsert(local.rsrd,"hasNotMetDateRequirements",local.hasNotMetDateRequirements);
					}
					local.materialsDocObj = getMaterialsDocument(seminarID=local.strSeminar.seminarID, enrollmentID=arguments.enrollmentid, userType=local.verifyReg.SWLUserType, sitecode=session.mcstruct.sitecode);			
					structInsert(local.rsrd,"materialsDocumenObj",local.materialsDocObj);


					local.showReplayVideo = false;
					local.replayVideoLink = '';
					structInsert(local.rsrd,"canViewReplay",false);
					structInsert(local.rsrd,"replayVideoLink",'');
					structInsert(local.rsrd,"replayVideoLinkExpire",'');
					
					if(not local.strSeminarProgress.qrySeminarSettings.isOpen){
						local.loadPointArr = arrayNew(1);
						if(local.strSeminarProgress.qrySeminarProgress.allPostTestCompleted is not 1){
							arrayAppend(local.loadPointArr, "exam");
						}
						if(local.strSeminarProgress.qrySeminarProgress.showSurvey eq 1 or local.strSeminarProgress.qrySeminarProgress.allEvaluationCompleted is not 1){
							arrayAppend(local.loadPointArr, "evaluation");
						}
						local.canViewReplay = 0;
						if(local.qrySWP.offerSWLReplays is 1 AND local.strSeminarProgress.qrySeminarProgress.showReplayVideo is 1){
							local.replayVideoObj = local.objSWL.getSWLReplayVideoLinkFromSeminarID(seminarID=local.verifyReg.seminarID);
							local.rsrd.replayVideoLink = local.replayVideoObj.replayVideoLink;
							if(len(local.replayVideoObj.expireDate)){
								local.rsrd.replayVideoLinkExpire = DateFormat(local.replayVideoObj.expireDate,"mm/dd/yyyy");
							}
							local.replayAvailability = 0;
							if(local.replayVideoObj.replayAvailability eq 'attendees'){
								if(local.strSeminarProgress.qrySeminarProgress.attended){
									local.replayAvailability = 1;
								}

							}else if(local.replayVideoObj.replayAvailability eq 'nonattendees'){
								if(local.strSeminarProgress.qrySeminarProgress.attended neq 1){
									local.replayAvailability = 1;
								}
							}else if(local.replayVideoObj.replayAvailability eq 'registrants'){
								local.replayAvailability = 1;
							}
							
							if(local.replayAvailability eq 1 AND (local.replayVideoObj.offerReplay eq 1) AND dateDiff('d',local.replayVideoObj.expireDate,now()) lte 0){
								local.canViewReplay = 1;
							}
							local.showReplayVideo = ((len(local.rsrd.replayVideoLink) GT 0) AND local.canViewReplay eq 1)? true : false;
						}

						if(local.showReplayVideo){
							local.rsrd.canViewReplay = true;							
						}
						if (local.rsrd.offerCertificate is 1)
							structInsert(local.rsrd,"certificateURL",'/?pg=semWebCatalog&panel=viewCert&eId=#Replace(URLEncodedFormat(ToBase64(Encrypt(arguments.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")#&mode=stream',true);
					}
		
				}else if(arguments.enrollmentid eq '' and  isdefined("arguments.seminarID") and isnumeric(arguments.seminarID)){
					local.seminarObj = local.objSWL.getSeminarBySeminarID(seminarID=arguments.seminarID);
					structInsert(local.returnStruct.returnData,"enrollmentID",'');
					structInsert(local.returnStruct.returnData,"depomemberdataID",'');
					structInsert(local.rsrd,"isOpen",local.seminarObj.isOpen);
					structInsert(local.rsrd,"offerCertificate",local.seminarObj.offerCertificate);
					structInsert(local.rsrd,"allPreTestCompleted",0);
					structInsert(local.rsrd,"allPostTestCompleted",0);
					structInsert(local.rsrd,"allEvaluationCompleted",0);

					local.completionCheckObj = structnew();
					local.completionCheckObj['returnData']['allPreTestCompleted'] = 1;
					local.completionCheckObj['returnData']['allPostTestCompleted'] = 1;
					local.completionCheckObj['returnData']['allEvaluationCompleted'] = 1;
					structInsert(local.rsrd,"completionCheckObj",local.completionCheckObj);
					structInsert(local.rsrd,"seminarLoadPointsObj",structnew());
					structInsert(local.rsrd,"topRightLogo","");
					structInsert(local.rsrd,"topLeftLogo",local.objSWP.getAssociationAds(orgcode=arguments.mcproxy_siteCode).primaryLogo);
					structInsert(local.rsrd,"enrollmentCreditObj",structnew());
					structInsert(local.rsrd,"isFastForwardPermitted",0);
					local.meetingUrl = '';
					structInsert(local.rsrd,"meetingUrl",local.meetingUrl);	
					structInsert(local.rsrd,"titleArray",ArrayNew(1));
					structInsert(local.rsrd,"currentLoadPoint",'');
					structInsert(local.rsrd,"memberKey",'');
					// load seminar metadata
					structInsert(local.rsrd,"seminarID",local.seminarObj.seminarID);
					structInsert(local.rsrd,"seminarName",local.seminarObj.seminarName);

					// load orgcode branding and details
					structInsert(local.rsrd,"sponsor",local.qrySWP.description);
					structInsert(local.rsrd,"seminarBrand",local.qrySWP.brandSWLTab);
					structInsert(local.rsrd,"supportPhone",local.qrySWP.supportPhone);
					structInsert(local.rsrd,"supportEmail",local.qrySWP.supportEmail);
					structInsert(local.rsrd,"supportHours",local.qrySWP.supportHours);

					// load enrollee information
					structInsert(local.rsrd,"firstName",'');
					structInsert(local.rsrd,"lastName",'');
					structInsert(local.rsrd,"email",'');

					// set to true upon completion
					structInsert(local.rsrd,"loadSuccess",1,true);
					structInsert(local.rsrd,"showSurvey",0);

					structInsert(local.rsrd,"userType",'speaker');
					structInsert(local.rsrd,"attended",0);
					structInsert(local.rsrd,"hasNotMetDateRequirements",0);

					local.materialsDocObj = getMaterialsDocument(seminarID=local.seminarObj.seminarID, enrollmentID=0, userType='speaker', sitecode=session.mcstruct.sitecode);			
					structInsert(local.rsrd,"materialsDocumenObj",local.materialsDocObj);

					structInsert(local.rsrd,"canViewReplay",false);
					structInsert(local.rsrd,"replayVideoLink",'');
					structInsert(local.rsrd,"replayVideoLinkExpire",'');
					structInsert(local.rsrd,"certificateURL",'');
					structInsert(local.rsrd,"startDate",'');
					structInsert(local.rsrd,"seminarTime",'');

				}else {
					structInsert(local.returnStruct.returnData,"enrollmentID",0);
					arrayAppend(local.SWLoadArray,"#now()# - SWLEnrollmentID undefined/not numeric - enrollmentID #local.returnStruct.returnData.enrollmentID#");
				}

				return local.returnStruct;
				
			</cfscript>
		<cfelse>
			<cfscript>		
				// create objects
				local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars");
				local.objSWP = CreateObject("component","model.seminarweb.SWParticipants");
				local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars");
	
				// setup return structure and variables
				structInsert(local,"returnStruct",structNew());
				structInsert(local.returnStruct,"returnData",structNew());
				local.missingFilesArray = ArrayNew(1);
				
				// shortcut used in sets below
				local.rsrd = local.returnStruct.returnData;		
				local.mc_siteInfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode);
				local.memberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=local.mc_siteInfo.orgID);
				// is user logged in?
				structInsert(local.returnStruct,"userLoggedIn",1);
	
				// set to false and overwrite with true upon completion
				structInsert(local.rsrd,"loadSuccess",0);
				structInsert(local.rsrd,"isAdminMode",0);
				
				// if bad enrollmentid, set logged in to false
				if (isdefined("arguments.enrollmentid") and isnumeric(arguments.enrollmentid)) {
					local.strSeminar = local.objSWL.getEnrollmentByEnrollmentID(arguments.enrollmentid);
					local.strAccessIDs = setupLogAccessIDs(arguments.enrollmentID);
					local.memberInfo = application.objMember.getMemberInfo(memberID=local.strSeminar.MCMemberID);
					local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=local.strSeminar.orgcode, membernumber=local.memberInfo.membernumber);
					structInsert(local.returnStruct.returnData,"enrollmentID",val(local.strSeminar.enrollmentID));
					structInsert(local.returnStruct.returnData,"depomemberdataID",local.strSeminar.depomemberdataid,true);
					arrayAppend(local.SWLoadArray,"#now()# - SWLEnrollmentID #arguments.enrollmentid# - enrollmentID #local.strSeminar.enrollmentID#");
					if (local.returnStruct.returnData.enrollmentID is 0)
						reportErrorToAdmin(arguments.mcproxy_siteCode,0,"getSWLEnrollment: enrollmentID set to 0 - getEnrollmentByEnrollmentID returned 0");
	
					local.qrySWP = local.objSWP.getAssociationDetails(arguments.mcproxy_siteCode).qryAssociation;
					local.strSeminarProgress =local.objSWL.getSeminarProgress(enrollmentID=local.strSeminar.enrollmentID);
					structInsert(local.rsrd,"logAccessID",local.strAccessIDs.seminarAccessID);
					// load enrollment progress
					structInsert(local.rsrd,"isOpen",local.strSeminarProgress.qrySeminarSettings.isOpen);
					structInsert(local.rsrd,"offerCertificate",local.strSeminar.offerCertificate);
					structInsert(local.rsrd,"allPreTestCompleted",local.strSeminarProgress.qrySeminarProgress.allPreTestCompleted);
					structInsert(local.rsrd,"allPostTestCompleted",local.strSeminarProgress.qrySeminarProgress.allPostTestCompleted);
					structInsert(local.rsrd,"allEvaluationCompleted",local.strSeminarProgress.qrySeminarProgress.allEvaluationCompleted);
					structInsert(local.rsrd,"completionCheckObj",checkSeminarforCompletion(arguments.mcproxy_siteCode,arguments.enrollmentid));
					structInsert(local.rsrd,"seminarLoadPointsObj",getSeminarLoadPointsObj(local.strSeminar.seminarID));
					structInsert(local.rsrd,"topRightLogo","");
					structInsert(local.rsrd,"memberKey",local.memberKey );
					structInsert(local.rsrd,"topLeftLogo",local.objSWP.getAssociationAds(orgcode=arguments.mcproxy_siteCode).primaryLogo);
					structInsert(local.rsrd,"enrollmentCreditObj",getEnrollmentCreditJSon(arguments.enrollmentid));
					structInsert(local.rsrd,"isFastForwardPermitted",local.objSWOD.checkStaffCanForward(memberID=local.memberID,siteID=local.mc_siteInfo.siteID,orgID=local.mc_siteInfo.orgID));
					local.meetingUrl = '';
					if (local.strSeminar.providerID is 3)
						local.meetingUrl = local.strSeminar.ZoomWebinarJoinURL;
					structInsert(local.rsrd,"meetingUrl",local.meetingUrl);				
					
					structInsert(local.rsrd,"titleArray",ArrayNew(1));
					// figure out loadPoint				
					local.currentLoadPoint = '';
					
					if (local.strSeminarProgress.qrySeminarSettings.isOpen is 1) {
					
						// any pre tests to take?
						if (local.strSeminarProgress.qrySeminarProgress.allPreTestCompleted is not 1)
							local.currentLoadPoint = 'preTest';
	
					// sem closed
					} else {
						// any post tests to take?
						if (local.strSeminarProgress.qrySeminarProgress.allPostTestCompleted is not 1)
							local.currentLoadPoint = 'postTest';
	
						// any evaluations to take?
						else if (local.strSeminarProgress.qrySeminarProgress.allEvaluationCompleted is not 1)
							local.currentLoadPoint = 'evaluation';
					}
						
					structInsert(local.rsrd,"currentLoadPoint",local.currentLoadPoint);
					
	
					// load seminar metadata
					structInsert(local.rsrd,"seminarID",local.strSeminar.seminarID);
					structInsert(local.rsrd,"seminarName",local.strSeminar.seminarName);
	
					// load orgcode branding and details
					structInsert(local.rsrd,"sponsor",local.qrySWP.description);
					structInsert(local.rsrd,"seminarBrand",local.qrySWP.brandSWLTab);
					structInsert(local.rsrd,"supportPhone",local.qrySWP.supportPhone);
					structInsert(local.rsrd,"supportEmail",local.qrySWP.supportEmail);
					structInsert(local.rsrd,"supportHours",local.qrySWP.supportHours);
	
					// load enrollee information
					structInsert(local.rsrd,"firstName",local.strSeminar.firstname);
					structInsert(local.rsrd,"lastName",local.strSeminar.lastName);
					structInsert(local.rsrd,"email",local.strSeminar.email);
	
					// set to true upon completion
					structInsert(local.rsrd,"loadSuccess",1,true);
	
				} else {
					structInsert(local.returnStruct.returnData,"enrollmentID",0);
					arrayAppend(local.SWLoadArray,"#now()# - SWLEnrollmentID undefined/not numeric - enrollmentID #local.returnStruct.returnData.enrollmentID#");
					reportErrorToAdmin(arguments.mcproxy_siteCode,0,"fn getSWLEnrollment: enrollmentID set to 0 - not defined or not numeric check");
				}
	
				return local.returnStruct;
				
			</cfscript>
		</cfif>
	</cffunction>		
	<cffunction name="checkSeminarforCompletion" access="remote" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="false" >
		<cfargument name="enrollmentID" type="numeric" required="false" >
		<cfargument name="logAccessID" type="numeric" required="false" default="0">
		<cfargument name="reportObj" type="struct" required="false" default="#structNew()#">		

		<cfset var local = Structnew()>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.objSWFiles = CreateObject("component","model.seminarweb.SWFiles")>
		
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnStruct["returnData"] = structnew()>
		
		<cfset local.argumentsStruct = structnew() />
		<cfif isDefined("arguments.json")>
			<cfset local.argumentsStruct = deserializeJSON(arguments.json)>
		<cfelseif structKeyExists(getHttpRequestData(),"content") AND getHttpRequestData().content NEQ ''>
			<cfset local.argumentsStruct = deserializeJSON(deserializeJSON(getHttpRequestData().content).params.json)>
		<cfelse>
			<cfset local.argumentsStruct.orgcode = arguments.mcproxy_siteCode />
			<cfset local.argumentsStruct.enrollmentID = arguments.enrollmentID />
			<cfset local.argumentsStruct.logAccessID = arguments.logAccessID />
			<cfset local.argumentsStruct.reportObj = arguments.reportObj />
		</cfif>
		
		<cfif local.returnStruct["userLoggedIn"]>
			<cfif IsDefined("local.argumentsStruct.logAccessID") and IsDefined("local.argumentsStruct.reportObj.logAccessID") and local.argumentsStruct.reportObj.logAccessID gt 0 AND  arraylen(local.argumentsStruct.progressObj.fileArray) >	
				<cfset local.argumentsStruct.sendReminderEmailFlag = false />	
				<cfset local.argumentsStruct.includeLoginDetails = false />	
				<cfset saveProgressFromPlayer(serializeJSON(local.argumentsStruct))>
			</cfif>
			
			<cfstoredproc procedure="swod_getSeminarProgress" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.enrollmentID#" null="No">
				<cfprocresult name="local.qrySeminarSettings" resultset="1">
				<cfprocresult name="local.qrySeminarProgress" resultset="2">
				<cfprocresult name="local.qryCreditsPassed" resultset="3">
				<cfprocresult name="local.qryFiles" resultset="4">
				<cfprocresult name="local.qryTimeSpent" resultset="5">
			</cfstoredproc>
	
			<cfscript>
			// step 1 : check for incomplete exams and complete them for this enrollmentID
			local.objSWOD.completeIncompleteExams(enrollmentid=local.argumentsStruct.enrollmentID,loadPoint='postTest',recordedByMemberID=session.cfcuser.memberdata.memberID);

			local.returnStruct["returnData"]["isCompleted"] = 1;
			local.returnStruct["returnData"]["totalTimeSpent"] = val(local.qryTimeSpent.totalTimeSpent);
			local.returnStruct["returnData"]["seminarRulesArray"] = ArrayNew(1);
			local.returnStruct["returnData"]["fileRulesArray"] = ArrayNew(1);
			local.returnStruct["returnData"]["askedQA"] = local.qrySeminarProgress.askedQA;
			
			// check for test, survey 
			if (local.qrySeminarProgress.allPreTestCompleted)
				local.returnStruct["returnData"]["allPreTestCompleted"] = 1;
			else
				local.returnStruct["returnData"]["allPreTestCompleted"] = 0;

				if (local.qrySeminarProgress.allPostTestCompleted )
				local.returnStruct["returnData"]["allPostTestCompleted"] = 1;
			else
				local.returnStruct["returnData"]["allPostTestCompleted"] = 0;

				if (local.qrySeminarProgress.allEvaluationCompleted )
				local.returnStruct["returnData"]["allEvaluationCompleted"] = 1;
			else
				local.returnStruct["returnData"]["allEvaluationCompleted"] = 0;	
			
			// check for credit deadlines
			if (local.qryCreditsPassed.recordcount) {
				local.tmpArrRules = structnew();
				local.tmpArrRules.code = 2;
				local.tmpArrRules.description = "You have missed the course completion deadline for the authorities listed below.  You will not receive credit in these states.";
				if (local.qryCreditsPassed.recordcount eq 1)
					local.tmpArrRules.name = "You Have Missed 1 Completion Deadline";
				else 
					local.tmpArrRules.name = "You Have Missed #local.qryCreditsPassed.recordcount# Completion Deadlines";
				local.tmpArrRules.data = ArrayNew(1);
				for (local.i = 1; local.i lte local.qryCreditsPassed.recordcount; local.i = local.i + 1) {
					local.tmpStrData = StructNew();
					local.tmpStrData.authority = local.qryCreditsPassed.authorityName[local.i];
					local.tmpStrData.deadline = local.qryCreditsPassed.lastDateToComplete[local.i];
					ArrayAppend(local.tmpArrRules.data,local.tmpStrData);
				}
				ArrayAppend(local.returnStruct["returnData"]["seminarRulesArray"],local.tmpArrRules);
			}
			</cfscript>
	
			<!--- check completion percentage --->
			<cfif local.qrySeminarSettings.mediaRequiredPct gt 0>
				<!--- for each file, get the final accessDetails --->
				<cfset local.strAccessDetails = structNew()>
				<cfoutput query="local.qryFiles" group="fileID">
					<cfquery name="local.qryfiledetails" dbtype="query">
						select *
						from [local].qryFiles
						where fileID = #local.qryFiles.fileID#
					</cfquery>
					<cfset local.strAccessDetails[local.qryFiles.fileid] = local.objSWOD.bitORFiles(local.qryfiledetails)>
				</cfoutput>
			
				<!--- setup structure --->
				<cfset local.tmpArrRules = structnew()>
				<cfset local.tmpArrRules.code = 3>
				<cfset local.tmpArrRules.description = "You must play at least #local.qrySeminarSettings.mediaRequiredPct#% of each video/audio file in this seminar.">
				<cfset local.tmpArrRules.name = "You Have Not Played the Required Amount of Media Files">
				<cfset local.tmpArrRules.data = ArrayNew(1)>
	
				<!--- loop over each title, then over each file matching the percentage --->
				<cfoutput query="local.qryFiles" group="titleID">
					<cfset local.tmpStrData = StructNew()>
					<cfset local.tmpStrData["titleID"] = local.qryFiles.titleID>
					<cfset local.tmpStrData["fileArray"] = arrayNew(1)>
					<cfoutput group="fileID">
						<cfif NOT local.objSWFiles.checkFileCompletion(local.strAccessDetails[local.qryFiles.fileid],local.qrySeminarSettings.mediaRequiredPct)>
							<cfset local.tmpStrFileData = StructNew()>
							<cfset local.tmpStrFileData["fileID"] = local.qryFiles.fileID>
							<cfif len(local.strAccessDetails[local.qryFiles.fileid]) gt 0>
								<cfset local.tmpStrFileData["playRatio"] = len(replacenocase(local.strAccessDetails[local.qryFiles.fileid],"0","","all")) / len(local.strAccessDetails[local.qryFiles.fileid])>
							<cfelse>
								<cfset local.tmpStrFileData["playRatio"] = 0>
							</cfif>
							<cfset local.tmpStrFileData["accessDetailsArray"] = getPlayReportFromAccessDetails(local.strAccessDetails[local.qryFiles.fileid],false)>
							<cfset ArrayAppend(local.tmpStrData.fileArray,local.tmpStrFileData)>
						</cfif>
					</cfoutput>
					<cfif Arraylen(local.tmpStrData.fileArray)>
						<cfset ArrayAppend(local.tmpArrRules.data,local.tmpStrData)>
					</cfif>
				</cfoutput>
					
				<cfif arrayLen(local.tmpArrRules.data)>
					<cfset ArrayAppend(local.returnStruct["returnData"]["fileRulesArray"],local.tmpArrRules)>
					<cfset local.returnStruct["returnData"]["isCompleted"] = 0>
				</cfif>
			</cfif>
			
			<!--- total time spent requirement --->
			<cfif local.qrySeminarSettings.mustAttendMinutes gt 0 and val(local.qryTimeSpent.totalTimeSpent) lt (local.qrySeminarSettings.mustAttendMinutes * 60)>
				<cfset local.tmpArrRules = structnew()>
				<cfset local.tmpArrRules.code = 4>
				<cfset local.tmpArrRules.description = "This seminar has a minimum time requirement of #generateTimeLengthString(local.qrySeminarSettings.mustAttendMinutes)#. According to our records, you have only spent #generateTimeLengthString(val(local.qryTimeSpent.totalTimeSpent)\60)#.">
				<cfset local.tmpArrRules.name = "You Have Not Satisfied the Minimum Time Requirement">
				<cfset local.tmpArrRules.data = "">
				<cfset ArrayAppend(local.returnStruct["returnData"]["seminarRulesArray"],local.tmpArrRules)>
				<cfset local.returnStruct["returnData"]["isCompleted"] = 0>
			</cfif>
		</cfif>			
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSeminarLoadPointsObj" access="private" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["semninarHasPreTest"] = 0>
		<cfset local.returnStruct["semninarHasPostTest"] = 0>
		<cfset local.returnStruct["semninarHasEvaluation"] = 0>
		
		<cfstoredproc procedure="sw_getSeminarForms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#" null="No">
			<cfprocresult name="local.qryPoints" resultset="1">
		</cfstoredproc>

		<cfloop query="local.qryPoints">
			<cfswitch expression="#local.qryPoints.loadPoint#">
				<cfcase value="evaluation">
					<cfset local.returnStruct["semninarHasEvaluation"] = 1>
				</cfcase>
				<cfcase value="postTest">
					<cfset local.returnStruct["semninarHasPostTest"] = 1>
				</cfcase>		
				<cfcase value="preTest">
					<cfset local.returnStruct["semninarHasPreTest"] = 1>
				</cfcase>							
			</cfswitch>
		</cfloop>	

		<cfreturn local.returnStruct>
	</cffunction>	
	<cffunction name="getEnrollmentCreditJSon" access="private" returntype="Any" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = structNew()>
		
		<cfquery name="local.qryEnrollmentCredit" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT ca.authorityName, eac.lastDateToComplete, 0 as completeByDatePassed
			FROM dbo.tblSeminars as s
			INNER JOIN dbo.tblEnrollments as e ON s.seminarID = e.seminarID
			INNER JOIN dbo.tblEnrollmentsAndCredit as eac ON e.enrollmentID = eac.enrollmentID
			INNER JOIN dbo.tblSeminarsAndCredit as sac ON s.seminarID = sac.seminarID 
				AND eac.seminarCreditID = sac.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa ON sac.CSALinkID = csa.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities as ca ON csa.authorityID = ca.authorityID
			INNER JOIN dbo.tblCreditAuthoritiesSWOD as caswod ON ca.authorityID = caswod.authorityID 
			where e.enrollmentid = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="cf_sql_integer">
			order by eac.lastDateToComplete, ca.authorityName
		</cfquery>		

		<cfset local.returnStruct = application.objCommon.queryToArrayOfStructures(local.qryEnrollmentCredit)>		
		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="loadFormsByLoadPoint" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">

		<cfscript>
		var local = StructNew();
		local.returnStruct = structnew();
		structInsert(local.returnStruct,"userLoggedIn",isLoggedIn());

		if (local.returnStruct.userLoggedIn) {
			structInsert(local.returnStruct,"returnData",StructNew());
			try {
				local.objSeminarsSWOD = CreateObject("component","model.seminarweb.SWODSeminars");
				local.objSeminarsSWL = CreateObject("component","model.seminarweb.SWLiveSeminars");
				local.qryEnrollment = local.objSeminarsSWL.getEnrollmentByEnrollmentID(arguments.enrollmentID)
				// step 1 : check for incomplete exams and complete them for this enrollmentID
				local.objSeminarsSWOD.completeIncompleteExams(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint,recordedByMemberID=local.qryEnrollment.MCMemberID);

				if(arguments.loadPoint eq 'posttestall'){
					arguments.loadPoint = 'posttest';
					local.strForms = getFormsFromLoadpointJSon(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint,depomemberdataid=local.qryEnrollment.depomemberdataid);					
				}else if(arguments.loadPoint eq 'evaluationall'){
					arguments.loadPoint = 'evaluation';
					local.strForms = getFormsFromLoadpointJSon(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint,depomemberdataid=local.qryEnrollment.depomemberdataid);					
				}else if(arguments.loadPoint eq 'evaluation'){
					arguments.loadPoint = 'evaluation';
					local.strForms = getFormsFromLoadpointJSon(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint,depomemberdataid=local.qryEnrollment.depomemberdataid);					
				}else{
					// step 2 : return all forms (formID, formtitle) for this seminar that user needs to take
					local.strForms = local.objSeminarsSWOD.getFormsFromLoadpointJSon(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint);
				}
				// step 3 : Get custom failed message if any
				local.strFormsMessage = local.objSeminarsSWOD.getFormsCustomFailedMessage(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint);
				
				structInsert(local.returnStruct.returnData,"examObj",local.strForms);
				structInsert(local.returnStruct.returnData,"strFormsMessage",local.strFormsMessage);
			}
			catch (any e) {
				structInsert(local.returnStruct.returnData,"examObj",structnew());
				reportErrorToAdmin(message="Error in EXAMPLAYERHTML5.loadFormsByLoadPoint() for EnrollmentID: #arguments.enrollmentID#",errorStruct=e,objectToDump=local);
			}
		}		
		
		return local.returnStruct;
		</cfscript>
	</cffunction>
	
	<cffunction name="getFormsFromLoadpointJSon" access="public" returntype="Any" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">
		<cfargument name="depomemberdataid" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.objSeminarsSWOD = CreateObject("component","model.admin.seminarweb.seminarWebSWOD")>
		
		<cfquery name="local.qryForms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;

			SELECT form.formid, f.formtitle, isnull(f.formintro,'') as formintro, form.isrequired, 
				form.maxminutesallowed, isnull(form.certifiedstatement,'') as certifiedstatement, 
				form.maxtriesperquestion, form.allowskipbackward, 
				form.enforceqreqstatuscorrectmaxtries, form.showimmediateanswer,
				f.passingPct, form.numResponsesPerEnrollment, form.overrideMessage, 
				dbo.sw_getNumFormAttemptsRemaining(e.enrollmentID,form.seminarFormID) as attemptsRemaining,
				form.orderBy,form.seminarFormID,0 as completePercent
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblUsers as u on u.userID = e.userID
			INNER JOIN dbo.tblSeminarsAndForms AS form ON e.seminarID = form.seminarID
				AND form.loadPoint = <cfqueryparam value="#arguments.loadPoint#" cfsqltype="cf_sql_varchar">				
			INNER JOIN formbuilder.dbo.tblForms as f on f.formid = form.formid			
				AND f.isPublished = 1 and f.isDeleted = 0
			WHERE e.enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfset local.arrForms = ArrayNew(1,false)>
		<cfloop query="local.qryForms">
			<cfset local.seminarFormDetail = local.objSeminarsSWOD.getSeminarFormDetail(seminarFormID=local.qryForms.seminarFormID,depoMemberDataID=arguments.depomemberdataid,isActive=1)>
			
			<cfset local.passingPct = 0>
			<cfif local.seminarFormDetail.recordcount gt 0>
				<cfset local.passingPct = local.seminarFormDetail.passingPct[local.seminarFormDetail.recordcount]>
			<cfelse>
				<cfset local.passingPct = -1>
			</cfif>
			
			
			<cfset local.arrFormsRow = StructNew()>
			<cfset local.arrFormsRow.formid = local.qryForms.formid>
			<cfset local.arrFormsRow.formtitle = local.qryForms.formtitle>
			<cfset local.arrFormsRow.passingPct = local.passingPct>
			<cfset local.arrFormsRow.completePercent = local.qryForms.completePercent>
			<cfset arrayAppend(local.arrForms,local.arrFormsRow)>
		</cfloop>
		
		<cfreturn local.arrForms>
	</cffunction>
	<cffunction name="loadForm" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">
		<cfargument name="formID" type="numeric" required="yes">
	
		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset structInsert(local.returnStruct,"userLoggedIn",1)>

		<cfif (local.returnStruct.userLoggedIn)>
			<cfset structInsert(local.returnStruct,"returnData",StructNew())>
			<cftry>
			
				<cfset local.objSeminarsSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
				<cfset local.xmlForm = loadFormXMLHTML5(enrollmentid=arguments.enrollmentID,loadpoint=arguments.loadPoint,formID=arguments.formID,mode='exam')>
				<cfset local.returnStructTemp = structnew()>
				<cfset local.returnStructTemp = formXMLToJSon(xmlString=local.xmlForm,loadPoint=arguments.loadPoint)>
				
				<cfset structInsert(local.returnStruct.returnData,"examObj",local.returnStructTemp)>
				
				<cfset structInsert(local.returnStruct,"formConfigObj",loadFormJSon(enrollmentid=arguments.enrollmentID,loadpoint=arguments.loadPoint,formID=arguments.formID))>
				
			<cfcatch type="any">
				<cfset structInsert(local.returnStruct.returnData,"examObj",structnew(),true)>
				<cfset reportErrorToAdmin(message="Error in EXAMPLAYERHTML5.loadForm() for EnrollmentID: #arguments.enrollmentID#",errorStruct=cfcatch,objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="loadFormXMLHTML5" access="public" returntype="xml" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">
		<cfargument name="formID" type="numeric" required="yes">		
		<cfargument name="mode" type="string" required="yes">		

		<cfset var local = StructNew()>
		
		<cfquery name="local.qryGetSFID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT TOP 1 saf.seminarFormID, saf.includeQuestions
			FROM dbo.tblSeminarsAndForms as saf 
			INNER JOIN dbo.tblEnrollments as e ON saf.seminarID = e.seminarID
			WHERE saf.formID = <cfqueryparam value="#arguments.formID#" cfsqltype="CF_SQL_INTEGER">
			AND saf.loadPoint = <cfqueryparam value="#arguments.loadPoint#" cfsqltype="CF_SQL_VARCHAR">
			AND e.enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryGetSFID.recordcount>
			<cfscript>
			local.objSeminarsSWL = CreateObject("component","model.seminarweb.SWLiveSeminars");
			local.qryEnrollment = local.objSeminarsSWL.getEnrollmentByEnrollmentID(arguments.enrollmentID)
			
			local.strArgs = StructNew();
			local.strArgs.FBFormid = arguments.formID;
			local.strArgs.FBAction = "displayFormXML";
			local.strArgs.FBdepoID = local.qryEnrollment.depomemberdataid;
			local.strArgs.FBEnrollmentID = local.qryEnrollment.enrollmentID;
			local.strArgs.insertEmptyResponse = true;
			local.strArgs.emptyResponseInfo = StructNew();
			local.strArgs.emptyResponseInfo.FBdepoID = local.qryEnrollment.depomemberdataid;
			local.strArgs.emptyResponseInfo.FBorgcode = local.qryEnrollment.orgcode;
			local.strArgs.paging = 1;
			local.strArgs.includeQuestions = local.qryGetSFID.includeQuestions;
			local.strArgs.loadPoint = arguments.loadPoint;

			local.objForm = CreateObject("component","model.formBuilder.FBForms");
			local.xmlForm = local.objForm.doAction(FBAction='displayFormXML',strArgs=local.strArgs);
			
			// SWOD exams need to remove all images from questiontext and put them in a separate questionimgs node
			local.arrQuestionsToParse = XMLSearch(local.xmlForm,"//question[contains(@questiontext,'<img ')]");
			local.rex = CreateObject("java","java.util.regex.Pattern");
			local.regex = "(<img[^>]*>)";
			for (local.i = 1; local.i lte arraylen(local.arrQuestionsToParse); local.i = local.i + 1) {
				local.qiNode = XmlElemNew(local.xmlForm,"questionimgs");
				local.imgPattern = local.rex.compile(local.regex);
				local.qtext = local.arrQuestionsToParse[local.i].xmlAttributes.questiontext;
				local.imgMatch = local.imgPattern.matcher(local.qtext);
				while (local.imgMatch.Find()) {
					for (local.gidx = 1; local.gidx lte local.imgMatch.GroupCount(); local.gidx = local.gidx + 1) {
						local.imgTag = local.imgMatch.group();
						try {
							local.imgXML = xmlParse(local.imgTag);
							local.imgNode = XmlElemNew(local.xmlForm,"img");
							local.imgNode.xmlAttributes['src'] = local.imgXML.xmlRoot.xmlAttributes.src;
							local.imgNode.xmlAttributes['width'] = local.imgXML.xmlRoot.xmlAttributes.width;
							local.imgNode.xmlAttributes['height'] = local.imgXML.xmlRoot.xmlAttributes.height;
							arrayAppend(local.qiNode.XMLChildren,local.imgNode);
						}
						catch (any e) { }
					}
				}
				local.arrQuestionsToParse[local.i].xmlAttributes.questiontext = ReReplaceNoCase(local.qtext,local.regex,'','ALL');
				if (arrayLen(local.qiNode.XMLChildren))
					arrayAppend(local.arrQuestionsToParse[local.i].xmlChildren,local.qiNode);
			}
			</cfscript>
			
			<cfif (arguments.loadPoint neq 'evaluation' or (arguments.loadPoint eq 'evaluation' and arguments.mode eq 'exam')) and isDefined("local.xmlForm.XMLRoot.xmlattributes.responseid") and len(local.xmlForm.XMLRoot.xmlattributes.responseid)>
				<cfquery name="local.qrySaveResponse" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					INSERT INTO dbo.tblSeminarsAndFormResponses (seminarFormID, enrollmentID, responseID)
					VALUES (
						<cfqueryparam value="#local.qryGetSFID.seminarFormID#" cfsqltype="CF_SQL_INTEGER">, 
						<cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">, 
						<cfqueryparam value="#local.xmlForm.XMLRoot.xmlattributes.responseid#" cfsqltype="CF_SQL_INTEGER">
					)
				</cfquery>			
			</cfif>

			<cfreturn local.xmlForm>
		<cfelse>
			<cfreturn xmlParse("<form/>")>
		</cfif>
	</cffunction>	
	
	<cffunction name="formXMLToJSon" access="private" output="no" returntype="Struct">
		<cfargument name="xmlString" type="string" required="true" />
		<cfargument name="loadPoint" type="string" required="true" />
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		
		<cfset local.arrFormNode = XMLSearch(arguments.xmlString,"//form")>
		<cfloop array="#local.arrFormNode#" index="local.thisItem">
			<cfset structInsert(local.returnStruct,"alloweditresponses",local.thisItem.xmlAttributes.alloweditresponses)>
			<cfset structInsert(local.returnStruct,"formclose",local.thisItem.xmlAttributes.formclose)>
			<cfset structInsert(local.returnStruct,"formid",local.thisItem.xmlAttributes.formid)>				
			<cfset structInsert(local.returnStruct,"formintro",local.thisItem.xmlAttributes.formintro)>
			<cfset structInsert(local.returnStruct,"formtitle",local.thisItem.xmlAttributes.formtitle)>
			<cfset structInsert(local.returnStruct,"formtype",local.thisItem.xmlAttributes.formtype)>				
			<cfset structInsert(local.returnStruct,"passingpct",local.thisItem.xmlAttributes.passingpct)>
			<cfset structInsert(local.returnStruct,"responseid",local.thisItem.xmlAttributes.responseid)>
			<cfset structInsert(local.returnStruct,"submitbtntext",local.thisItem.xmlAttributes.submitbtntext)>
		</cfloop>
		
		<cfset local.arrQuestionNode = XMLSearch(arguments.xmlString,"//questiontypes")>
		<cfset local.arrQuestions = arrayNew(1)>	
		<cfloop array="#local.arrQuestionNode#" index="local.thisItem">
			<cfloop array="#local.thisItem.xmlChildren#" index="local.thisChildrenItem">
				<cfset local.tempQuestionStruct = structNew()>
				<cfset structInsert(local.tempQuestionStruct,"questiontype",local.thisChildrenItem.xmlAttributes.questionType)>
				<cfset structInsert(local.tempQuestionStruct,"questiontypecode",local.thisChildrenItem.xmlAttributes.questiontypecode)>
				<cfset structInsert(local.tempQuestionStruct,"questiontypeid",local.thisChildrenItem.xmlAttributes.questiontypeid)>
				<cfset arrayAppend(local.arrQuestions,local.tempQuestionStruct)>
			</cfloop>
		</cfloop>
		<cfset structInsert(local.returnStruct,"questionarr",local.arrQuestions)>
		
		<cfset local.arrSectionNode = XMLSearch(arguments.xmlString,"//section")>
		<cfset local.tempSectionStruct = structNew()>
		<cfset local.arrSections = arrayNew(1)>
		<cfset local.arrPages = arrayNew(1)>
		<cfloop array="#local.arrSectionNode#" index="local.thisItem">		
			<cfset structInsert(local.tempSectionStruct,"sectionid",local.thisItem.xmlAttributes.sectionid)>
			<cfset structInsert(local.tempSectionStruct,"sectiontitle",local.thisItem.xmlAttributes.sectiontitle)>
			<cfif isDefined("local.thisItem.xmlAttributes.sectiondesc")>
				<cfset structInsert(local.tempSectionStruct,"sectiondesc",local.thisItem.xmlAttributes.sectiondesc)>
			<cfelse>
				<cfset structInsert(local.tempSectionStruct,"sectiondesc","")>
			</cfif>
			
			<cfset local.arrPageNode = XMLSearch(local.thisItem,"//section[@sectionid=#local.thisItem.xmlAttributes.sectionid#]/page")>
			<cfloop array="#local.arrPageNode#" index="local.thisChildrenItem">
				<cfset local.arrQuestionNode = XMLSearch(local.thisChildrenItem,"//section[@sectionid=#local.thisItem.xmlAttributes.sectionid#]/page/question")>
				<cfloop array="#local.arrQuestionNode#" index="local.thisQuestionNodeItem">
					<cfset local.tempPageQuestionStruct = structNew()>
					<cfset structInsert(local.tempPageQuestionStruct,"qkey",local.thisQuestionNodeItem.xmlAttributes.qkey)>
					<cfif arguments.loadPoint neq "evaluation">
						<cfset structInsert(local.tempPageQuestionStruct,"controlfield",local.thisQuestionNodeItem.xmlAttributes.controlfield)>
					</cfif>
					<cfset structInsert(local.tempPageQuestionStruct,"controlstyle",local.thisQuestionNodeItem.xmlAttributes.controlstyle)>
					<cfset structInsert(local.tempPageQuestionStruct,"displayquestionnumber",local.thisQuestionNodeItem.xmlAttributes.displayquestionnumber)>
					<cfset structInsert(local.tempPageQuestionStruct,"displayquestiontext",local.thisQuestionNodeItem.xmlAttributes.displayquestiontext)>
					<cfset structInsert(local.tempPageQuestionStruct,"isdisplayedinline",local.thisQuestionNodeItem.xmlAttributes.isdisplayedinline)>
					<cfset structInsert(local.tempPageQuestionStruct,"isrequired",local.thisQuestionNodeItem.xmlAttributes.isrequired)>
					<cfset structInsert(local.tempPageQuestionStruct,"questionid",local.thisQuestionNodeItem.xmlAttributes.questionid)>
					<cfset structInsert(local.tempPageQuestionStruct,"questiontext",local.thisQuestionNodeItem.xmlAttributes.questiontext)>
					<cfset structInsert(local.tempPageQuestionStruct,"questiontypecode",local.thisQuestionNodeItem.xmlAttributes.questiontypecode)>
					<cfset structInsert(local.tempPageQuestionStruct,"questiontypeid",local.thisQuestionNodeItem.xmlAttributes.questiontypeid)>	

					<cfset local.arrOptions = arrayNew(1)>
					<cfif arguments.loadPoint neq "evaluation">
						<cfloop from="1" to="#arraylen(local.thisQuestionNodeItem.xmlChildren)#" index="local.thisOptionNodeItem">
							<cfset local.tempQuestionOptionStruct = structNew()>
							<cfset local.arrOptionNode = XMLSearch(local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem],"//section[@sectionid=#local.thisItem.xmlAttributes.sectionid#]/page/question[@questionid=#local.thisQuestionNodeItem.xmlAttributes.questionid#]/options")>
							<cfif arraylen(local.arrOptionNode)>								
								<cfloop from="1" to="#arraylen(local.arrOptionNode)#" index="local.thisOptionItem">
		 							<cfif not structKeyExists(local, "tempQuestionOptionStruct")>
		 								<cfset local.tempQuestionOptionStruct = structNew()>	 								
		 							</cfif>	 							
		 							<cfset structInsert(local.tempQuestionOptionStruct,"inputtext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionItem].xmlAttributes.inputtext)>
									<cfset structInsert(local.tempQuestionOptionStruct,"optionid",local.thisQuestionNodeItem.xmlChildren[local.thisOptionItem].xmlAttributes.optionid)>
									<cfset structInsert(local.tempQuestionOptionStruct,"optiontext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionItem].xmlAttributes.optiontext)>
									<cfset structInsert(local.tempQuestionOptionStruct,"showinput",local.thisQuestionNodeItem.xmlChildren[local.thisOptionItem].xmlAttributes.showinput)>
									<cfset structInsert(local.tempQuestionOptionStruct,"optionsxArr",arrayNew(1))>	
									<cfset arrayAppend(local.arrOptions,local.tempQuestionOptionStruct)>
									<cfset structDelete(local,"tempQuestionOptionStruct")>								
								</cfloop>
								<cfbreak>
							<cfelse>
								<cfset structInsert(local.tempQuestionOptionStruct,"inputtext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.inputtext)>
								<cfset structInsert(local.tempQuestionOptionStruct,"optionid",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.optionid)>
								<cfset structInsert(local.tempQuestionOptionStruct,"optiontext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.optiontext)>
								<cfset structInsert(local.tempQuestionOptionStruct,"showinput",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.showinput)>	
								<cfset structInsert(local.tempQuestionOptionStruct,"optionsxArr",arrayNew(1))>	
								<cfset arrayAppend(local.arrOptions,local.tempQuestionOptionStruct)>
							</cfif>
						</cfloop>
					<cfelse>
						<cfloop from="1" to="#arraylen(local.thisQuestionNodeItem.xmlChildren)#" index="local.thisOptionNodeItem">
							<cfset local.tempQuestionOptionStruct = structNew()>	
							<cfset local.arrOptionsX = arrayNew(1)>	
							<cfif not structIsEmpty(local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes)>
								<cfset structInsert(local.tempQuestionOptionStruct,"inputtext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.inputtext)>
								<cfset structInsert(local.tempQuestionOptionStruct,"optionid",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.optionid)>
								<cfset structInsert(local.tempQuestionOptionStruct,"optiontext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.optiontext)>
								<cfset structInsert(local.tempQuestionOptionStruct,"showinput",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.showinput)>	
								<cfloop from="1" to="#arraylen(local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlChildren)#" index="local.thisOptionxItem">
									<cfset local.tempOptionxStruct = structNew()>	
									<cfif not structIsEmpty(local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlChildren[local.thisOptionxItem].xmlAttributes)>	
										<cfset structInsert(local.tempOptionxStruct,"optiontext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlChildren[local.thisOptionxItem].xmlAttributes.optiontext)>
										<cfset structInsert(local.tempOptionxStruct,"optionid",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlChildren[local.thisOptionxItem].xmlAttributes.optionid)>
										<cfset arrayAppend(local.arrOptionsX,local.tempOptionxStruct)>
									</cfif>
								</cfloop>	
								<cfset structInsert(local.tempQuestionOptionStruct,"optionsxArr",local.arrOptionsX)>						
							</cfif>
							<cfset arrayAppend(local.arrOptions,local.tempQuestionOptionStruct)>								
						</cfloop>				
					</cfif>
					<cfset structDelete(local,"tempQuestionOptionStruct")>
					<cfset structInsert(local.tempPageQuestionStruct,"optionsarr",local.arrOptions)>							
					<cfset arrayAppend(local.arrPages,local.tempPageQuestionStruct)>					
				</cfloop> <!--- // local.arrQuestionNode --->
				<cfbreak>
			</cfloop> <!---// local.arrPageNode --->
			<cfset structInsert(local.tempSectionStruct,"pagesArr",local.arrPages)>
			
			<!--- Ensure the player can handle multiple sections --->
			<cfset arrayAppend(local.arrSections,local.tempSectionStruct)>
			<cfset structDelete(local,"tempSectionStruct")>	
			<cfset local.tempSectionStruct = structNew()>
			<cfset local.arrPages = []>
			
		</cfloop>	<!---// local.arrSectionNode --->			

		<cfset structInsert(local.returnStruct,"sectionObj",local.arrSections)>
		
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getCurrentTime" access="remote" returntype="date" output="false">
		<cfreturn now()>
	</cffunction>
	
	<cffunction name="getSystemData" access="remote" returntype="struct" output="false">
		
		<cfset var local = StructNew()>
		<cfset local.argumentsStruct = getHttpRequestData()>
		<cfset local.returnStruct = StructNew()>		
		<cfset local.returnstruct.success = true>
		<cfset local.returnstruct.language = ''>
		<cfset local.returnstruct.userAgent = ''>
		<cfset local.returnstruct.hostAddress = ''>
		<cfif structKeyExists(local.argumentsStruct.headers, "Accept-Language")>
			<cfset local.returnstruct.language = local.argumentsStruct.headers['Accept-Language']>
		</cfif>
		<cfif structKeyExists(local.argumentsStruct.headers, "User-Agent")>
			<cfset local.returnstruct.userAgent = local.argumentsStruct.headers['User-Agent']>	
		</cfif>
		<cfif structKeyExists(local.argumentsStruct.headers, "Host")>
			<cfset local.returnstruct.hostAddress = local.argumentsStruct.headers['Host']>
		</cfif>	
			
		<cfreturn local.returnstruct>
	</cffunction>
	
	<cffunction name="saveSeminarStartTime" access="remote" returntype="struct" output="false">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.currentTime = getCurrentTime()>
		<cfset local.requestData = deserializeJSON(toString( getHttpRequestData().content ))>		
		<cfset local.enrollmentID = local.requestData.enrollmentid>
		
		<cftry>
			<cfquery name="local.objEnrollment"  datasource="#application.dsn.tlasites_seminarweb.dsn#">
				
				update tblEnrollments set dtInstanceStartDate =  CONVERT(VARCHAR(23), <cfqueryparam value="#local.currentTime#" cfsqltype="cf_sql_timestamp">  , 120) where enrollmentID =  <cfqueryparam value="#local.enrollmentID#" cfsqltype="cf_sql_integer">
				
				select convert(varchar(23), dtInstanceStartDate , 120) as dtInstanceStartDate from tblEnrollments where enrollmentID =  <cfqueryparam value="#local.enrollmentID#" cfsqltype="cf_sql_integer">		
				
			</cfquery>	
				
			<cfset local.returnstruct.success = true>
			<cfset local.returnstruct.savedTime = local.objEnrollment['dtInstanceStartDate']>
		<cfcatch type="Any">
			<cfset local.returnstruct.success = false>
			<cfset reportErrorToAdmin("Error reported from SWOD Player for Enrollment ID: #local.enrollmentID#",cfcatch,arguments)>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="checkSeminarInstanceExists" access="remote" returntype="struct" output="false">
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.requestData = deserializeJSON(toString(getHttpRequestData().content))>

		<!--- this is protect against bots calling this function directly with no valid request data. we dont need the exception sent to us. --->
		<cfif NOT isDefined("local.requestData.enrollmentid")>
			<cfset local.returnstruct.success = false>
		<cfelse>			
			<cfset local.enrollmentID = local.requestData.enrollmentid>
			<cfset local.dateInstanceStarted = local.requestData.dateInstanceStarted>

			<cftry>
				<cfquery name="local.objEnrollment" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					select enrollmentID 
					from dbo.tblEnrollments 
					where convert(varchar(23), dtInstanceStartDate , 120) > convert(varchar(23), <cfqueryparam value="#local.dateInstanceStarted#" cfsqltype="cf_sql_timestamp">, 120) 
					AND enrollmentID = <cfqueryparam value="#local.enrollmentID#" cfsqltype="cf_sql_integer">
				</cfquery>	
			
				<cfset local.returnstruct.isExist = 0>
				<cfif local.objEnrollment.recordcount>
					<cfset local.returnstruct.isExist = 1>
				</cfif>
			
				<cfset local.returnstruct.success = true>
			<cfcatch type="Any">
				<cfset local.returnstruct.success = false>
				<cfset reportErrorToAdmin("Error reported from SWOD Player for Enrollment ID: #local.enrollmentID#",cfcatch,arguments)>
			</cfcatch>
			</cftry>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="reportErrorToAdmin" access="private" returntype="boolean" output="no">
		<cfargument name="message" type="string" required="Yes">
		<cfargument name="errorStruct" type="any" required="yes" hint="changed to any to support retro players still sending in strings">
		<cfargument name="objectToDump" type="struct" required="no">
		
		<cfset var local = structNew()>
		
		<cfif isdefined("arguments.objectToDump") and isStruct(arguments.objectToDump) and structCount(arguments.objectToDump)>
			<cfif (isDefined("arguments.objectToDump.ERRORSTRUCT.message") and FindNoCase("All Streaming server ports and protocols failed.",arguments.objectToDump.ERRORSTRUCT.message))
				OR (isDefined("arguments.objectToDump.ERRORSTRUCT.faultDetail") and FindNoCase("NetConnection.Call.Failed: HTTP: Failed",arguments.objectToDump.ERRORSTRUCT.faultDetail))>
				<!--- do nothing. supress error message. Nothing will be done about it. --->
			<cfelse>
				<cfset application.objError.sendError(cfcatch=arguments.errorStruct, customMessage=arguments.message, domain="seminarweb", objectToDump=arguments.objectToDump )>
			</cfif>
		<cfelse>
			<cfset application.objError.sendError(cfcatch=arguments.errorStruct, customMessage=arguments.message, domain="seminarweb" )>
		</cfif>

		<cfreturn true>
	</cffunction>
	<cffunction name="loadFormJSon" access="private" returntype="Struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">
		<cfargument name="formID" type="numeric" required="yes">		

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>
		
		<cfquery name="local.qryGetSFID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT TOP 1 saf.seminarFormID, saf.includeQuestions, saf.maxminutesallowed, 
				isnull(saf.certifiedstatement,'') as certifiedstatement, saf.maxtriesperquestion,  
				saf.allowskipbackward,  saf.enforceqreqstatuscorrectmaxtries, 
				saf.showimmediateanswer, f.passingPct, saf.numResponsesPerEnrollment,
				dbo.sw_getNumFormAttemptsRemaining(<cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">,saf.seminarFormID) as attemptsRemaining
			FROM dbo.tblSeminarsAndForms as saf 
			INNER JOIN formbuilder.dbo.tblForms as f on f.formid = saf.formid	
			WHERE saf.formID = <cfqueryparam value="#arguments.formID#" cfsqltype="CF_SQL_INTEGER">
			AND saf.loadPoint = <cfqueryparam value="#arguments.loadPoint#" cfsqltype="CF_SQL_VARCHAR">
		</cfquery>
		
		<cfif local.qryGetSFID.recordcount>
			<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWODSeminars").getEnrollmentByEnrollmentID(arguments.enrollmentID)>

			<cfscript>
			// get form xml from FormBuilder
			local.strArgs = StructNew();
			local.strArgs.FBFormid = arguments.formID;
			local.strArgs.FBAction = "displayFormXML";
			local.strArgs.FBdepoID = local.qryEnrollment.depomemberdataid;
			local.strArgs.FBEnrollmentID = local.qryEnrollment.enrollmentID;
			local.strArgs.insertEmptyResponse = true;
			local.strArgs.emptyResponseInfo = StructNew();
			local.strArgs.emptyResponseInfo.FBdepoID = local.qryEnrollment.depomemberdataid;
			local.strArgs.emptyResponseInfo.FBorgcode = local.qryEnrollment.orgcode;
			local.strArgs.paging = 1;
			local.strArgs.includeQuestions = local.qryGetSFID.includeQuestions;
			local.strArgs.loadPoint = arguments.loadPoint;

			// append seminarweb settings
			structInsert(local.returnStruct,'maxminutesallowed',local.qryGetSFID.maxminutesallowed);
			structInsert(local.returnStruct,'certifiedstatement',local.qryGetSFID.certifiedstatement);
			structInsert(local.returnStruct,'maxtriesperquestion',local.qryGetSFID.maxtriesperquestion);
			structInsert(local.returnStruct,'allowskipbackward',local.qryGetSFID.allowskipbackward);
			structInsert(local.returnStruct,'enforceqreqstatuscorrectmaxtries',local.qryGetSFID.enforceqreqstatuscorrectmaxtries);
			structInsert(local.returnStruct,'showimmediateanswer',local.qryGetSFID.showimmediateanswer);
			structInsert(local.returnStruct,'passingPct',local.qryGetSFID.passingPct);
			structInsert(local.returnStruct,'numResponsesPerEnrollment',local.qryGetSFID.numResponsesPerEnrollment);
			structInsert(local.returnStruct,'attemptsRemaining',local.qryGetSFID.attemptsRemaining);
			</cfscript>
		</cfif>
		<cfreturn local.returnStruct>		
	</cffunction>
	<cffunction name="saveFormResponse" access="public" returntype="struct" output="no">
		 <cfargument name="json" type="string" />		
		
		<cfset var local = StructNew()>
		<cfset local.objForms = CreateObject("component","model.formBuilder.FBForms")>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnstruct["returnData"] = ArrayNew(1)>	
		<cfset local.returnstruct["success"] = true>
		<cfset local.argumentsStruct = structnew() />
		
		<cfif isDefined("arguments.json") and len(arguments.json)>
			<cfset local.argumentsStruct = deserializeJSON(arguments.json)>
		<cfelse>
			<cfset local.argumentsStruct = deserializeJSON(deserializeJSON(getHttpRequestData().content).params.json)>
		</cfif>
	
		<!--- 
		FB's postFormDetail function needs to called once per question. 
		We assume the SWOD player only has 1 question per page.
		Take the responseArray -- the form elements -- and make a formVars collection to pass into FB.
		If responseArray is empty, nothing was selected so there is nothing to record.
		--->
		<cfif arrayLen(local.argumentsStruct.responseArray)>
			<cfif local.argumentsStruct.responseID gt 0>
				<cfset local.thisQuestionResponse = { responseID=local.argumentsStruct.responseID }>
				<cfloop array="#local.argumentsStruct.responseArray#" index="local.thisResponse">
					<!--- if a name comes in with no value, it defaults to blank --->
					<cfif NOT StructKeyExists(local.thisResponse,"value")>
						<cfset local.thisResponse.value="">
					</cfif>
					<cfset structInsert(local.thisQuestionResponse, local.thisResponse.name, local.thisResponse.value)>
				</cfloop>
				<cftry>
					<cfset ArrayAppend(local.returnstruct.returnData,local.objForms.doAction("postFormDetail",local.thisQuestionResponse))>
				<cfcatch type="Any">
					<cfset local.arguments = arguments>
					<cfset reportErrorToAdmin(message="Error in EXAMPLAYERHTML5.saveFormResponse()",errorStruct=cfcatch,objectToDump=local)>
					<cfset local.returnstruct["success"] = false>
				</cfcatch>
				</cftry>				
			<cfelse>
				<cfset local.returnstruct["success"] = false>
			</cfif>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>	
	<cffunction name="setupLogAccessIDs" access="private" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>

		<cfstoredproc procedure="swod_createLogAccessIDs" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
			<cfprocresult name="local.qrySeminarAccessID" resultset="1">
			<cfprocresult name="local.qryFileAccessID" resultset="2">
			<cfprocresult name="local.qryFileAccessDetails" resultset="3">
		</cfstoredproc>

		<cfset local.seminarAccessID = local.qrySeminarAccessID.logAccessID>
		<cfset local.files = StructNew()>
		<cfloop query="local.qryFileAccessID">
			<cfquery name="local.qryfiledetails" dbtype="query">
				select *
				from [local].qryFileAccessDetails
				where fileID = #local.qryFileAccessID.fileID#
			</cfquery>
			<cfset local.files[local.qryFileAccessID.fileID] = StructNew()>
			<cfset local.files[local.qryFileAccessID.fileID]['accessid'] = local.qryFileAccessID.accessID>
			<cfset local.files[local.qryFileAccessID.fileID]['lasttimecode'] = val(local.qryFileAccessID.lastTimeCode)>
			<cfset local.files[local.qryFileAccessID.fileID]['accessDetails'] = local.objSWOD.bitORFiles(local.qryfiledetails)>
		</cfloop>
		
		<cfreturn local>
	</cffunction>	
	<cffunction name="saveProgressFromPlayer" access="remote" returntype="struct" output="no">
		<cfargument name="json" type="string" />

		<cfset var local = StructNew()>
		<cfset local.CRLF = Chr(13) & chr(10)>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = 1>
		<cfset local.argumentsStruct = structnew() />
		<cfset local.argumentsStruct = deserializeJSON(arguments.json) />		

		<cftry>
			<cfif local.argumentsStruct.sendReminderEmailFlag >
				<cfset sendReminderEmail(local.argumentsStruct.logAccessID,local.argumentsStruct.includeLoginDetails)>
			</cfif>

			<cfif local.returnStruct.userLoggedIn>
				<cfif arraylen(local.argumentsStruct.progressObj.fileArray)>
					<cfloop index="local.currentfile" from="1" to="#arraylen(local.argumentsStruct.progressObj.fileArray)#">
						<cfstoredproc procedure="swod_updateLogAccessSWODFiles" datasource="#application.dsn.tlasites_seminarweb.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.fileArray[local.currentfile].timespent#" null="No">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.fileArray[local.currentfile].lasttimecode#" null="No">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.argumentsStruct.progressObj.fileArray[local.currentfile].accessDetails#" null="No">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.fileArray[local.currentfile].fileLogAccessID#" null="No">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.logAccessID#" null="No">
						</cfstoredproc>
					</cfloop>
				</cfif>

				<cfset local.activityLogText = createObject("java","java.lang.StringBuffer")>
				<cfif arraylen(local.argumentsStruct.progressObj.activityLogArray) gt 0>
					<cfloop index="local.currentrow" from="1" to="#arraylen(local.argumentsStruct.progressObj.activityLogArray)#">
						<cfset local.activityLogText.append(local.argumentsStruct.progressObj.activityLogArray[local.currentrow]["dateentered"] & chr(9) & local.argumentsStruct.progressObj.activityLogArray[local.currentrow]["message"] & local.CRLF)>
					</cfloop>
				</cfif>

				<cfset local.debugLogText = createObject("java","java.lang.StringBuffer")>
				<cfif arraylen(local.argumentsStruct.progressObj.debugLogArray) gt 0>
					<cfloop index="local.currentrow" from="1" to="#arraylen(local.argumentsStruct.progressObj.debugLogArray)#">
						<cfset local.debugLogText.append(local.argumentsStruct.progressObj.debugLogArray[local.currentrow]["dateentered"] & chr(9) & local.argumentsStruct.progressObj.debugLogArray[local.currentrow]["message"] & local.CRLF)>
					</cfloop>
				</cfif>
				<cfif arraylen(local.argumentsStruct.progressObj.fileArray)>
					<cfstoredproc procedure="swod_updateLogAccessSWOD" datasource="#application.dsn.tlasites_seminarweb.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.timespent#" null="No">
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.activityLogText.toString()#" null="no">
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.debugLogText.toString()#" null="No">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.logAccessID#" null="No">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.enrollmentID#" null="No">
					</cfstoredproc>
				</cfif>
	
				<cfset local.returnStruct["returnData"] = 1>
			</cfif>
		<cfcatch type="any">
			<cfif isDefined("local.argumentsStruct.progressObj.enrollmentID")>
				<cfset reportErrorToAdmin("Error in ExamPlayer.saveProgressFromPlayer() for EnrollmentID: #local.argumentsStruct.progressObj.enrollmentID#",cfcatch,local)>
			</cfif>
			<cfset local.returnStruct["returnData"] = 0>
		</cfcatch>
		</cftry>
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="sendReminderEmail" access="private" returntype="void" output="no">
		<cfargument name="logAccessID" type="numeric" required="yes">
		<cfargument name="includeLoginDetails" type="boolean" required="yes">
		
		<cfset createObject("component","model.seminarweb.SWODEmails").sendSaveAndExitEmail(arguments.logAccessID,arguments.includeLoginDetails)>
	</cffunction>
	<cffunction name="gradeExam" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="responseID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnstruct["returnData"] = StructNew()>

		<cftry>
			<cfset local.objSeminarsSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
			<cfset local.qryExamResult = local.objSeminarsSWOD.completeExam(enrollmentid=arguments.enrollmentid,responseid=arguments.responseID)>
			<cfset structInsert(local.returnStruct.returnData,"pctcorrect",local.qryExamResult.passingPct)>
			<cfset structInsert(local.returnStruct.returnData,"passfail",local.qryExamResult.passFail)>
			<cfset structInsert(local.returnStruct.returnData,"attemptsremaining",local.qryExamResult.attemptsRemaining)>
			<cfset structInsert(local.returnStruct.returnData,"numcorrect",local.qryExamResult.numCorrect)>
			<cfset structInsert(local.returnStruct.returnData,"numgiven",local.qryExamResult.numGiven)>
		<cfcatch type="Any">
			<cfset reportErrorToAdmin(message="Error in EXAMPLAYERHTML5.gradeExam()",errorStruct=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>
			
		<!--- Check if this is SWLive Seminar --->
		<cftry>
			<cfset local.qryEnrollment = local.objSeminarsSWOD.getEnrollmentByEnrollmentID(enrollmentid=arguments.enrollmentid)>
			<cfstoredproc procedure="swl_getSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.qryEnrollment.seminarID)#" null="No">
				<cfprocresult name="local.qrySWLDetails" resultset="1">
			</cfstoredproc>
			<cfif local.qrySWLDetails.recordCount gt 0>
				<cfset local.objSeminarsSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
				<cfset local.strReturn = local.objSeminarsSWL.checkCompletion(enrollmentid=arguments.enrollmentid,passFail=local.qryExamResult.passFail)>
			</cfif>
		<cfcatch type="Any">
			<cfset reportErrorToAdmin(message="Error in EXAMPLAYERHTML5.gradeExam() SWLive",errorStruct=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="isLoggedIn" access="private" returntype="numeric" output="no">
		<cfreturn 1>
	</cffunction>
	<cffunction name="finalCheckSeminarforCompletion" access="remote" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="Yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="userTimeSpent" type="numeric" required="yes">

		<cfset var local = Structnew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfif local.returnStruct.userLoggedIn>
			<cftry>
				<cfset local.strCompletion = checkSeminarforCompletion(arguments.mcproxy_siteCode,arguments.enrollmentID)>
				<cfset local.returnStruct.rawData = local.strCompletion>
 				<cfif local.strCompletion.returnData.isCompleted is not 1 
					OR local.strCompletion.returnData.allPostTestCompleted is 0>
					<cfthrow>
				</cfif>

				<!--- convert totalTimeSpent from seconds to minutes --->
				<cfset local.minutes = round(local.strCompletion.returnData.totalTimeSpent / 60)>

				<cfstoredproc procedure="swod_recordCompletion" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.userTimeSpent#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.minutes#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#" null="No">
				</cfstoredproc>
			<cfcatch type="any">
				<cfset local.returnStruct["returnData"] = 0>
					<cfset local.arguments = arguments>
					<cfset reportErrorToAdmin(message="Error in EXAMPLAYERHTML5.finalCheckSeminarforCompletion()",errorStruct=cfcatch,objectToDump=local)>
				<cfreturn local.returnStruct>
			</cfcatch>
			</cftry>
			<cfset local.returnStruct["returnData"] = 1>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="emailCertificate" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="emailToUse" type="string" required="yes">
		
		<cfset var local = StructNew()>		
		<cfset local.emailStruct = structNew()>
		
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnStruct["success"] = 0>
		
		<cfif isValid("email",arguments.emailToUse) AND arguments.enrollmentID > 0>
			<cftry>
				<cfset CreateObject("component","model.seminarweb.SWCertificates").sendCertificateByEmail(enrollmentID=arguments.enrollmentID, 
				performedBy=session.cfcuser.memberdata.depomemberdataID, outgoingType="manualCertificate", emailToUse=arguments.emailToUse)>
				<cfset local.returnStruct["success"] = 1>
			<cfcatch type="any">
				<cfset local.returnStruct["success"] = 0>
			</cfcatch>
			</cftry>			
		<cfelse>
			<cfset local.returnStruct["success"] = 0>
		</cfif>	

		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="getMaterialsDocument" access="private" returntype="Any" output="no">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="enrollmentID" type="numeric" required="true">
		<cfargument name="userType" type="string" required="true">
		<cfargument name="sitecode" type="string" required="true">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		
		<cfset local.materialsDocObj = local.objSWL.getMaterialsDocument(seminarID=arguments.seminarID, enrollmentID=arguments.enrollmentID, userType=arguments.userType, sitecode=arguments.sitecode)>	
		<cfset local.returnStruct = application.objCommon.queryToArrayOfStructures(local.materialsDocObj)>		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="emailMaterials" access="remote" returntype="struct" output="no">
		<cfargument name="json" type="string" />
		
		<cfset var local = StructNew()>		

		<cfset local.emailStruct = structNew()>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnStruct["success"] = 0>
		
		<cfif isDefined("arguments.json")>
			<cfset local.argumentsStruct = deserializeJSON(arguments.json)>
		<cfelseif structKeyExists(getHttpRequestData(),"content") AND getHttpRequestData().content NEQ ''>
			<cfset local.argumentsStruct = deserializeJSON(deserializeJSON(getHttpRequestData().content).params.json)>
		<cfelse>
			<cfset local.argumentsStruct.emailToUse = arguments.emailToUse />
			<cfset local.argumentsStruct.signUpOrgCode = arguments.signUpOrgCode />
			<cfset local.argumentsStruct.seminarName = arguments.seminarName />
			<cfset local.argumentsStruct.uniqueCode =  arguments.uniqueCode>
			<cfset local.argumentsStruct.fromEmail = arguments.supportEmail>
		</cfif>
		<cfset var local = structNew()>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.swlcode = reReplaceNoCase(local.argumentsStruct.uniqueCode,"[^A-Z]","","ALL")>
		<cfset local.verifyReg = local.objSWL.verifySWLCode(local.swlcode)>
		<cfset local.emailStruct = structNew()>
		<cfset local.strSeminarEnrolled = local.objSWL.getEnrollmentByEnrollmentID(local.verifyReg.enrollmentID)>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.strSeminarEnrolled.signUpOrgCode)>						
		<cfset local.materialsDoc = local.objSWL.getMaterialsDocument(seminarID=local.verifyReg.seminarID, enrollmentID=val(local.verifyReg.enrollmentID), userType=local.verifyReg.SWLUserType, sitecode=local.mc_siteInfo.sitecode)>
		<cfset local.strAssociation = local.objSWP.getAssociationDetails(local.mc_siteInfo.sitecode)>
		
		<cfset local.emailFrom = ''>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["success"] = 0>		
		<cfset local.memberID = val(local.strSeminarEnrolled.MCMemberID)>
		<cfif NOT local.memberID>
			<cfset local.memberID = local.mc_siteInfo.sysMemberID>
		</cfif>

		
		<cfif len(local.strSeminarEnrolled.email)>
			<cfif NOT structKeyExists(local.strAssociation, "qryAssociation")>
				<cfset local.returnStruct["success"] = 0>
			<cfelse>
				<cfset local.qrySWP = local.strAssociation.qryAssociation>
				<cfset local.semWeb.qrySWP = local.qrySWP>
				<cfset local.emailFrom = local.qrySWP.emailFrom>
				
				<cfif isLoggedIn() and isValid("email",local.argumentsStruct.emailToUse)>			
					
					<cfif local.materialsDoc.recordCount>
						<cfsavecontent variable="local.emailStruct.emailTitle">
							<cfoutput>
							<div style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:3px;text-align:center;"><i>#local.mc_siteInfo.siteName#</i></div>
							<div style="text-align:center;">Program Materials for #encodeForHTML('#local.strSeminarEnrolled.seminarName#')#</div>
							</cfoutput>
						</cfsavecontent>			
		
						<cfsavecontent variable="local.emailStruct.html">
							<cfoutput>
								<cfloop query="local.materialsDoc">
									<cfset local.eachMaterialsObj = local.materialsDoc>	
									<cfset local.LinkToProgram = "#application.objPlatform.isRequestSecure() ? 'https' : 'http'#://#local.mc_siteInfo.mainhostname#/?pg=semwebCatalog&panel=downloadMaterialsDoc&uniqueCode=#local.swlcode#&documentID=#local.eachMaterialsObj.documentID#">
		
									<div style="font-size:14px;padding-left:5px;margin-bottom:20px;"><a  style="font-size: 14px;  padding: 5px 5px; background: grey; color: ##fff; border-radius: 5px; text-decoration: none;" href="#local.LinkToProgram#" target="_blank">Download</a> &nbsp;#local.eachMaterialsObj.docTitle#</div>
								</cfloop>						
							</cfoutput>
						</cfsavecontent>
						<cfset local.emailStruct.templateDisp = application.objEmailWrapper.wrapMessage(emailTitle=local.emailStruct.emailTitle, emailContent=local.emailStruct.html, sitecode=local.strSeminarEnrolled.signUpOrgCode)>
						
						<cfscript>
							local.arrEmailTo = [];
							local.toEmailArr = listToArray(replace(local.strSeminarEnrolled.email,",",";","all"),';');
							for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
								local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
							}
							
							if (arrayLen(local.arrEmailTo)) {
								local.strEmailResult = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name=local.emailFrom, email='<EMAIL>' },
									emailto=local.arrEmailTo,
									emailreplyto='',
									emailsubject="Program Materials: #local.strSeminarEnrolled.seminarName#",
									emailtitle=local.emailStruct.emailTitle,
									emailhtmlcontent=local.emailStruct.html,
									siteID=local.mc_siteInfo.siteid,
									memberID=local.memberID,
									messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SWLMATERIALS"),
									sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
								);
							}
						</cfscript>	
						<cfset local.returnStruct["success"] = 1>	
					<cfelse>
						<cfset local.returnStruct["success"] = 0>
					</cfif>						
				<cfelse>
					<cfset local.returnStruct["success"] = 0>
				</cfif>
			</cfif>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>
</cfcomponent>