<cfoutput>

    <cfset local.hasZoneP = 0>
    <cfset local.hasZoneQ = 0>
    <cfset local.hasZoneR = 0>
	<cfset local.zone = "P"><!-- Footer Logo -->
	<cfset local.zonePcontent = ''>
	<cfif application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1)>
		<cfset local.zonePcontent = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))>
        <cfset local.hasZoneP = 1>
    </cfif>

	<cfset local.zone = "Q">
	<cfset local.zoneQcontent = ''>
	<cfif application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1)>
		<cfset local.zoneQcontent =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))>
        <div id="zoneQObj" class="hide">	
            #local.zoneQcontent#
        </div>
        
         <cfset local.hasZoneQ = 1>
    </cfif>

	<cfset local.zone = "R"><!-- Contact Us Footer -->
	<cfset local.zoneRcontent = ''>
	<cfif application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1)>
		<cfset local.zoneRcontent = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))>
    <cfset local.hasZoneR = 1>
    </cfif>
    <cfset local.PageClass = ''>
    <cfif local.hasZoneP eq 0 && local.hasZoneQ eq 0 && local.hasZoneR eq 0>
         <cfset local.PageClass = 'hidden'>
    </cfif>
	
	
	<!-- Footer Start -->
	<div class="footer #local.PageClass#">
         <div class="container">
            <div class="row d-flex-wrap">
               <div class="col1 contact-links zonePWrap">
                    #local.zonePcontent#
               </div>
               <div class="col2 zoneQWrap hidden">
                  <div class="footer-links-wrap">
                     
                  </div>
               </div>
            </div>
         </div>
         <div class="copyright-block">
            <div class="container">
               <div class="row-fluid">
                  <div class="copyright clearfix">
                     #local.zoneRcontent#
                  </div>
               </div>
            </div>
         </div>
      </div>
	 <!-- Footer End -->
</cfoutput>