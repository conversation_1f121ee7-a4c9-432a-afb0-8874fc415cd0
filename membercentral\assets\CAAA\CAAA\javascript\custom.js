$(document).ready(function () {
    strMenumain = '';
    
    $('#menuObj > ul > li').each(function (key,val) {
      if($(this).find(' > ul').length > 0){
        strSubMenuCnt = '';
        $(this).find(' > ul > li').each(function (k1,v1) {
          strSubTitle = isset($(this).find(' >ul >li').eq(0).html())?$(this).find(' >ul >li').eq(0).html():''; 
          $(this).find(' >ul >li').eq(0).remove();
          strOtherLinks = isset($(this).find(' >ul').html())?$(this).find(' >ul').html():''; 
          strSubMenuCnt += `
                <div class="mainMenuMob-col">
                  <ul class="mainMenuMob-list">
                      <li>
                        <span class="SubHeading">${strSubTitle}</span>
                      </li>
                      ${strOtherLinks}
                  </ul>
                </div>        
            
            `;
        });
        $(this).find(' > ul').remove();
        strMainLink = isset( $(this).html())?$(this).html():''; 
        strMenumain += `
            <li>${strMainLink}
              <ul>
                <li class="closeBox textUl">
                  <div class="mainMenuMob">                    
                      ${strSubMenuCnt}
                  </div>
                </li>
              </ul>
            </li>
        `;

      }else{
        strCnt = isset($(this).html())?$(this).html():''; 
        strMenumain += `
          <li>${strCnt}</li>
        `;
      }

    });

    if(strMenumain != ''){
      $('.navLiList').after(strMenumain);
      setUpMenu();
      $(window).on("resize", function (e) {
         setUpMenu();
      });
    }else{
      $('.rightMenus').removeClass('hidden');
    }

  //Zone A
  var zoneAObj = $('#zoneAObj').html();
  logoFound = 0;
  if(zoneAObj != undefined){
    $('#zoneAObj > ul > li').each(function(key,val){
      objLiCnt =  $(this);
      strCnt = isset(objLiCnt.html())?objLiCnt.html():''; 
      
      if(objLiCnt.find('img').length > 0 && logoFound == 0){
        $('.navListWrap').before(strCnt);
        $('.navListWrap').prev().addClass('navbar-brand');
        logoFound = 1;
      }else if(objLiCnt.find('img').length > 0 && logoFound == 1){
        $('.navListWrap .top-action-wrapper').prepend(strCnt);
        $('.navListWrap .top-action-wrapper .centered-btn-wrap').prev().addClass('logo-mobile');
        logoFound = 2;
      }else{         
        $('.headerMyCaaa').html(strCnt);
        $('.navListWrap .centered-btn-wrap').html(strCnt);
        $('.headerMyCaaa > a').addClass('nav-member-center toggle-form');
        $('.navListWrap .centered-btn-wrap > a').addClass('CAAAButton');

        $('.headerlogin .nav-member-center').each(function() {
          var $this = $(this);
          var textOnly = $this.clone().find('i').remove().end().text().trim();

          var icon = $this.find('i').prop('outerHTML');
          if(icon == undefined || icon == null){
            icon = '';
          }
          $this.html(icon + '<p>' + textOnly + '</p>');
        });
      }
    });
  }

  $('.zonePWrap > h3').addClass('footer-title');
  if(isset($('.zonePWrap > ul').eq(1).html())){
    $('.zonePWrap > ul').eq(1).addClass('social-list')
  }

  var zoneQObj = $('#zoneQObj').html();
  if(zoneQObj != undefined){
    $('#zoneQObj  > ul').each(function(key,val){
      objLiCnt =  $(this);
      strTitle1 = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():''; 
      if(isset(objLiCnt.children().eq(0).html())){
        objLiCnt.children().eq(0).remove();
      }
      strLis = isset(objLiCnt.html())?objLiCnt.html():'';
      strClass = '';
      if(key > 3){
        strClass = "ZoneQMargin";
      }
      
      strZoneQCnt = `
        <div class="footer-links ${strClass}">
            <h3>${strTitle1}</h3>
            <ul>
                ${strLis}
            </ul>
            
        </div> 
      ` ;
      $('.zoneQWrap .footer-links-wrap').append(strZoneQCnt);
      $('.zoneQWrap').removeClass('hidden');
    });
  }


  //Home page Banner
  if($('#zoneMainObj').html() != undefined)
  var zoneMainObj = $('#zoneMainObj').html().trim();
  liCount = 0;
  if(zoneMainObj != undefined && zoneMainObj.length > 0){
    $('#zoneMainObj > ul').each(function(key,val){
      objLiCnt =  $(this);
      liCount = liCount + 1;
      strImage1 = '';
      strImage2 = '';
      strTitle = '';
      strDesc = '';
      strLink1 = '';
      strLink2 = '';

      $(objLiCnt.find('li')).each(function(key1,val1){
        if($(val1).find('img').length > 0 && strImage1 == ''){
          strImage1 = $(val1).html();
        }
        if($(val1).find('img').length > 0 && strImage1 != '' && key1 > 0){
          strImage2 = $(val1).html();
        }

        if($(val1).find('a').length > 0 && strLink1 == ''){
          strLink1 = $(val1).html();
        }else if($(val1).find('a').length > 0 && strLink1 != ''){
          strLink2 = $(val1).html();
        }
        
        if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle != '' && strDesc == ''){
          strDesc = $(val1).html();
        } 

        if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle == ''){
          strTitle = $(val1).html();
        }    

      });
      secondImageBanner = '';
      if(strImage2 == ''){
        secondImageBanner = 'secondImageBanner';
      }
      strBannerItem = `
            ${strImage1}
							<div class="carousel-caption">
							<div class="captionFrame">
								<ul>
									<li class="${secondImageBanner}">
										<div class="banner-img-wrap">
										    ${strImage2}
										</div>
									</li>
									<li class="liTitleText">
										<span class="TitleText">${strTitle}</span>
									</li>
									<li class="liDescText">
										 ${strDesc}
									</li>
									<li class="bannerLinks">
										 ${strLink1}
										 ${strLink2}
										
									</li>
								</ul>
							</div>
							</div>
      
      
      `;
      $('.heroBanner .container').append(strBannerItem);
      $('.heroBanner').removeClass('hidden');
    });
    $('.heroBanner .container .banner-img-wrap img').addClass('banner-img');

    if(isset($($('.heroBanner .container .bannerLinks a')[0]).html())){
      $($('.heroBanner .container .bannerLinks a')[0]).addClass('WhiteBorder');
    }
    if(isset($($('.heroBanner .container .bannerLinks a')[1]).html())){
      $($('.heroBanner .container .bannerLinks a')[1]).addClass('WhiteTextButton');
    }

  }

  strZoneBLCnt = '';
  var zoneBLeftObj = $('#zoneBObj').html();
  if(zoneBLeftObj != undefined && zoneBLeftObj.length > 0){
    $('#zoneBObj > ul').each(function(key,val){
      objLiCnt =  $(this);
      strImage1 = '';
      strImage2 = '';
      strTitle = '';
      strDesc = '';
      strLink1 = '';
      strLink2 = '';
      $(objLiCnt.find('li')).each(function(key1,val1){
        if($(val1).find('img').length > 0 && strImage1 == ''){
          strImage1 = $(val1).html();
        }
        if($(val1).find('a').length > 0){
          strLink1 = isset($(val1).find('a').attr('href'))?$(val1).find('a').attr('href'):'javascript:void(0);'; 
          strTitle = isset($(val1).find('a').html())?$(val1).find('a').html():'';
        }
       
        if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle != '' && strDesc == ''){
          strDesc = $(val1).html();
        }       

      });
      strZoneBLCnt += `
          <li>
            <a class="quick-link-box" href="${strLink1}">
              <span class="left-icon">
                  ${strImage1}
              </span>
              <h2 class="HeaderTextSmall bottom-line-0"> ${strTitle}</h2>
              <p> ${strDesc}</p>
            </a>
          </li>
      `;
    });

    if(strZoneBLCnt != ''){
      $('.zoneBLeftWrap > ul') .html(strZoneBLCnt);
    }
  }

  strZoneBRCnt = '';
  var zoneBRightObj = $('#zoneCObj').html();
  if(zoneBRightObj != undefined && zoneBRightObj.length > 0){
    $('#zoneCObj > ul').each(function(key,val){
      objLiCnt =  $(this);
      strTitle1 = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():''; 
      strDesc = isset(objLiCnt.children().eq(1).html())?objLiCnt.children().eq(1).html():''; 
      strLink1 = isset(objLiCnt.children().eq(2).html())?objLiCnt.children().eq(2).html():''; 
    });

    strZoneBRCnt = `
      <div class="quick-link-right-box">
          <h2 class="HeaderText bottom-line-0">${strTitle1}</h2>
          <p>${strDesc}</p>
          ${strLink1}
        </div>    
    `;

    $('.zoneBRighttWrap').html(strZoneBRCnt);
    $('.zoneBRighttWrap .quick-link-right-box > a').addClass('CAAAButton');
  }
  if(strZoneBRCnt == '' && strZoneBLCnt == ''){
    $('.zoneBWrap').addClass('hidden');
  }else if(strZoneBRCnt == '' && strZoneBLCnt != ''){
    $('.zoneBRighttWrap').remove();
    $('.zoneBLeftWrap').removeClass('span8');
    $('.zoneBLeftWrap').addClass('span12');
  } else if(strZoneBRCnt != '' && strZoneBLCnt == ''){
    $('.zoneBLeftWrap').remove();
    $('.zoneBRighttWrap').removeClass('span4');
    $('.zoneBRighttWrap').addClass('span12 zoneBRightOnly');
  }

  var zoneDObj = $('#zoneDObj').html();
  strEventList = '';
  if(zoneDObj != undefined && zoneDObj.length > 0){

      $('#zoneDObj .mcMergeTemplate > ul').each(function(key,val){
        objLiCnt =  $(this);
        
        evTitle = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():'';
        evLink = isset(objLiCnt.children().eq(1).html())?objLiCnt.children().eq(1).html():'javascript:void(0);';  
        evDate = isset(objLiCnt.children().eq(2).html())?objLiCnt.children().eq(2).html():''; 
        evWebinar = isset(objLiCnt.children().eq(3).html())?objLiCnt.children().eq(3).html():''; 
        evLocCnt = isset(objLiCnt.children().eq(4).html())?objLiCnt.children().eq(4).html():''; 
        evLoc = isset(objLiCnt.children().eq(5).html())?objLiCnt.children().eq(5).html():''; 

        strLocation = evLocCnt;
        if(evLocCnt == ''){
          strLocation = evLoc;
        }

        strLocation = '<i class="fa-solid fa-location-dot"></i> '+strLocation;

        if(evWebinar == 1 || evWebinar.toLowerCase() == 'yes'){
          strLocation = '<i class="fa-solid fa-video"></i> Live Webinar';
        }
        arrDate = evDate.split(' ');
        strMonth = '';
        strDay = '';
        if(arrDate.length == 3){
          strMonth = arrDate[0];
          strDay = arrDate[1];
        }

        strLocation = $("<textarea/>").html(strLocation).text();
        strLocation = strLocation.replace(/<\/?p>/g, '');
        strEventList += `<div class="date-card-col">
                      <a href="${evLink}">
                      <div class="date-card">
                        <div class="dc-date">${strMonth} <span> ${strDay}</span></div>
                        <div class="dc-content">
                          <p class="evTitleZoneD">${evTitle}</p>
                          <p class="InfoText">${strLocation}</p>
                        </div>
                      </div>
                      </a>
                    </div> `;

      });
      if(strEventList != ''){
        $('.zoneDList').html(strEventList);
      }
      $('#zoneDObj .mcMergeTemplate > ul').closest('li').remove();
      zoneDObj = $('#zoneDObj').find(' > ul > li');
      evLeftImage = isset(zoneDObj.eq(0).html())?zoneDObj.eq(0).html():'';
      evHeadTitle = isset(zoneDObj.eq(1).html())?zoneDObj.eq(1).html():''; 
      evMoreLinks = isset(zoneDObj.eq(2).html())?zoneDObj.eq(2).html():''; 
      $('.zoneDLeft').html(evLeftImage);
      $('.zoneDLeft > img').addClass('section-left-img');
      $('.zoneDTitle').html(evHeadTitle);
      $('.zoneDLinks').html(evMoreLinks);

      $('.left-icon-title.zoneDTitle').each(function() {
        let $this = $(this);
        
        let $img = $this.find('img').clone();
        let $imgSRC = $this.find('img').attr('src');
        let text = $this.clone().children('img').remove().end().text().trim();
        
        $this
            .empty()
            .append($img.attr('src', $imgSRC)) 
            .append(`<h2 class="HeaderText bottom-line-0">${text}</h2>`);
      });

      $('.zoneDLinks > a').eq(0).addClass('WhiteBorder');
      $('.zoneDLinks > a').eq(1).addClass('WhiteTextButton');

      $('.zoneDWrap').removeClass('hidden');
  }
  

  var zoneEObj = $('#zoneEObj').html();
  strBlogList = '';
  if(zoneEObj != undefined && zoneEObj.length > 0){

      $('#zoneEObj .mcMergeTemplate > ul').each(function(key,val){
        objLiCnt =  $(this);
        
        strLink = "javascript:void(0);";
        if(objLiCnt.children().eq(0).find('a').length > 0){
          strLink = isset(objLiCnt.children().eq(0).find('a').attr('href'))?objLiCnt.children().eq(0).find('a').attr('href'):'javascript:void(0);';
          strTitle = objLiCnt.children().eq(0).find('a').html();
        }else{
            strTitle = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():'';
        }
        strDate = isset(objLiCnt.children().eq(1).html())?objLiCnt.children().eq(1).html():'';  
        strDesc = isset(objLiCnt.children().eq(2).html())?objLiCnt.children().eq(2).html():''; 
        strHover = isset(objLiCnt.children().eq(3).html())?objLiCnt.children().eq(3).html():''; 

        strBlogList += `<div class="span3">
									<a href="${strLink}" class="border-card">
										<span class="date-box">${strDate}</span>
										<div class="news-img">
											${strHover}
										</div>
										<h3 class="ColumnHeader">${strTitle}</h3>
										<p>${strDesc}</p>
									</a>
								</div>`;

      });
      if(strBlogList != ''){
        $('.strBlogList').html(strBlogList);
      }
      $('#zoneEObj .mcMergeTemplate > ul').closest('li').remove();
      zoneEObj = $('#zoneEObj').find(' > ul > li');
      strLeftImage = isset(zoneEObj.eq(0).find('img'))?zoneEObj.eq(0).find('img').attr('src'):'';
      if(isset(zoneEObj.eq(0).find('img'))){
        zoneEObj.eq(0).find('img').remove();        
        $('.zoneELeft > img').attr('src',strLeftImage)
      }

      strHeadTitle = isset(zoneEObj.eq(1).html())?zoneEObj.eq(1).html():''; 
      strMoreLinks = isset(zoneEObj.eq(2).html())?zoneEObj.eq(2).html():''; 
      
      $('.zoneELeftTop h2').html(strHeadTitle);
      $('.zoneELeftTop').append(strMoreLinks);
      $('.zoneELeftTop > a').addClass('all-post-link');
      $('.zoneEWrap').removeClass('hidden');

      $('.strBlogList .news-img').each(function() {
        let $this = $(this);
        
        let $img = $this.find('img').clone();
        let $imgSRC = $this.find('img').attr('src');
        let text = $this.clone().children('img').remove().end().text().trim();

        if (typeof $imgSRC === 'string') {
            $imgSRC = $imgSRC.replace(/^\/(https?:)/, '$1');
        }
        
        $this
            .empty()
            .append($img.attr('src', $imgSRC)) 
            .append(`<span class="read-more-btn">${text}</span>`);
      });
  }

  if($('#zoneFObj').html() != undefined)
    var zoneFObj = $('#zoneFObj').html().trim();
    liFCount = 0;
    if(zoneFObj != undefined && zoneFObj.length > 0){
      $('#zoneFObj > ul').each(function(key,val){
        objLiCnt =  $(this);
        liFCount = liFCount + 1;
        strImage1 = '';
        strImage2 = '';
        strTitle = '';
        strTitleMain = '';
        strDesc = '';
        strLinks = '';

        $(objLiCnt.find('li')).each(function(key1,val1){
          if($(val1).find('img').length > 0 && key1 == 0){
            strImage1 = $(val1).html();
          }else if($(val1).find('img').length > 0 && strImage1 != ''){
            strImage2 = $(val1).html();
          }

          if($(val1).find('a').length > 0){
            strLinks = $(val1).find('a').closest('li').html();
          }
          
          if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle != '' && strTitleMain != '' && strDesc == ''){
            strDesc = $(val1).html();
          } 

          if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle == ''){
            strTitle = $(val1).html();
          }else if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle != '' && strTitleMain == ''){
            strTitleMain = $(val1).html();
          }  

        });
		
       
        $('.zoneFCnt').append(`
             <ul>
                <li>
                  <span class="member-icon">
                      ${strImage1}
                  </span>
                  <span class="ColumnHeader">
                      ${strTitle}
                  </span>
                </li>
                <li>
                   ${strImage2}
                </li>
                <li>
                  <h3 class="ColumnHeader"> ${strTitleMain}</h3>
                </li>
                <li>
                  <p>${strDesc}</p>
                </li>
                <li>
                    ${strLinks}
                </li>
            </ul>        
          
        `);
        
      });
        $('.zoneFCnt > ul').each(function(){
          $(this).find(' > li:last-child > a')
          if($(this).find(' > li:last-child > a').eq(0).length > 0){
            $(this).find(' > li:last-child > a').eq(0).addClass('WhiteBorder');
          }
          if($(this).find(' > li:last-child > a').eq(1).length > 0){
            $(this).find(' > li:last-child > a').eq(1).addClass('WhiteTextButton');
          }
        })
      
      if(liFCount > 0) {
          $(".zoneFCnt.member-card-slider").addClass("owl-carousel").owlCarousel({
            items: 1,
            margin: 0,
            loop: (liFCount > 1)?true:false,
            nav: (liFCount > 1)?true:false,
            dots: (liFCount > 1)?true:false,
            autoplay: true,
            autoplayTimeout: 7000,
            //autoplayHoverPause:true,
            navText: ['<i class="fa-solid fa-angle-left"></i>', '<i class="fa-solid fa-angle-right"></i>'],
            animateIn: 'fadeIn',
            animateOut: 'fadeOut',
            touchDrag: false,
            mouseDrag: false
          });
      }

  }

  if($('#zoneGObj').html() != undefined)
    var zoneGObj = $('#zoneGObj').html().trim();
    liGCount = 0;
    if(zoneGObj != undefined && zoneGObj.length > 0){
      $('#zoneGObj > ul').each(function(key,val){
        objLiCnt =  $(this);
        liGCount = liGCount + 1;
        strImage1 = '';
        strImage2 = '';
        strTitle = '';
        strTitleMain = '';
        strDesc = '';
        strLinks = '';

        $(objLiCnt.find('li')).each(function(key1,val1){
          if($(val1).find('img').length > 0 && key1 == 0){
            strImage1 = $(val1).html();
          }else if($(val1).find('img').length > 0 && strImage1 != ''){
            strImage2 = $(val1).html();
          }

          if($(val1).find('a').length > 0){
            strLinks = $(val1).find('a').closest('li').html();
          }
          
          if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle != '' && strTitleMain != '' && strDesc == ''){
            strDesc = $(val1).html();
          } 

          if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle == ''){
            strTitle = $(val1).html();
          }else if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle != '' && strTitleMain == ''){
            strTitleMain = $(val1).html();
          }  

        });
		
       
        $('.zoneGCnt').append(`
             <ul>
                <li>
                  <span class="member-icon">
                      ${strImage1}
                  </span>
                  <span class="ColumnHeader">
                      ${strTitle}
                  </span>
                </li>
                <li>
                   ${strImage2}
                </li>
                <li>
                  <h3 class="ColumnHeader"> ${strTitleMain}</h3>
                </li>
                <li>
                  <p>${strDesc}</p>
                </li>
                <li>
                    ${strLinks}
                </li>
            </ul>        
          
        `);
        
      });

      $('.zoneGCnt > ul').each(function(){
        $(this).find(' > li:last-child > a')
        if($(this).find(' > li:last-child > a').eq(0).length > 0){
          $(this).find(' > li:last-child > a').eq(0).addClass('WhiteBorder');
        }
         if($(this).find(' > li:last-child > a').eq(1).length > 0){
          $(this).find(' > li:last-child > a').eq(1).addClass('WhiteTextButton');
        }
      })

      if(liGCount > 0) {
          $(".zoneGCnt.member-card-slider").addClass("owl-carousel").owlCarousel({
            items: 1,
            margin: 0,
            loop: (liGCount > 1)?true:false,
            nav: (liGCount > 1)?true:false,
            dots: (liGCount > 1)?true:false,
            autoplay: true,
            autoplayTimeout: 7000,
            //autoplayHoverPause:true,
            navText: ['<i class="fa-solid fa-angle-left"></i>', '<i class="fa-solid fa-angle-right"></i>'],
            animateIn: 'fadeIn',
            animateOut: 'fadeOut',
            touchDrag: false,
            mouseDrag: false
          });
      }

  }

  if(liFCount != '' || liGCount != ''){
    $('.zoneFGWrap').removeClass('hidden');
  }

  if($('#zoneInnerImage').html() != undefined){
    var zoneInnerImage = $('#zoneInnerImage').html().trim();
    if(zoneInnerImage != undefined && zoneInnerImage.length > 0){
        strImage1 = '';
        strImage2 = '';
        strLinks = '';
        strDesc = '';
        $($(zoneInnerImage).find('li')).each(function(key1,val1){
          if($(val1).find('img').length > 0 && key1 == 0){
            strImage1 = $(val1).html();
          }else if($(val1).find('img').length > 0 && strImage1 != ''){
            strImage2 = $(val1).html();
          }
          if($(val1).find('a').length > 0){
            strLinks = $(val1).html();
          }          
          if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strDesc == ''){
            strDesc = $(val1).html();
          } 
        });
        $('.bannerInnerContainer').before(strImage1);
        $('.bannerRightWrap .banner-img-wrap').html(strImage2);
        $('.bannerDesc').html(strDesc);
        $('.bannerBtns').html(strLinks);
        if($('.bannerInnerWrap > img').eq(0) != undefined){
          $('.bannerInnerWrap > img').eq(0).addClass('fixed-bg');
        }
        $('.bannerInnerContainer .bannerBtns > a').eq(0).addClass('WhiteBorder');
        $('.bannerInnerContainer .bannerBtns > a').eq(1).addClass('WhiteTextButton');
        $('.bannerRightWrap img').addClass('banner-img');
        if(strImage2 == ''){
          $('.bannerRightWrap').addClass('hidden');
           $('.bannerInnerContainer .bannerBtns').addClass('noBannerSecond');
        }
    }
  }

  var zoneMObj = $('#zoneMObj').html();
	if(zoneMObj != undefined){
		$('#zoneMObj').find('> ul > li').each(function(key,val){
			objLiCnt =  $(this);     
			
			if(objLiCnt.find('ul').length > 0){
				strUL = isset(objLiCnt.find('ul').html())?objLiCnt.find('ul').html():'';
				objLiCnt.find('ul').remove();
				strLink = isset(objLiCnt.find('a').attr('href'))?objLiCnt.find('a').attr('href'):'javascript:void(0);';
				strText= isset($(objLiCnt).html())?$(objLiCnt).html().trim():'';

				strLi = `
					<a href="${strLink}" data-submenu="submenu-${key}" >
								${strText} 
                                 </a>
                                 <ul class="quicklinks-submenu" id="submenu-${key}">
								 	${strUL}
                                 </ul>
				`;
				
				$('.zoneMWrap .eventbox-info').append(strLi);
			}else{
				$('.zoneMWrap .eventbox-info').append($(objLiCnt).html());
			}



			
		});
		$('#zoneMObj').find('> ul').remove();
		strImg = isset($('#zoneMObj').find('img').attr('src'))?$('#zoneMObj').find('img').attr('src'):''; 
		if(strImg != ''){
			$('#zoneMObj').find('img').remove();
			$('.zoneMWrap .event-icon').html('<img src="'+strImg+'" alt="">');
		}
		strTitle = isset($('#zoneMObj h3').html())?$('#zoneMObj h3').html().trim():''; 
		
		$('.zoneMWrap .ColumnHeader').html(strTitle);
		
		
		if($('.zoneMWrap .eventbox-info > a').length > 0){
			$('.zoneMWrap .eventbox-info > a').addClass('sidebar-iconbox');
			$('.zoneMWrap').removeClass('hidden');

		}
	}	

  var zoneNObj = $('#zoneNObj').html();
  strEventCnt = '';
	if(zoneNObj != undefined){
		$('#zoneNObj').find('.mcMergeTemplate > ul').each(function(key,val){
			objLiCnt =  $(this); 
			strTitle = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html().trim():''; 
			strLink = isset(objLiCnt.children().eq(1).html())?objLiCnt.children().eq(1).html().trim():''; 
			strDate = isset(objLiCnt.children().eq(2).html())?objLiCnt.children().eq(2).html().trim():''; 

      arrDate = strDate.split('/');
      if(arrDate.length > 1){
          strDate = arrDate[0]+'/ <span>'+arrDate[1]+'</span>';
      }
      strEventCnt += `
        <div class="sidebar-event">
            <p class="sidebar-event-date">${strDate}</p>
            <p class="sidebar-event-title"><a href="${strLink}">${strTitle}</a></p>
          </div>
      `;   
		});

    if(strEventCnt != ''){
      $('.zoneNWrapCnt').html(strEventCnt); 
      $('#zoneNObj').find('.mcMergeTemplate').remove();
      strImg = isset($('#zoneNObj').find('img').attr('src'))?$('#zoneNObj').find('img').attr('src'):''; 
      if(strImg != ''){
        $('#zoneNObj').find('img').remove();
        $('.zoneNWrap .event-icon').html('<img src="'+strImg+'" alt="">');       
      }

      strTitle = isset($('#zoneNObj h3').html())?$('#zoneNObj h3').html().trim():''; 
      $('.zoneNWrap .ColumnHeader').html(strTitle);

      strBtnText = isset($('#zoneNObj a').html())?$('#zoneNObj a').html():''; 
      strBtnLink = isset($('#zoneNObj a').attr('href'))?$('#zoneNObj a').attr('href'):'javascript:void(0);'; 

      $('.zoneNWrap .eventbox-item-link').html(' <a href="'+strBtnLink+'" class="CAAAButton">'+strBtnText+'</a>');

			$('.zoneNWrap').removeClass('hidden');
    }
	}
   var zoneOObj = $('#zoneOObj').html();
  if(zoneOObj != undefined){
    strTitle = isset($('#zoneOObj').html())?$('#zoneOObj').html().trim():''; 
    $('.zoneOObjCnt').html(strTitle);
    $('.zoneOObjCnt').removeClass('hidden');
  }

  /*****************/
  $(".quicklink-mobile .event-list").hide();
  $(".quicklink-mobile h3").click(function () {
    $(".quicklink-mobile .event-list").slideToggle();
    $(this).toggleClass("quicklink-open");
  });

  $(".event-mobile .event-list").hide();
  $(".event-mobile h3").click(function () {
    $(this).closest(".event-mobile").find(".event-list").slideToggle();
    $(this).toggleClass("event-open");
  });



  $(".slider .owl-carousel").owlCarousel({
    items: 1,
    margin: 0,
    loop: true,
    autoplay: true,
    autoplayTimeout: 7000,
    autoplayHoverPause: true,
    animateIn: 'fadeIn',
    animateOut: 'fadeOut',
    touchDrag: false,
    mouseDrag: false
  });

  $(".friendsSliderBox ul").addClass('owl-carousel owl-theme').owlCarousel({
    items: 1,
    margin: 30,
    loop: true,
    nav: true,
    dots: false,
    autoplay: true,
    autoplayTimeout: 7000,
    animateIn: 'fadeIn',
    animateOut: 'fadeOut',
    touchDrag: false,
    mouseDrag: false,
    responsive:{
        0:{
            items:1,
            nav:true
        },
        767:{
            items:4,
            nav:false
        },
        1000:{
            items:4,
            nav:true,
            loop:false
        }
    }
  });

  $(".friendsSliderBox.friendsSliderBox-mobile .owl-carousel").owlCarousel({
    items: 1,
    margin: 0,
    loop: true,
    nav: true,
    dots: false,
    autoplay: true,
    autoplayTimeout: 7000,
    //autoplayHoverPause:true,
    animateIn: 'fadeIn',
    animateOut: 'fadeOut',
    touchDrag: false,
    mouseDrag: false
  });

  if($(".member-card-slider").length > 0) {
      $(".member-card-slider").addClass("owl-carousel").owlCarousel({
        items: 1,
        margin: 0,
        loop: true,
        nav: true,
        dots: true,
        autoplay: true,
        autoplayTimeout: 7000,
        //autoplayHoverPause:true,
        navText: ['<i class="fa-solid fa-angle-left"></i>', '<i class="fa-solid fa-angle-right"></i>'],
        animateIn: 'fadeIn',
        animateOut: 'fadeOut',
        touchDrag: false,
        mouseDrag: false
      });
  };



  $(document).on('click', '.searchBtnFn  .dropdown-toggle',  function(e) {
    e.preventDefault();
    $(this).closest('li').addClass('show-search-bar');
  });

  $(document).on('click', '.close-menu',  function(e) {
    e.preventDefault();
    $('.btn-navbar').trigger('click');
  });
  
  $(document).on('click', '.searchclose',  function(e) {
    e.preventDefault();
    $(this).closest('li.show-search-bar').removeClass('show-search-bar');
  });

  $(document).on('click', '.toggle-form, .close-form',  function(e) {
    if(!$(this).parent().hasClass('headerMyCaaa')){
      e.preventDefault();
      $(this).closest('li').toggleClass('show-form');
    }
    
  });

  $('#myTab a').click(function (e) {
    e.preventDefault();
    $(this).tab('show');
    $(".friendsSliderBox .owl-carousel").trigger('refresh.owl.carousel');
 })

 $(document).on('click', '.eventbox-col .event-head', function()  {
  $(this).closest('.eventbox-col').toggleClass('open');
 })


	$(document).on('click', '[data-submenu]', function(e) {
		e.preventDefault();
		$('#'+ $(this).attr('data-submenu')).slideToggle();
	})

});

function isset(el){	
  if(el !=  undefined && el != null && el != ''){		
    return true;
  }else{
    return false;
  }	
}
function imgError(_this){	
  $(_this).attr('src','/images/default-event.png');
}
function decodeHtml(str)
{
    var map =
    {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#039;': "'"
    };
    return str.replace(/&amp;|&lt;|&gt;|&quot;|&#039;/g, function(m) {return map[m];});
}

function setUpMenu(){
  $('.nav-collapse ul > li:has(ul)').addClass('dropdown');
  $('.nav-collapse ul > li.dropdown > a').addClass('dropdown-toggle');
  $('.nav-collapse ul > li.dropdown > ul').addClass('dropdown-menu row-fluid');
  $('.nav-collapse ul > li.dropdown > ul.dropdown-menu >  li').addClass('megaMenuSection');
  $('.nav-collapse ul > li.dropdown > ul.dropdown-menu >  li.megaMenuSection > ul').addClass('mainMenu');
  $('.nav-collapse ul > li.dropdown > ul.dropdown-menu >  li.megaMenuSection > ul.mainMenu > li > ul').addClass('subMenu');
  // $('.nav-collapse ul > li.dropdown > ul.dropdown-menu >  li.megaMenuSection:has(form)').addClass('formDiv clearfix');
  // $('.nav-collapse ul > li.dropdown > ul.dropdown-menu >  li.megaMenuSection.formDiv > div ').addClass('formframe clearfix');
  $('.mainMenu > li:has(ul)').addClass('subMenuParent');

  $('.hbf-nav-wrap .nav > li:has(ul)').addClass('dropdown');
  $('.hbf-nav-wrap .nav > li.dropdown > a').addClass('dropdown-toggle');
  $('.hbf-nav-wrap .nav > li.dropdown > ul').addClass('dropdown-menu row-fluid');
  $('.hbf-nav-wrap .nav > li.dropdown > ul.dropdown-menu >  li').addClass('megaMenuSection');
  $('.hbf-nav-wrap .nav > li.dropdown > ul.dropdown-menu >  li.megaMenuSection > ul').addClass('mainMenu');
  $('.hbf-nav-wrap .nav > li.dropdown > ul.dropdown-menu >  li.megaMenuSection > ul.mainMenu > li > ul').addClass('subMenu');
  


  if ($(window).width() < 980) {
    $(".btn-navbar").click(function () {
      $("body").toggleClass("overlay");
    });

    $(".header .dropdown").append("<span class='menu-arrow'></span>");
    $(".anchore-list").append("<li class='openList'></li>");
    
    $(".menu-arrow").click(function () {
      if($(this).closest('.dropdown').hasClass('open-droupdown')) {
        $(this).closest(".dropdown").removeClass('open-droupdown');
        $(this).closest(".dropdown").children(".dropdown-menu").slideUp();
      } else {
        $(this).closest(".dropdown").addClass('open-droupdown');
        $(this).closest(".dropdown").children(".dropdown-menu").slideDown();
      }
    });
    
    $(".caaa-tabs span").click(function () {
      $(this).closest(".caaa-tabs").addClass('open-droupdown');
    });

    $(document).on('click', '.caaa-tabs.open-droupdown>.btn', function (e) {
      e.preventDefault();
      $(this).closest(".caaa-tabs").removeClass('open-droupdown');
    });


   
    $(document).on('click', '.mainMenuMob-list .SubHeading', function (e) {
      e.preventDefault();
      if($(this).closest(".mainMenuMob-list").hasClass('open-droupdown')) {
        $(this).closest(".mainMenuMob-list").removeClass('open-droupdown');
        
      } else {
        $(this).closest(".mainMenuMob-list").addClass('open-droupdown');
      }
    });
    
    $(document).on('click', '.anchore-list .openList', function (e) {
      e.preventDefault();
      if($(this).closest(".anchore-list").hasClass('open-droupdown')) {
        $(this).closest(".anchore-list").removeClass('open-droupdown');
        
      } else {
        $(this).closest(".anchore-list").addClass('open-droupdown');
      }
    });

    $(document).on('click', '.anchore-list.open-droupdown>li>a', function (e) {
      e.preventDefault();
      $(this).closest(".anchore-list").removeClass('open-droupdown');
    });
    


    $(".btn-navbar").click(function () {
      $(".dropdown").removeClass('open-droupdown');
      $(".dropdown-menu").hide();
    });


    $(".mainMenuMobBtn").click(function () {
      $(this).toggleClass('textUnderline');
      $(this).parents(".megaMenuSection").toggleClass('closeBox');
      $(this).parents(".megaMenuSection").children(".mainMenuMob").slideToggle();
      $(this).parents(".megaMenuSection").children(".mainMenuOnclickBtn").slideToggle();
      $(this).parents(".megaMenuSection").children(".mainMenuOnclickBtn").toggleClass('openBoxInner');
    });


    $(".mainMenuOnclickBtn").click(function () {
      $(this).parents(".megaMenuSection").children(".mainMenuOnclick").slideToggle();
    });

  }

  if ($(window).width() > 979) {
    $(".mainMenuOnclickBtn").click(function () {
      $(this).parents(".megaMenuSection").children(".mainMenuOnclick").slideToggle();
    });
  }
  $('.rightMenus').removeClass('hidden');
  $('.menuNavWrap').removeClass('hidden');
}
function initiateHeaderSerach(id){
    searchText = $('#'+id+' #s_key_all').val();
    if(searchText != ''){
       $('#'+id).submit();
    }else{
      $('#'+id+' #s_key_all').addClass('noSearchText');
      setTimeout(function(){
         $('#'+id+' #s_key_all').removeClass('noSearchText');
      },2000)
      return false;
    }
}