@charset "utf-8";
@media print {
	* { -webkit-print-color-adjust: exact; -moz-print-color-adjust: exact; -o-print-color-adjust: exact; visibility: visible; }
	video, audio, object, embed { display: none; }
	img { max-width: 500px; }
	body { margin: 1cm auto; }
	.printHeader { text-align: center; padding: 15px; display: block; width: 100%; }
	.printHeader a { display: inline-block; width: 100%; text-align: center; margin-top: 10px; }
	.printHeader p { margin-top: 15px; }
	.headerSpace { display: none; }
	.header, .navbar, .footer, .bgBorder { display: none; }
	.copyrightText { display: block; text-align: center; }
	.copyrightText ul li { color: #000; }
	.printFooter { width: 100%; text-align: center; display: block; font-size: 13px; color: #fff; font-weight: 400; }
	.container { width: 100% !important; }
	.span4 { width: 33%; }
	.captionFrame ul li, .captionFrame ul li { color: white; }
	.captionFrame {
		margin-left: 0;
		max-width: 100%;
		padding: 0 15px;
	}
	.owl-carousel .owl-stage {
		display: block !important;
		transform: none !important;
		width: 100% !important;
		padding: 15px !important;
	}
	.slider .owl-carousel .item:before,.slider .owl-carousel .item img {
		display: none;
	}
	.captionFrame ul li {
		word-break: break-all;
	}
	.captionBtnBox { position: static; max-width: 100%; }
	.captionBtnFrame { position: static; background-color: white; max-width: 100%; }
	.captionBtnBox ul li a {
		background: transparent;
		border: 1px solid #BA0C2F; display: block; height: 130px;
	}
	.captionBtnBox ul li a .iconBox img.default, .arrow img {
		filter: invert(1);
	}
	.captionBtnBox ul li a .textBox h2 {
		color: #2d2d2d;
	}
	
	.inner-page-content {
		position: relative;
	}
	.inner-page-content .sidebar {
		width: 280px;    
		border-right: 1px solid #717171;
	}
	.inner-page-content .inner-content-area {
		padding: 20px;
	}
	.sponsors-box, .sponsors-boxthree {
		border: 1px solid #717171;
	}
	.event-slider .owl-stage {
		width: 100% !important;
	}
	.event-slider .owl-stage .owl-item {
		width: 100% !important;
		margin: 0 0 35px 0 !important;
	}
	.eventBoxFrame { margin: 0; }
	.owl-carousel .owl-item.cloned {
		display: none !important;
	}
	.owl-carousel .owl-stage {
		display: block;
		width: 100%;
		transform: none;
		height: auto;
		max-width: 100%;
	}
	
	.owl-carousel .owl-stage .owl-item {
		width: 100% !important;
	}
	.for-mobile .events {
		display: none;
	}
	
	.anouncebanner, .friendsLogoBox.for-mobile {
		display: none;
	}
	.captionBtnBox ul li a .iconBox img {
		filter: none;
		margin-top: 10px;
	}
	.upcoming-event-sec .flex-row>div {
		width: 33%;
	}
	.inner-page-content>.row-fluid, .inner-page-content>.row-fluid>div {
		display: block;
		max-width: 100%;
		width: 100%;
	}
	.for-mobile, .inner-content-area .sponsors-sec:before {
		display: none;
	}
	.inner-page-content .inner-content-area, .inner-page-content .leftInner.widget {
		flex: 0 0 100%;
		max-width: 100%;
	}	
}

 @media screen and (min-width:1300px) {
	.quick-links-sec .container {max-width: 1230px;width: 100%;}
	.container {
		max-width: 1230px;
		width: 100%;
	}
	
}
 @media screen and (min-width:1439px) {


 .important-dates-sec .container {
	    width: 100%;
	    max-width: 1440px;
	    padding-right: 90px;
	}

}

@media screen and (min-width: 980px) {
.nav-collapse.collapse { margin: 0 -15px 0; }
.header .nav-collapse .nav .dropdown .dropdown-menu {
	position: absolute;
	width: 100%;
	max-width: 100%;
	visibility: visible;
	background: #ffffff;
	opacity: 0;
	top: 87px;
	padding: 40px 25px;
	border: none;
	visibility: hidden;
	opacity: 0;
	-moz-transition: top 0.5s ease 0s, visibility 0s ease 0s;
	-ms-transition: top 0.5s ease 0s, visibility 0s ease 0s;
	-o-transition: top 0.5s ease 0s, visibility 0s ease 0s;
	-webkit-transition: top 0.5s ease 0s, visibility 0s ease 0s;
	transition: top 0.5s ease 0s, visibility 0s ease 0s, z-index 0s ease 0.1s;
	z-index: 9;
	/* border-top: 1px solid #cacbcd; */
	}
.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection {
	display: table;
	width: 100%;
	max-width: 100%;
	margin: 0 auto;
}
.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.textUl ul {
	padding-left: 0;
	display: block;
	vertical-align: middle;
	position: relative;
	list-style: none;
	margin: 0;
}
.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta ul li:first-child { float: left; margin-right: 45px; }
.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta ul li p { margin-bottom: 15px; font-size: 16px; }
.header .nav-collapse .nav li:nth-last-child(2).dropdown .dropdown-menu {}
.header .navbar .nav>li.dropdown:hover>.dropdown-menu {display: block !important;visibility: visible !important;z-index: 9;opacity: 1 !important;list-style: none;}
.header .navbar .nav li.dropdown>.dropdown-toggle .caret { border-top-color: #eeeeee; border-bottom-color: #eeeeee; }
.header .navbar .nav li.dropdown:hover>.dropdown-toggle .caret { border-top-color: #006eb3; border-bottom-color: #006eb3; }
.header .header .nav li .dropdown-menu>li.dropdown-submenu a:hover .caret { border-top: 4px solid #000; }
.header .navbar .nav li.dropdown .dropdown-menu .dropdown-submenu .dropdown-menu:before { display: none; }
.header .dropdown-submenu li { padding: 0 20px; }
.header .dropdown-submenu .dropdown-menu { padding: 20px 0; }
.header .dropdown-submenu .dropdown-menu { background: #44687d; }
.header .dropdown-submenu>.dropdown-menu { display: block !important; margin-left: -1px; left: 70%; opacity: 0; visibility: hidden; border-radius: 0; overflow: hidden; }
.header .dropdown-submenu:hover>.dropdown-menu { display: block !important; left: 100%; visibility: visible; -moz-transition: all 0.3s ease 0s; -ms-transition: all 0.3s ease 0s; -o-transition: all 0.3s ease 0s; -webkit-transition: all 0.3s ease 0s; transition: all 0.3s ease 0s; opacity: 1; }
.header .dropdown:hover>.dropdown-menu:not(.searchLgUl) { display: block; }
.blockquote-row>div:first-child{
	padding-right: 25px;
}
.blockquote-row>div:last-child{
	padding-left: 25px;
}
body .header .navbar .nav>li.searchBtnFn:hover .dropdown-menu, body .header .navbar .nav>li.headerlogin:not(.show-form):hover .dropdown-menu {
    display: none !important;
}

.footer-mobile-menus {
	display: none;
}
.top-action-wrapper {
	display: none;	
} 
.inner-page-content .inner-content-area .mobile-adv {
	display: none;
}
}
/* min980px */
 @media screen and (max-width:1800px) {
.navbar .container {/* padding: 0; */}
header .top-strip {
    padding-right: 310px;
}
}
 @media screen and (max-width:1600px) {
.navbar .container {/* padding: 0; */}
.dropdown-menu {}
.header .navbar .nav li.dropdown .megaMenuSection .searchHeading { max-width: 280px; }
.sliderFrame .item ul li { padding-right: 60px; }
.footer .row-fluid { padding: 0; }
.sliderFrame { padding: 0; }
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection:last-child { padding-right: 0px; }
blockquote, blockquote.pull-right {
}
}
 @media screen and (max-width:1500px) {
body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
    width: calc(100% - 449px);
    right: 150px;
}
.captionFrame {
	max-width: 45%;
}
header .navbar .nav li.headerlogin {
	width: 150px;
}
	 .social-list {
		 right: 180px;
	 }
.header .navbar .nav li {}
header .top-strip {
    padding-right: 280px;
}

.header .nav-collapse .nav .dropdown.headerlogin>ul.memberSection {
    padding: 25px 40px 30px 415px;
}
.header .navbar .nav li .memberSection a.toggle-form {
	font-size: 14px;
	right: 25px;
}
.header .navbar-brand {
    width: 240px;
    padding: 18px 0px 12px;
}

.header .nav-collapse.collapse {
    width: calc(100% - 240px);
}
}



@media screen and (max-width:1399px) {
.captionFrame h1 { font-size: 36px; }
.header .navbar .nav li.dropdown .megaMenuSection .searchHeading { max-width: 250px; }
.header .navbar .nav>li.searchBtnFn a img {height: auto;}
.header .navbar .nav li.dropdown .memberSection li form a.WhiteBorder { min-width: 110px; }
header .top-strip {
    padding-right: 250px;
}
.header .nav-collapse .nav {
	padding-left: 2%;
}
.header .navbar-brand {
    /* width: 230px; */
    /* padding: 25px 15px 15px 15px; */
}
	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
    width: calc(100% - 379px);
}

.header .nav-collapse.collapse {
    width: calc(100% - 240px);
}
.header .navbar-inner .headerlogin .dropdown-menu {
	padding: 30px 40px;
}
.not-a-member .CAAAButton {
	padding-left: 5px;
	padding-right: 5px;
	width: 100%;
	max-width: 150px;
} 
.header .navbar .nav>li.dropdown>a {
	    padding-right: 20px;
}
	.header .navbar .nav>li.dropdown>a:after {
		
	}
.header .navbar .nav>li.dropdown>a:after {
	right: 15px;
}
    .navbar .container {
        padding-left: 15px;
        padding-right: 15px;
    }
	.member-card .owl-dots {
    top: 447px;
}

.member-card .owl-carousel .owl-nav button.owl-prev, .member-card .owl-carousel .owl-nav button.owl-prev:hover, .member-card .owl-carousel .owl-nav button.owl-next, .member-card .owl-carousel .owl-nav button.owl-next:hover {
    top: 468px;
}
.captionFrame ul li:nth-child(1) .banner-img-wrap {
    width: 680px;
    height: 680px;
}

.captionFrame ul li:nth-child(1):before {
    width: 700px;
    height: 700px;
    left: -29px;
}
.heroBanner {
    min-height: 430px;
}
.rightMenus>ul {
	gap: 15px;
}
.searchBtnFn.dropdown>ul.dropdown-menu {
    width: calc(100% - 500px);
    right: 220px;
}
}


@media screen and (max-width:1330px) {
.header .navbar .nav li>a {font-size: 13px;}
.header .navbar .nav > li.searchBtnFn > a {margin-right: 5px;}
.header .nav-collapse .nav .dropdown .dropdown-menu {padding: 20px 25px;}
.header .navbar .nav li.dropdown .megaMenuSection .heading { left:0; }
.header .navbar .nav li.dropdown .megaMenuSection .HeaderText {
	width: 360px;
}
.searchnav-logo {
    max-width: 200px;
    padding: 35px 0 35px 20px;
    height: 105px;
}
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection {
    /* width: 200px; */
}
.nav-member-center {
    width: 180px;
    padding: 10px;
}
.nav-member-center p {
    font-size: 14px;
}
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.member-center-wrap:last-child {
    max-width: 180px;
}
.TitleText {
	font-size: 36px;
}
	.banner-img-wrap {
    width: 600px;
    height: 600px;
}

.circle-wrap:before {
    width: 600px;
    height: 600px;
    left: -18px;
    top: -29px;
    transform: scale(1.025);
}
.slider .owl-carousel .item {
	padding: 30px 0;
}
body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
	height: 115px;
}
.header .navbar-brand {
	width: 180px;
	padding: 30px 15px 15px 0px;
}
	.header .navbar .nav li > a {
    /* padding-left: 6px; */
    /* padding-right: 6px; */
}

header .navbar .nav li.headerlogin {
    width: 120px;
}
	.social-list {
		right: 144px;
	}

.headerlogin .nav-member-center p {
    /* font-size: 12px; */
}

.nav-member-center img {
    width: 25px;
}
.header .nav-collapse.collapse {
	width: calc(100% - 300px);
}
.header .navbar .nav>li {
	padding-top: 0;
}
.header .nav-collapse.collapse {
	width: calc(100% - 180px);
}
body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
	width: calc(100% - 298px);
	right: 119px;
}
.flex-sec .flex-row {
    margin: 0 -15px;
}

.flex-sec .flex-row>.col {
    padding: 0 15px;
}
.searchBtnFn.dropdown>ul.dropdown-menu {
        width: calc(100% - 415px);
        right: 220px;
    }
}
@media screen and (min-width: 1299px) {
	.member-sec .span6 {width: 600px;}
}

@media screen and (max-width: 1199px) {
	.noBannerSecond{
		margin-bottom: 20px !important;
	}

	header .top-strip {
		padding-right: 215px;
	}
	.inner-page-content .sidebar {
		flex: 0 0 400px;
    	width: 400px;
	}
	.inner-page-content .inner-content-area {
		flex: 0 0 calc(100% - 300px);
		max-width: calc(100% - 300px);
		padding: 50px 100px 0 40px;
	}
	.TitleText {font-size: 38px;}
	blockquote, blockquote.pull-right {
		font-size: 20px;
	}
	blockquote:before, blockquote:after {
		width: 40px;
		height: 40px;
	}
	blockquote:before {
		left: -14px;
	}
	blockquote:after {
		right: -14px;
	}
	blockquote.pull-right:after {
		left: -14px;
	}
	blockquote.pull-right:before {
		right: -14px;
	}
	
	.captionBtnBox ul li a .iconBox img {
		width: 40px;
		height: 40px;
	}
	.captionBtnBox ul li a .iconBox {
		width: 50px;
	}
	
	.captionFrame ul li:nth-child(3) {
	/* font-size: 38px; */
	}
	
	.captionFrame ul li:nth-child(2) {
		font-size: 40px;
	}
	.info-iconbox img {
		width: 50px;
		height: 50px;
		padding: 0;
	}
	.info-iconbox h2 {
		font-size: 30px;
		min-height: 40px;
	}
	.newscard .news-inner-wrap {
		padding-left: 120px;
	}
	.newscard .news-inner-wrap img {
		left: 30px;
		width: 70px;
		height: 70px;
		object-fit: contain;
	}
	.info-iconbox .iconlink {
		width: 50px;
		height: 50px;
		font-size: 30px;
	}
	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
		height: 100px;
	}
.captionFrame {max-width: 50%;padding: 60px 0 0;}
.captionFrame h3 { font-size: 20px; }
.captionFrame h1 { font-size: 36px; margin-bottom: 20px; }
.captionBtnFrame {max-width: 351px;padding: 25px 15px;}
.captionBtnBox ul li a {padding: 10px 45px 10px 15px;min-height: 70px;}
.captionBtnBox ul li a .textBox {left: 80px;max-width: 180px;}
.captionBtnBox ul li a .textBox h2 {font-size: 18px;}

.HeaderText {font-size: 28px;}
.BodyTextLarge { font-size: 14px; }

.header .nav-collapse .nav {width: 100%;}
.header .navbar .nav li > a {
	font-size: 12px;
	padding: 28px 10px;
}
.header .navbar .nav li .megaMenuSection a {height: auto;}
.header .navbar .nav>li:last-child>a {width: 100%;}
	.header .navbar .nav>li.dropdown>a:after {
		top: 28px;
	}
.header .navbar .nav li.dropdown .memberSection li, .header .navbar .nav li.dropdown .memberSection li p, .header .navbar .nav li.dropdown .memberSection li>a { font-size: 14px; }
.header .navbar .nav li.dropdown .memberSection li label { font-weight: 300; font-size: 14px; letter-spacing: 0.2px; }
.header .navbar .nav li.dropdown .memberSection li input, .header .navbar .nav li.dropdown .memberSection li form a.btn, .header .navbar .nav li.dropdown .megaMenuSection .heading .btn { height: 40px; line-height: 36px; }
.header .navbar .nav li.dropdown .memberSection li form a { width: 100%; }
.header .navbar .nav li.dropdown .megaMenuSection .HeaderText { font-size: 24px; }
.header .navbar .nav li.dropdown .megaMenuSection .heading .TitleText { font-size: 24px; }
.header .navbar .nav>li.dropdown:last-child:hover:hover>a::after, .header .navbar .nav>li.dropdown:last-child:hover:focus>a::after, .header .navbar .nav>li.dropdown:last-child:hover:visited>a::after {/* border-top: 10px solid #017977; */}

.header .navbar .nav li.dropdown .megaMenuSection .searchHeading { max-width: 180px; }
.header .navbar .nav>li.dropdown:hover>a::after, .header .navbar .nav>li.dropdown:focus>a::after, .header .navbar .nav>li.dropdown:visited>a::after {
	/* border-left: 15px solid transparent; */
	/* border-right: 15px solid transparent; */
	/* border-top: 10px solid #f1f1ef; */
	/* top: 100%; */
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe {padding: 0;}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input {height: 40px;padding: 0 15px 0 40px;background-position: left 15px center;}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a { height: 40px; line-height: 36px; padding: 0 25px;}
.header .navbar .nav li.dropdown .memberSection li form a:last-child {margin-top: 10px;color: #ffffff;}
.container { width: 980px; }
.header {min-height: 80px;}
.headerSpace {height: 79px;}
.header .navbar-brand {
	max-width: 160px;
	padding: 23px 15px 15px 0px;
	/* height: 100px; */
}
body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
    width: calc(100% - 298px);
    right: 119px;
}
.header .navbar .nav li:nth-last-child(2) a img { margin-bottom: 2px; }
.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta ul li:first-child { margin-left: 15px; }
.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta ul li:first-child img { max-width: 200px }
.header .nav-collapse .nav .dropdown .dropdown-menu {/* top: 100px; */}
.header .navbar .nav li.dropdown .megaMenuSection .heading {
	left: inherit;
}
ul.follow-us li:first-child {
	margin-right: 10px;
}
/****************/
.inner-page-content .sidebar { width: 350px; }
.inner-page-content .inner-content-area {padding: 0 15px 0 30px;}
.Highlight p {
	font-size: 18px;
}
header .navbar .nav li.headerlogin {
    /* width: 150px; */
    padding: 0 10px;
    min-height: 100px;
}
.header-drop-title {
    width: header-top;
    padding-right: 20px;
}
.mainMenuMob {
    width: 100%;
}
.footer .row.d-flex-wrap>div.col1 {
	padding-right: 15px;
	-webkit-flex: 0 0 30%;
	flex: 0 0 30%;
	max-width: 30%;
	margin-right: 0;
	margin-bottom: 40px;
}
.footer .row.d-flex-wrap>div.col2 {
	margin-left: 1%;
}

.footer .row.d-flex-wrap>div.col2 {
	-webkit-flex: 1 1 70%;
	flex: 1 1 70%;
	margin: 0;
}
.footer-links ul.social-list li {
    margin-right: 15px;
}
img.footlogo {
    max-width: 100%;
}
ul.social-list li {
	margin-right: 10px;
}
.headerlogin .nav-member-center p {
}
.header .navbar .nav li a.nav-member-center {
    height: 100px;
}
.header .nav-collapse .nav {padding: 0;}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxone {
    width: 300px;
}
	.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.model-bg {
    position: absolute;
    width: calc(100% - 300px);
    height: 100%;
    top: 0;
    right: 0;
    z-index: -1;
}
.header .nav-collapse .nav .dropdown.headerlogin>ul.memberSection {
    padding-left: 330px;
}

.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxtwo {
    width: 60%;
}

.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection:last-child {
    /* width: 40%; */
}
.sponsors-link ul a {
	min-width: auto;
}
.flex-sec .flex-row .col-4 {
    flex: 0 0 50%;
    max-width: 50%;
}
.flex-sec .flex-row {
    flex-wrap: wrap;
    justify-content: center;
}
.flex-sec .flex-row .col-4:last-child {
    margin-top: 30px;
}
.footer .row.d-flex-wrap>div.col3 {
    -webkit-flex: 0 0 auto;
    flex: 0 0 auto;
    max-width: 150px;
    padding-top: 50px;
}
.footer-title {
	font-size: 24px;
}
.sec-pd70 {
	padding-top: 50px;
	padding-bottom: 50px;
}
.SectionHeader {
	/* font-size: 40px; */
}
.ColumnHeader, .event-head h4, .HeaderTextSmall {
    font-size: 18px;
}
.eventbox-item ul li {
    font-size: 12px;
}

.eventbox-item ul li {padding: 0 8px;}

.eventbox-item p {
    font-size: 16px;
    padding: 0;
}
.header .navbar .nav li.dropdown .megaMenuSection h2 {
    font-size: 26px;
}
.droptitle {
    font-size: 16px;
}
.hbf-nav-sec h2 {
	font-size: 40px;
}
.mainMenuMob .mainMenuMob-col {
	padding: 0 15px;
}
.tab-wrap .content-wrap {
    padding-left: 40px;
}
.left-img-card .li-img {
    flex: 0 0 250px;
	max-width: 250px;
}

.left-img-card .li-content {
    flex: 0 0 calc(100% - 250px);
    max-width: calc(100% - 250px);
}
.searchBtnFn.dropdown>ul.dropdown-menu {
    width: calc(100% - 390px);
    right: 221px;
    top: 6px;
}
    .header .nav-collapse.collapse {
        width: calc(100% - 160px);
    }
	
	.date-card-wrap .date-card-col {
		flex: 0 0 50%;
		max-width: 50%;
		margin-bottom: 30px;
	}
	.quick-link-box {
    padding: 0px 0px 0px 70px;
}
.quick-links-wrapper li {
	padding: 20px 30px;
}
.quick-link-box span.left-icon {
    width: 50px;
    height: 50px;
    top: 0px;
    left: 0px;
}
.member-card ul li:nth-child(3) {
    padding-top: 30px;
}

.member-card ul li:nth-child(3), .member-card ul li:nth-child(4), .member-card ul li:nth-child(5) {
    padding-left: 40px;
    padding-right: 40px;
}

.member-card ul li:last-child {
    padding-bottom: 20px;
}
.footer .footer-links {
	padding: 0 10px;
}
.footer-links h3 {
	font-size: 20px;
}
.news-sec .row.d-flex-wrap .span3 {
    padding-left: 15px;
    padding-right: 15px;
    flex: 0 0 50%;
    max-width: 50%;
}
.news-sec .row.d-flex-wrap .span3 {
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 30px;
}
.member-card ul li:first-child .member-icon {
    flex: 0 0 65px;
    max-width: 65px;
    height: 65px;
}
.member-card .owl-dots {
    top: 269px;
    height: 50px;
}
.member-card .owl-carousel .owl-nav button.owl-prev, .member-card .owl-carousel .owl-nav button.owl-prev:hover,  .member-card .owl-carousel .owl-nav button.owl-next, .member-card .owl-carousel .owl-nav button.owl-next:hover {
    top: 280px;
}

.member-card .owl-dots {
    top: 269px;
    height: 50px;
}
.member-card .owl-carousel .owl-nav button.owl-prev, .member-card .owl-carousel .owl-nav button.owl-prev:hover, .member-card .owl-carousel .owl-nav button.owl-next, .member-card .owl-carousel .owl-nav button.owl-next:hover {
    top: 280px;
}
.member-card ul li:nth-child(3), .member-card ul li:nth-child(4), .member-card ul li:nth-child(5) {
    padding-left: 20px;
    padding-right: 20px;
}
.member-card ul li:nth-child(3) {
    padding-top: 20px;
}
.member-card ul li:last-child {
	flex-wrap: wrap;
}

.member-card .owl-dots {
    top: 358px;
}
.member-card .owl-carousel .owl-nav button.owl-prev, .member-card .owl-carousel .owl-nav button.owl-prev:hover,  .member-card .owl-carousel .owl-nav button.owl-next, .member-card .owl-carousel .owl-nav button.owl-next:hover {
    top: 367px;
}

.captionFrame ul li:nth-child(1) .banner-img-wrap {
    width: 600px;
    height: 600px;
    left: -15px;
}

.captionFrame ul li:nth-child(1):before {
    width: 620px;
    height: 620px;
    left: -29px;
}

.captionFrame ul li:nth-child(1) {
    top: 35%;
    left: 52%;
}
.bannerInner .span6 {
	flex: 0 0 100%;
	max-width: 100%;
	padding: 0 15px;
}
.img-row .img-col {
    flex: 0 0 100%;
    max-width: 100%;
	border-style: none;
	margin-bottom: 30px;
}
.img-row .img-col:first-child {
	border-style: none;
}

.circle-wrap {
    margin: 30px 0 0 ;
    height: 310px;
}

.circle-wrap .banner-img-wrap {
    position: relative;
    top: 10px;
    left: 15px;
}

.circle-wrap:before {
    top: 8px;
    left: 12px;
}

.bannerInner.sec-pd70 {
    padding-bottom: 0;
}
.event-head {
    height: 70px;
}

.eventbox-img {height: 70px;}
.event-head .event-icon {
    flex: 0 0 70px;
    max-width: 70px;
    height: 70px;
}
.top-action-wrapper .centered-btn-wrap .CAAAButton,
.headerlogin .nav-member-center {
    padding: 8px 20px;
    border: 1px solid #ffffff;
}
}
/* 1199px */
@media(min-width: 980px) and (max-width: 1050px) {
	.header .navbar .nav>li.dropdown:hover>a::after, .header .navbar .nav>li.dropdown:focus>a::after, .header .navbar .nav>li.dropdown:visited>a::after {
		/* top: 62px; */
	}
	body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
		width: calc(100% - 268px);
	}
	.header .navbar-brand {
		padding: 20px 0px 10px 0px;
		height: 70px;
		max-width: 150px;
	}
	.hbf-design .header .navbar-brand {
		padding: 20px 10px 10px 15px;
	}
	.header .nav-collapse.collapse {
		width: calc(100% - 150px);
	}
	.header .nav-collapse .nav {
		padding-left: 0;
		padding-right: 0;
	}
	.rightMenus>ul {
	gap: 10px;
	}
	.header .navbar .nav li > a {
		padding: 25px 5px;
		font-size: 12px;
	}
	.header .navbar .nav>li.dropdown>a:after {
		top: 25px;
	}
	.header .navbar .nav > li:nth-last-child(3) > a {
		/* padding: 15px 15px 15px 8px; */
	}
	.header {
		height: 70px;
		min-height: 70px;
		}
	.headerSpace {
		height: 69px;
	}
	.header .nav-collapse .nav .dropdown .dropdown-menu {
		/* top: 100px; */
		padding: 20px 15px;
	}
	.headerlogin .nav-member-center {
		min-width: 110px;
		padding: 10px 10px;
	}
	.headerlogin .nav-member-center i {
	font-size: 12px;
	}
	.header-top .headerright .btn-logout {
		width: 160px;
		font-size: 13px;
	}
	.header-top .headerright p {
		font-size: 13px;
	}
	
	.headerlogin .nav-member-center p {
		font-size: 12px;
	}
	.searchBtnFn.dropdown>ul.dropdown-menu {
		width: calc(100% - 350px);
		right: 180px;
		top: 0;
	}
	
}

 @media screen and (max-width: 979px) {
	header .navbar .nav li.headerlogin.show-form .nav-member-center {
		display: none !important;
	}
	.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxthree {
		width: 100%;
		padding: 15px;
		background: #354F73;
		text-align: left;
	}
	
	.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxone {
		display: none !important;
		background: transparent !important;
		padding: 0 !important;
	}
	
	.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection {
		background: #932E26;
		padding: 0 !important;
		position: relative;
	}
	
	.header .navbar .nav li .memberSection a.toggle-form {
		font-size: 0;
		line-height: 1;
		padding: 0 4px 0 0;
		top: 5px;
		right: 5px;
		background: #ffffff;
		color: #354f73;
		display: inline-flex;
		width: 30px;
		height: 30px;
		align-items: center;
		justify-content: center;
	}
	
	.header .navbar .nav li .memberSection a.toggle-form i {
		font-size: 20px;
		margin: 0;
	}
	
	header .navbar .nav li.headerlogin span.menu-arrow {
		display: none !important;
	}
	.RedButton, .header .navbar .nav li.dropdown .memberSection a.RedButton {
		align-self: self-end;
	}
	
	header .navbar .nav li.headerlogin a.nav-member-center {
		background: var(--lightblue);
		display: flex;
		flex-wrap: nowrap;
		flex-direction: row;
		align-items: center;
		width: auto;
		height: auto;
		padding: 12px 30px;
		border-radius: 0;
		color: #ffffff !important;
	}

	header .navbar .nav li.headerlogin a.nav-member-center p {
		margin: 0;
		padding-left: 10px;
		color: #ffffff;
	}
	
	header .navbar .nav li.headerlogin a.nav-member-center:after {
		display: none !important;
	}
	
	header .top-strip {
		display: none;
	}
	.headerSpace {
		height: 69px;
	}
	.header {
		background: #ffffff;
		min-height: 70px;
	}
	.inner-page-content>.row-fluid {
		display: block;
		margin: 0 -15px;
		width: auto;
	}
	.droptitle {
		display: none;
	}
	.footer .row.d-flex-wrap>div.col3 {
		flex: 0 0 150px;
		max-width: 150px;
		margin-left: 0;
	}
	.loggedinBox {
		width: 100%;
		max-width: 100%;
		padding-right: 0;
	}
	.row.d-flex-wrap:before,.row.d-flex-wrap:after {
		display: none;
	}
	
	.footer .row.d-flex-wrap>div.col1, .footer .row.d-flex-wrap>div.col2, .footer .row.d-flex-wrap>div.footer-mobile-menus {
		flex: 0 0 100%;
		max-width: 100%;
	}
	.infoicon-sec .flex-row .col-6 {
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		max-width: 100%;
	}
	.row.d-flex-wrap {
		margin-left: -15px !important;
		margin-right: -15px !important;
	}
	
	.col1.footer-info:before {
		width: 100vw;
		left: 50%;
		right: auto;
		transform: translateX(-50%);
		margin-left: 0;
	}
	
	.footer .row.d-flex-wrap>div.col1 .footstar {
		width: 30vw;
	}

	.header .navbar .nav > li.mobile-links ul.dropdown-menu {
		display: flex !important;
		background: #ffffff !important;
		flex-wrap: wrap;
		justify-content: center;
	}
	
	.header .navbar .nav > li.mobile-links span.menu-arrow {
		display: none !important;
	}
	
	.header .navbar .nav > li.mobile-links ul.dropdown-menu a {
		padding: 10px !important;
		height: auto !important;
		display: inline-block;
		color: #ed3943 !important;
		font-size: 12px !important;
		border-style: none !important;
		height: auto !important;
		min-height: auto !important;
		line-height: 1.4;
	}
	
	.header .navbar .nav > li.mobile-links ul.dropdown-menu li {
		flex: 0 0 auto;
		width: auto;
		margin: 0;
		border-style: none;
		padding: 0;
	}
	
	.header .navbar .nav > li.mobile-links ul.dropdown-menu li:not(:first-child):before {
		content: "/";
		color: #d1d1d1;
	}

	.event-mobile .sbm-event {
		border-top: 1px solid #ebebeb;
		padding: 15px 0px;
		margin-bottom: 0;
		border-radius: 0;
		text-align: center;
	}
	.captionBtnBox ul li a .iconBox img {
		width: 30px;
		height: 30px;
	}
	.event-mobile .event-list {
		padding-top: 15px;
	}
	.event-mobile .event-list .sbm-event .sbm-e-head span {
		min-width: auto;
	}
	.event-mobile .event-list .sbm-event .sbm-e-head span:after {
		margin: 0 20px;
		position: relative;
	}
	.event-mobile .event-list .sbm-event .sbm-e-head {
		justify-content: center;
	}
	.header-member-form {
		display: none !important;
	}
	header .navbar .nav li.headerlogin {
		width: 100%;
		max-width: 100%;
		display: block;
		background: transparent;
		text-align: center;
		min-height: auto;
		border-bottom: none;
		padding-top: 20px;
		padding-bottom: 20px;
	}
	.home3 header .navbar .nav li.headerlogin,
	.home2 header .navbar .nav li.headerlogin {
		background: transparent;
	}
	header .navbar .nav li.headerlogin a.member-center-btn {
		text-align: center;
		display: inline-block;
		background: #A8462B;
		color: #ffffff;
		width: auto;
		margin: 0 auto;
		padding: 5px 40px !important;
		border-radius: 50px;
		min-height: auto !important;
	}
	.member-center-btn { 
		display: block;
	}
	header .navbar .nav li.headerlogin a.member-center-btn img {
		width: 35px;
		height: 35px;
		margin-right: 10px;
	}
	.header-drop-title {
		display: none;
		padding: 0;
		width: 100%;
		padding: 15px 0;
	}
	.mainMenuMob {
		display: block;
		width: 100%;
		background: transparent;
	}
	.mainMenuMob .mainMenuMob-col {
		display: block;
		width: 100%;
		padding: 0;
		float: none;
	}
	.mainMenuMob .mainMenuMob-col ul {
		margin:0;
	}
	.header .navbar .nav li.dropdown .dropdown-menu li>a {
		color: #083372;
		font-size: 18px;
	}
	.header .navbar .nav li.dropdown .dropdown-menu li a:hover {
		/* padding-left: C4112F15px; */
		color: #BE6846;
	}
	.footer-links ul.social-list li {
		margin-right: 9px;
	}
	img.footlogo {
		width: 90%;
		max-width: 150px;
	}
	.infoicon-sec .flex-row .col-3 {
		flex: 0 0 50%;
		-webkit-flex: 0 0 50%;
		max-width: 50%;
		margin-bottom: 30px;
	}
	.info-iconbox h2 {
		min-height: auto;
	}
	.header .navbar .nav li.open-droupdown a {
	}
	.captionFrame ul li:nth-child(3) {
	/* font-size: 34px; */
	}
	.whats-new-sec .flex-row>div.span8 {
		padding-left: 15px;
	}
	.whats-new-sec .flex-row>div.span8 {
		padding-left: 15px;
	}
	.newscard .news-inner-wrap {
		padding: 30px 30px 30px 100px;
	}
	.newscard .news-inner-wrap img {
		width: 50px;
		left: 15px;
	}
	.newscard .news-inner-wrap h2 {
		font-size: 25px;
		margin: 0 0 5px;
	}
	.magazine-block h2 {
		font-size: 25px;
		line-height: 1.5;
		margin-top: 20px;
	}
	.upcoming-event-sec .flex-row>div {
		width: 33.33%;
	}
	.img-card .img-holder span {
	width: 40px;
	height: 40px;
	font-size: 28px;
	}
	.img-card .img-holder span small {
		font-size: 16px;
	}
	.captionFrame ul li:nth-child(2) {
		margin: 0 0 10px;
		font-size: 30px;
		max-width: 100%;
	}
	.anouncebanner {
		display: block;
	}
.mainMenuMobBtn { cursor: pointer; display: inline-block; font-size: 20px !important; }
.mainMenuOnclickBtn { display: none; padding-left: 20px; }
.megaMenuSection.closeBox ul.mainMenuOnclick { display: none !important; }
.dropdown-menu>.megaMenuSection {margin-left: 0px;}
.header .navbar .nav li.memberFirst {
	padding: 0;
	margin: 20px 0 0 0;
	padding: 0 30px 20px 30px;
	background: #fff;
}
.header .navbar .nav li>.dropdown-menu {margin: 0;}
.header .nav-collapse .nav .dropdown .dropdown-menu>li>a {padding-left: 38px !important;}
.header .nav-collapse .nav .dropdown .dropdown-menu:after {
right: 0;
}

.header .nav-collapse .nav .dropdown .dropdown-menu>li>.dropdown-menu>li>a {
padding-left: 70px !important;
}
.header .navbar .nav li.memberFirst>a { background: #0BBA97; padding: 5px 20px; text-align: center; font-size: 14px; text-transform: uppercase; }
.header .navbar .nav li.memberFirst .dropdown-menu li p a { padding: 0px; height: auto; }
.header .navbar .nav li.memberFirst>a>img { margin-right: 20px; }
.header .navbar .nav li.memberFirst>.dropdown-menu { 
	margin: 0; padding: 0 !important;background: #2d2d2d; }
.header .navbar .nav li.memberFirst > .dropdown-menu .megaMenuSection {
	padding: 20px; background: #2d2d2d;
}
.header .navbar .nav li.memberFirst > .dropdown-menu .megaMenuSection.formDiv {
	padding: 0 20px 20px 20px !important;
}
.header .navbar .nav li.memberFirst.open-droupdown>a, .header .navbar .nav li.memberFirst.open-droupdown:hover>a, 
.header .navbar .nav li.memberFirst.open-droupdown:focus>a,
.header .navbar .nav li.memberFirst.open-droupdown:visited>a { background-color: #BA0C2F; color: #fff; }
.header .navbar .nav li.memberFirst.open-droupdown .menu-arrow {
 display: block; width: 100%; left: 0; height: 86px; top: 0px; opacity: 1; transform: none; text-align: center; }
.header .navbar .nav li.memberFirst.open-droupdown .menu-arrow:after { color: #fff !important; left: 10px; }
.header .navbar .nav li.memberFirst>.menu-arrow { top: 20px; }
.social-mobile, .mobile-links { display: block; }
.header .navbar .nav > li.social-mobile.dropdown {
	background: #fff;
	padding: 0px 0 30px 0;
	text-align: center;
}
.header .navbar .nav > li.social-mobile.dropdown .follow-us.dropdown-menu {
	display: block !important;
	padding: 20px 0  0!important;
	background: #ffffff;
}
.header .navbar .nav > li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection {
	font-size: 20px;
	font-weight: 600;
	display: inline-block;
	vertical-align: middle;
	width: auto;
	margin: 0 2px;
}
.header .navbar .nav > li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection:first-child {
	width: 100%;
	margin: 0 0 20px 0;
}
.header .navbar .nav > li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection a {
	width: 40px;
	height: 40px;
	color: #083372;
	line-height: 40px;
	font-size: 20px;
	text-align: center;
	border-radius: 50%;
	padding: 0;
}
.header .navbar .nav > li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection a:hover,
.header .navbar .nav > li.social-mobile.dropdown .follow-us.dropdown-menu .megaMenuSection a:focus {
	border-color: #709ED1;
	color: #ffffff;
	background: #709ED1;
}
.header .navbar .nav > li.social-mobile.dropdown .menu-arrow {
	display: none;
}
.header .navbar .nav > li:nth-last-child(2) > a {
	width: 100%;
}
.TitleText {font-size: 36px;}
.HeaderTextMediumLink { color: #008e89; font-size: 16px; text-decoration: none; }
.HeaderTextSmall {font-size: 20px;}

.HeaderTextMedium { font-size: 18px; }
.HeaderTextMediumLink { font-size: 16px; }
.captionBtnBox ul li a .iconBox { margin: 0px 0px; }
.captionBtnFrame { max-width: 320px; padding: 25px 10px; }
.captionBtnBox ul li a .arrow { float: right; padding: 9px 0px; }
.captionBtnBox ul li a .textBox h2 { font-size: 16px; }
.captionFrame { max-width: 370px; margin-left: 30px; }
.captionFrame h1 { font-size: 28px; margin-top: 10px; }
.captionFrame h3 { font-size: 18px; }
.slider .owl-carousel .item img {/* height: 405px; */object-fit: cover;}
.slider .owl-carousel .owl-dots { bottom: 30px; }


.xsHidden979 { display: none !important; }
.header .navbar .nav>li { padding: 0 30px; }
.xs979 { display: block !important; }
.header .navbar .nav .searchBtnFn.xs979 { margin: 0px; padding: 0; margin-bottom: 0px; }
.header .navbar .nav .searchBtnFn.xs979 ul.dropdown-menu {display: block !important;padding-left: 0px !important;padding: 0 !important;}
.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe {display: inline-block;vertical-align: top;width: 100%;margin: 0;background: #1F2A44;border-bottom: 1px solid #f7f7f7;height: 60px;padding: 10px;border-radius: 0;}
.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe form { display: inline-block; width: 100%; margin: 0; }
.header .navbar .nav li.dropdown.searchBtnFn.xs979 .menu-arrow {display: none;}
.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe input {
	color: #ffffff;
	font-size: 18px;
	font-weight: 300;
	background: #1F2A44;
	padding: 5px 15px;
	width: 100%;
	box-shadow: none;
	font-family: 'Merriweather';
	letter-spacing: 0.02em;
	}
.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe a { border: 0; margin: 0; 
	height: auto;
}


.header .navbar .nav li.dropdown>a.dropdown-toggle:after, 
.header .navbar .nav li.dropdown:hover>a::after, 
.header .navbar .nav li.dropdown:focus>a::after, 
.header .navbar .nav li.dropdown:visited>a::after,
.header .navbar .nav li.dropdown:hover>a::after,
.header .navbar .nav li.dropdown:focus>a::after, 
.header .navbar .nav li.dropdown:visited>a::after {
	display: inline-block;
	top: 12px;
	left: auto;
	right: 30px;
	position: absolute;
}
.header .navbar-brand {
	padding: 5px 20px 5px 0px;
	line-height: 60px;
	position: relative;
	z-index: 2;
	height: 70px;
	max-width: 300px;
	width: 300px;
}
.header .navbar .nav li.dropdown.searchBtnFn.xs979 .megaMenuSection .formframe a {
	margin: 0;
	top: 10px;
	position: absolute;
	right: 10px;
	left: auto;
	color: #ffffff;
	font-size: 18px;
	padding: 2px 12px;
	z-index: 99;
	display: inline-block;
}

.btn.btn-navbar { min-width: auto; }
.navbar .btn-navbar .icon-bar { width: 38px; margin: 0px auto 4px; height: 6px; border-radius: 2px; }
.navbar .nav>li { width: 100%; }
.header .nav-collapse.collapse {margin: 0;background: #1F2A44;opacity: 1;position: fixed;top: 0;width: 100%;display: block;height: 0px;left: 0;}
.header .nav-collapse.collapse .nav { padding: 0px; }
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.formDiv {
	width: 100%;
}
.header .navbar .nav > li {
	max-width: 100%;
	height: auto;
	position: relative;
	width: 100%;
	vertical-align: top;
	border-bottom: 0px solid #E1E3E5;
	padding: 0;
	min-height: auto;
}
.header .navbar .nav li a, .header .navbar .nav li a, .header .navbar .nav li .dropdown-menu > li > a {
	/* text-transform: uppercase; */
}
.header .navbar .nav li.dropdown .megaMenuSection .HeaderText { display: none; width: 100%; }
.header .navbar .nav { position: relative; }
.header .navbar .nav>li>a { margin: 0; padding: 0; border: 0px solid; background-color: transparent; height: auto; }
.header .navbar .nav li:last-child a img {margin-right: 0;margin-bottom: 0px;margin-top: 0;}
	 header .navbar .nav li.headerlogin a.nav-member-center img {
    filter: brightness(100);
    width: 20px;
}
.header .navbar .nav li:last-child { position: relative; border:0; }
	 .hbf-design header .navbar .nav li.headerlogin {
		 background: #ffffff;
	 }

.brand { margin-left: -45px; max-width: 250px; }
.header .navbar .container { width: 750px; }
.container {width: 750px;}
.navMain { float: none; height: 40px; padding: 0; text-align: center; }
.header .navbar-inner { width: 100%; }
.nav>.dropdown { padding-bottom: 0; }
.navbar .btn-navbar .icon-bar { width: 30px; margin: 0px auto 4px; height: 4px; border-radius: 3px; }
.navbar .btn-navbar .icon-bar:last-child { margin-bottom: 0; }
.dropdown-menu { width: 100%; }
.header .nav-collapse {float: none;padding: 0;width: 100%;z-index: 99;max-height: calc(100vh - 0px);overflow-y: auto;}
.header .nav-collapse li { display: block; width: 100%; padding-bottom: 0px; }
.header .navbar .nav li>a, .header .navbar .nav li .dropdown-menu>li:last-child>a {border: none;margin: 0;}
.header .navbar .nav>li:last-child .menu-arrow {display: inline-block;width: 100%;left: 0;height: 50px;top: 0;opacity: 0;}
.header .navbar .nav li .dropdown-menu>li>a { padding: 15px 15px; font-size: 13px; }
.header .navbar .btn-navbar {
	margin: 0;
	position: absolute;
	right: 15px;
	top: 15px;
	background: none;
	border: none;
	-moz-border-radius: 4px;
	-ms-border-radius: 4px;
	-o-border-radius: 4px;
	-webkit-border-radius: 4px;
	border-radius: 4px;
	box-shadow: none;
	line-height: 1.42857;
	margin: 0;
	padding: 10px 12px;
	z-index: 1;
	}
.header .navbar .btn-navbar.collapsed {
	border-radius: 2px;
	color: #ffffff;
	padding: 26px 0px;
	height: auto;
	line-height: normal;
	margin-right: 0;
	margin-top: 0;
	width: 30px;
	z-index: 1;
	text-align: center;
	border-radius: 3px;
	top: 0;
	margin: 0;
}
.header .navbar .btn-navbar {
	top: 7px;
	padding: 0;
	margin: 30px 0;
}
.header .navbar-inner {position: relative;top: 0;width: 750px;margin: 0 auto;background: transparent;}
.navIcon { background: #0c1923; min-height: 52px; z-index: 9; width: 100%; }
.header .navbar .btn-navbar .icon-bar {background: #231f20 !important;box-shadow: none;}
.header .navbar .btn-navbar.collapsed .icon-bar { 
	width: 30px; height: 4px; border-radius: 0; background: #2d2d2d !important; opacity: 1
}
.header .navbar .btn-navbar .icon-bar:first-child {
	transform: rotate(45deg);
}
.header .navbar .btn-navbar .icon-bar:nth-child(2) {
	display: none;
}
.header .navbar .btn-navbar .icon-bar:last-child {
	transform: rotate(-45deg);
	margin-top: -8px;
}
.header .navbar .btn-navbar.collapsed .icon-bar:first-child,
.header .navbar .btn-navbar.collapsed .icon-bar:nth-child(2),
.header .navbar .btn-navbar.collapsed .icon-bar:last-child {
	transform: none;
	display: block;
	margin-top: 0;
}
.header .navbar .btn-navbar:hover .icon-bar { background: #2d2d2d; }
.header .navbar .nav li>a, .header .navbar .nav li .dropdown-menu>li>a {text-align: center;color: #333;border-radius: 0;}
.header .navbar .nav li>a, .header .navbar .nav li>a, .header .navbar .nav li .dropdown-menu>li>a 
{
	font-size: 15px;
	border: none;
	border-top-width: medium;
	border-bottom-width: medium;
	border-top-style: none;
	border-bottom-style: none;
	border-top-color: currentcolor;
	border-bottom-color: currentcolor;
	border-bottom-width: medium;
	border-bottom-style: none;
	border-bottom-color: currentcolor;
	border-top: 0px solid rgba(255, 255, 255, .5);
	background: transparent;
	font-weight: 500;
	line-height: 1.42857;
	color: #FFFFFF;
	text-decoration: none;
	padding: 13px 20px;
	padding-right: 0px;
	padding-right: 0px;
	text-align: left;
	margin-bottom: 0px;
	box-shadow: none;
	line-height: 29px;
	cursor: pointer;
	letter-spacing: 1px;
	}
.header .navbar .nav>li:last-child>a {/* width: 100%; *//* margin: 0; *//* text-align: center; */}
.header .navbar .nav li:hover>a, .header .navbar .nav li:focus>a, .header .navbar .nav li>a:hover, .header .navbar .nav li>a:focus {
	background: transparent;
	color: #be6846;
	font-weight: 700;
	text-shadow:none;
	outline: none;
	}
.header .navbar .nav li.memberFirst:hover a, .header .navbar .nav li.memberFirst:focus a {
 background: #BA0C2F; color: #fff; }
.header .navbar .nav li:hover .menu-arrow::after, .header .navbar .nav li:focus .menu-arrow::after { color: #0BBA97; }
.header .navbar .nav li.dropdown .megaMenuSection li a:focus {color: #C4112F;}
.header .nav-collapse .nav .dropdown .dropdown-menu {background: #ffffff;flex-wrap: wrap;margin-bottom: -1px;padding: 0px 0px;border-top: 1px solid #E1E3E5;}
.navbar .nav li.dropdown>.dropdown-toggle .caret { float: right; border-top-color: #eeeeee; border-bottom-color: #eeeeee; }
.navbar .nav li.dropdown>.dropdown-toggle:hover .caret { border-top-color: #006eb3; border-bottom-color: #006eb3; }
.header .navbar .pull-right>li>.dropdown-menu, .header .navbar .nav>li>.dropdown-menu {position: static;float: none;width: auto;/* margin-top: 0; */background-color: transparent;border: 0;box-shadow: none;margin: 0px;padding: 0;}
.dropdown .dropdown-menu { position: static; float: none; width: auto; margin-top: 0; background-color: transparent; border: 0; box-shadow: none; padding: 5px 0; }
.dropdown .dropdown-menu li {padding: 0;background: transparent;position: relative;}
.header .navbar .nav li .dropdown-menu>li>a:hover { background: #c1d82f; color: #3b3b3c; }
.dropdown-menu>li.active>a { color: #44687d; }
.header .navbar .nav li.dropdown>.dropdown-menu li>a {border: 0;text-align: left;padding: 15px 0 15px 20px;background: transparent;color: #2B2F32;font-weight: 500;font-size: 15px;margin-bottom: 0px;border-bottom: 1px solid #E1E3E5;margin-top: -1px;}
.header .nav li .dropdown-menu>li.dropdown-submenu li { padding: 0px 10px; }
.header .nav li .dropdown-menu>li.dropdown-submenu li a { background: transparent; font-weight: normal; }
.dropdown-submenu .caret { float: right; transform: rotate(-90deg); -webkit-transform: rotate(-90deg); -moz-transform: rotate(-90deg); -o-transform: rotate(-90deg); -ms-transform: rotate(-90deg); border-top-color: #eeeeee; border-bottom-color: #eeeeee; margin-top: 6px; }
.dropdown-submenu a:hover .caret { border-top-color: #fff; border-bottom-color: #fff; }
.header .navbar .nav li .dropdown-menu>li>a:hover {color: #3b3b3c;}
.header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:hover, .header .navbar .nav li a:hover, .header .navbar .nav li a:focus, .navbar .nav li.dropdown.open>.dropdown-toggle, .navbar .nav li.dropdown.active>.dropdown-toggle, .navbar .nav li.dropdown.open.active>.dropdown-toggle, .dropdown:hover .dropdown-toggle { -moz-border-radius: 0; -ms-border-radius: 0; -o-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; }
.dropdown-menu { margin-left: 0 !important; }
.header .nav-collapse li .menu-arrow::after { display: none; content: "\f107"; font-family: 'FontAwesome'; position: absolute; right: 17px; top: 12px; color: #fff; font-size: 24px; z-index: 99999; width: 15px; height: 15px; line-height: 15px; opacity: 1; font-weight: bolder; transform: rotate(270deg); font-weight: 300; }
.header .nav-collapse li.open-droupdown .menu-arrow { transform: none; }
.header .nav-collapse li.dropdown.memberFirst.xs979.open-droupdown .menu-arrow::after { content: "\f00d"; font-family: 'FontAwesome'; font-weight: 100; font-size: 18px; }
.header .nav-collapse li .menu-arrow {
	cursor: pointer;
	width: 100%;
	background: transparent;
	left: 0;
	top: 0;
	position: absolute;
	height: 54px;
	z-index: 999;
	}
.header .nav-collapse li.dropdown:hover:after, .header .nav-collapse li.dropdown.open::after { color: #9a0203; }
.header .nav-collapse .nav {/* overflow-y: auto; */margin: 0;width: 100%;float: none;padding: 0;display: block;}
.navbar .btn-navbar .icon-bar { transition: all ease-in-out 0.3s; }
.navMain { box-sizing: border-box; display: block; height: 100%; left: 0; max-height: 0; opacity: 0; overflow-x: hidden; overflow-y: auto; position: static; -moz-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; -ms-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; -o-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; -webkit-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; width: 100%; z-index: 999; }
body.overlay { position: fixed; width: 100%; }
body.overlay .navMain { max-height: 100vh; opacity: 1; }
.overlay header {position: fixed;top: 0;width: 100%;background: #FCFEFF;}

.overlay .overlay div#semwebcatalog_application .browseLink-in li a, .overlay div#semwebcatalog_application .well, .overlay div#semwebcatalog_application .browseLink-in li a { position: static; }
.navMain { border-bottom: none; }
.interestGroup>.dropdown-menu li p:before { display: none; }
.header .navbar .nav li.dropdown.interestGroup .megaMenuSection p.HeaderText { font-size: 18px !important; font-weight: 500; margin-bottom: 0px; }
.header .navbar .nav li.dropdown.interestGroup .megaMenuSection p.HeaderText:hover, .header .navbar .nav li.dropdown.interestGroup .megaMenuSection ul li a:hover { text-decoration: underline; }
.header .navbar .nav li.dropdown.interestGroup .megaMenuSection ul li a { padding-left: 20px; }
.header .navbar .nav li.dropdown.interestGroup .megaMenuSection.xs979 .heading { position: static; text-align: left; margin-top: 20px; }
.row-fluid .event_outer { width: 33.33%; margin: 0; }
.eventimgText .HeaderText { font-size: 24px; }
.eventimgText .HeaderText:after { bottom: -22px; }
.event_outer:last-child .eventimgText .HeaderText:after { display: block; }
.sliderFrame .item ul li { padding-right: 0; }
.sliderFrame { padding: 0 15px; }
.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.cta { display: none; }
.header .navbar .nav li:nth-last-child(1) ul.memberSection ul li a { height: auto !important; }
.header .navbar .nav li:nth-last-child(1).dropdown .megaMenuSection .HeaderText { display: block; }
.mainContent { width: calc(100% - 280px) }
.pd_70 { padding: 50px 0; }
.captionBtnBox ul li a .iconBox {
	width: 40px;
	height: 32px;
	position: relative;
	top: 1px;
}
.captionBtnBox ul li a .textBox {
	left: 70px;
}
.captionBtnBox ul li a .arrow {
	padding: 5px 0px;
}
.eventBoxFrame {
	padding: 15px 10px;
	margin-bottom: 50px;
}
.captionBtnBox ul li a .iconBox img.default { width: 30px; }
.eventBoxFrame .HeaderTextSmall {
	line-height: 25px;
	margin-bottom: 25px;
}
.captionFrame ul {
	margin-bottom: 20px;
}
.carousel-caption {/* top:44%; */min-height: 240px;}
.captionFrame ul li h1 { font-size: 32px; text-align: left; }
.captionFrame ul li small { font-size: 16px; }
.footer-info a > img {
	width: 235px;
}
.footer .footer-info {
	width: 100%;
	margin-bottom: 15px;
	position: relative;
}
.follow-us {
	position: absolute;
	right: 0;
	top: 0;
}
ul.follow-us li { font-size: 16px; }
.footer-info p { margin: 25px 0 0 0; }
.footer .footer-links, .footer .contact-links {
	width: 100%;
	flex: 0 0 350px;
	max-width: 350px;
	margin: 0;
	padding-right: 15px;
}
	 .footer-links-wrap .footer-form {
    flex: 0 0 95%;
    -webkit-flex: 0 0 95%;
}
ul.follow-us li:first-child {
	margin: 0 0 5px 0;
	display: block;
}
ul.follow-us li { margin: 0 4px 0 0; }
.footer-links ul li a, .contact-links ul li a, .contact-links ul li span, .footer-links.contact-links ul li {
	font-size: 16px;
	line-height: 20px;
}
.footer-links ul li {
	margin-bottom: 8px;
}
.contact-links ul li {
	margin-bottom: 10px;
	margin-right: 0;
}
.copyright p, .copyright p a {
	font-size: 14px;
}
.copyright p a {
	padding: 0 6px 0 10px;
}
.copyright p a:first-child {
	margin-left: 15px;
}
/*****************/
.inner-page-content {
	min-height: inherit !important;
}
.inner-page-content .inner-content-area {
	padding: 30px 0 0;
	margin-bottom: 30px;
}
.content-info { padding: 30px; }
.inner-page-content .sidebar, .inner-page-content .inner-content-area {
	width: 100%;
	position: static;
	padding: 0px;
	flex: 0 0 100%;
	max-width: 100%;
}
.quicklink-mobile {
	display: block;
	background: #2d2d2d;
	margin: 30px 30px 0;
	background: #FFFFFF;
	box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
	padding: 15px 20px;
}
.quicklink-mobile h3 {
	margin: 0;
	color: #083372;
	position: relative;
	font-size: 20px;
	font-weight: 400;
}
.quicklink-mobile h3:before {
	content: "\f18e";
	font-family: FontAwesome;
	position: absolute;
	top:0;
	right:0;
}
.quicklink-mobile h3.quicklink-open:before {
   content: "\f01a";
}
.event-mobile {
	display: block;
	background: #2d2d2d;
	margin: 30px 30px 0;
	background: #FFFFFF;
	box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
	padding: 15px 20px;
}
.event-mobile h3 {
	margin: 0;
	color: #083372;
	position: relative;
	font-size: 20px;
	font-weight: 400;
}
.event-mobile h3:before {
	content: "\f18e";
	font-family: FontAwesome;
	position: absolute;
	top:0;
	right:0;
}
.event-mobile h3.event-open:before {
   content: "\f01a";
}
.DiamondBullets ul, .event-list ul {
	padding: 20px 0 0 0px;
	margin: 0;
	list-style: none;
}
.quicklink-desktop { display: none; }
.events {
	margin-top: 0;
}
.sponsors-boxthree {
	margin: auto;
	width: 250px;
}
.Highlight {
	margin: 30px -30px 40px;
}
.header .navbar .nav li.dropdown .memberSection .megaMenuSection .DiamondBullets ul {
	padding: 0;
}
.header .navbar .nav li.dropdown .memberSection .megaMenuSection .DiamondBullets ul li {
	margin-bottom: 8px;
	padding-left: 30px;
}
.header .navbar .nav li.dropdown .memberSection .megaMenuSection .DiamondBullets ul li a {
	padding: 0;
	font-size: 16px;
}
.header .navbar .nav li.dropdown .memberSection .megaMenuSection .DiamondBullets ul li a:before {
	top:3px;
}
.carousel-caption {
    /* position: relative; */
    /* top: 0; */
    /* transform: none; */
    /* max-width: 780px; */
}
.captionFrame {
    max-width: 100%;
    margin: 0;
    padding: 20px;
    text-align: center;
}
	 .captionFrame ul li:nth-child(4) {
		 justify-content: center;
		 flex-wrap: wrap;
		}
.captionBtnBox {
    position: relative;
    height: auto;
    background: #2f3855;
}
.captionBtnBox .captionBtnFrame {
    width: 100%;
    position: relative;
    max-width: 780px;
    padding: 20px 30px;
    margin: 0 auto;
    background: transparent;
}
.slider .owl-carousel .owl-dots {
    position: absolute;
    transform: translateX(-50%);
    flex-direction: row;
    justify-content: center;
    height: auto;
    top: unset;
    left: 50%;
    bottom: 15px;
}
.captionBtnBox ul li a {
    min-height: 60px;
}
.captionBtnBox ul li a .iconBox svg {
    width: 30px;
    height: 30px;
}
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.formDiv {
    padding: 0;
}
body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
    height: auto;
    display: block !important;
	width: 100%;
}
header .navbar .nav li.headerlogin.show-form .header-member-form {
    display: flex !important;
}
header .navbar .nav li.headerlogin .header-member-form {
    background: #a8462b;
    padding: 30px 20px 15px 20px;
}
.header .navbar .nav li form a:last-child {
    font-size: 14px;
    line-height: 1.2;
    margin-top: 10px;
}
header .navbar .nav li.headerlogin .header-member-form {
    text-align: left;
}
header .navbar .nav li form a.MAJButton {
    height: auto;
    min-height: auto;
    line-height: 1.2;
}
header .navbar .nav li.headerlogin.show-form .header-member-form .MAJButton,
header .navbar .nav li.headerlogin.show-form .header-member-form a:last-child {
	color: #ffffff;
}
header .navbar .nav li.headerlogin.show-form .header-member-form .MAJButton {
	background: #472103;
	line-height: 1.2;
}
header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 0 !important;
    width: 30px;
    height: 30px;
    background: #472103;
}
header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form>img {
    display: none;
}
header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form {
    font-size: 0;
}
header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form:before, header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form:after {
    content: "";
    width: 15px;
    height: 2px;
    background: #ffffff;
    display: inline-block;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}
header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form:after {
	-webkit-transform: translate(-50%, -50%) rotate(-45deg);
	transform: translate(-50%, -50%) rotate(-45deg);
}

header .navbar .nav li.headerlogin.show-form a.member-center-btn.toggle-form:before {
    -webkit-transform: translate(-50%, -50%) rotate(45deg);
    transform: translate(-50%, -50%) rotate(45deg);
}
header .navbar .nav li.headerlogin.show-form .header-member-form>p {
    margin-bottom: 8px;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxtwo {
    width: 100%;
    text-align: left;
    padding: 35px 15px 15px;
    background: #354F73;
}
.slider .owl-carousel .item {
    min-height: 100%;
}
.flex-sec .container .eventbox-item-in {
	padding: 20px 30px;
}
.eventbox-item p {
	font-size: 20px;
}
.event-head h4 {
}
.event-head img {
	font-size: 40px;
}
.inner-page-content .leftInner.widget {
	width: 100%;
}
.event-link {
	font-size: 16px;
}
.sponsors-link ul a {
	min-width: auto;
}
.sponsors-link ul a {
	padding: 12px 20px;
}
.sponsor-with-fb-wid .right-col,
.sponsor-with-fb-wid .left-col {
    flex: 0 0 100%;
    max-width: 100%; 
}
.sponsors-link {
	margin-bottom: 40px;
}
.fb-wid .fb-wid-head {
    padding: 15px 15px;
}

.fb-wid-logo-wrap img {
    flex: 0 0 50px;
    -webkit-flex: 0 0 50px;
    max-width: 50px;
}

.follow-us-btn {
    padding: 7px 15px;
}
.header .navbar .nav li.dropdown .megaMenuSection h2 {
	font-size: 30px;
	margin-bottom: 20px;
}
	 .header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxthree p.HeaderText {
    margin: 10px 0 0;
}
.sponsor-with-fb-wid .left-col {
	margin-bottom: 20px;
}
.inner-content-area .sponsors-sec {
	margin: 0;
}
.inner-content-area .sponsors-sec:before,
.inner-page-content .sidebar {
	display: none;
}
.header .navbar-inner .container>.social-list {
	display: none;
}
.header .navbar .nav>li:not(:last-child)>a {
    margin: 0;
} 
header .navbar .nav li.headerlogin a.nav-member-center img {
    filter: brightness(100);
    width: 20px;
}

.header .navbar .nav li.dropdown .memberSection li form .WhiteBorder {
    width: auto;
}

.header .navbar .nav li.dropdown .memberSection li form a:last-child {
    margin-top: 0;
}

.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.model-bg {
    position: relative;
    width: 100%;
    height: 1px;
}

.header .nav-collapse .nav .dropdown .dropdown-menu li.megaMenuSection.model-bg img {
    display: none;
}
.flex-sec .flex-row>.col {
	flex: 0 0 50%;
	max-width: 50%;
	margin: 15px 0;
}
.flex-sec .flex-row>.col:nth-child(3) {
    flex: 0 0 100%;
    max-width: 100%;
}
.header .nav-collapse.collapse .social-list>li {
	width: auto !important;
    margin: 0 10px;
}
.header .nav-collapse.collapse .social-list {
	position: relative;
	top: 0;
	left: 0;
	justify-content: center;
}
.hbf-design header .navbar .nav li.headerlogin a.nav-member-center {
	background: #316900;
}
.hbf-design .header .navbar-brand img {
    width: 90px;
}

.hbf-design .header .navbar-brand {
    padding: 10px 0px 15px 5px;
}
.header .navbar .nav li.dropdown .dropdown-menu li>a {
    line-height: 1.3;
}
.rightMenus {
    display: block;
    width: 100%;
    position: relative;
    padding: 20px;
}

.headerlogin .nav-member-center {
    width: 100%;
    max-width: 50%;
    margin: 0 auto;
}

.header .navbar .container {
    padding: 0;
}
.mainMenuMob-list li .SubHeading {
    font-size: 15px;
    font-weight: 700;
    margin: 0 0 -1px;
    padding: 12px 20px;
    border-bottom: 1px solid #E1E3E5;
}
.header-top .headerright p {
    display: none;
}
.caaa-tabs, .anchore-list {
    box-shadow: 0 2px 14px #E1E3E5;
    position: relative;
    display: block;
    padding: 8px 0;
}

.anchore-list li,
.caaa-tabs .btn {
    position: absolute;
    left: 0;
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    opacity: 0;
    padding: 6px 20px !important;
    color: #2B2F32;
    text-align: left;
    top: 0;
}
.anchore-list li {
	padding: 0 !important;
}

.caaa-tabs .btn.active,
.anchore-list li.active {
    position: relative;
    opacity: 1;
    color: #2B2F32;
}
.anchore-list li a {
	padding: 10px 15px;
	display: block;
}

.caaa-tabs span {
    position: absolute;
    z-index: 999;
    width: 100%;
    height: 48px;
    left: 0;
    background: transparent;
    top: 0;
}


.caaa-tabs.open-droupdown .btn {
    position: relative;
    opacity: 1;
}

.caaa-tabs.open-droupdown span {
    display: none;
}
.caaa-tabs:after ,
.anchore-list:after {
    content: "\f078";
    position: absolute;
    display: inline-block;
    font-weight: 900;
    font-family: 'FontAwesome';
    right: 15px;
    top: 12px;
}

.img-card {
	padding: 20px;
}
.left-img-card .li-img {
    flex: 0 0 200px;
	max-width: 200px;
}

.left-img-card .li-content {
    flex: 0 0 calc(100% - 200px);
    max-width: calc(100% - 200px);
    padding-left: 20px;
}
.numberInfo>li {
    padding: 20px 10px;
}
.border-card {
	margin-bottom: 30px;
}
.footer-links-wrap {
	padding-left: 15px;
	border-style: none;
	padding-right: 15px;
}
.mainMenuMob-list li .SubHeading:after {
    content: "\f078";
    display: inline-block;
    font-family: 'Font Awesome 5 Pro';
    position: absolute;
    right: 15px;
    top: 12px;
}
.mainMenuMob .mainMenuMob-col ul>li:not(:first-child) {
    display: none;
}
.header .navbar .nav li.megaMenuSection>.menu-arrow {
    display: none;
}

.mainMenuMob .mainMenuMob-col ul>li {
    padding: 0;
    margin: 0;
}

.mainMenuMob .mainMenuMob-col ul.open-droupdown>li:not(:first-child) {
    display: block;
}

.mainMenuMob .mainMenuMob-col ul>li:not(:first-child)>a {
    padding-left: 35px !important;
}
.headerlogin .header-drop-title {
    display: block;
}

.header .navbar-inner .headerlogin .dropdown-menu {
    padding: 10px 25px 30px;
}
.mainMenuMob-list.open-droupdown li .SubHeading:after {
    content: "\f077";
}

.header .navbar .nav > li.dropdown.open-droupdown>a:after {
    content: "\f077";
}
.header-top .container {
	padding: 0;
}
.anchore-list .openList {
    position: absolute;
    width: 100%;
    height: 100%;
    display: block;
    z-index: 1;
    left: 0;
    top: 0;
    opacity: 1;
}
ul.anchore-list.open-droupdown li {
    opacity: 1;
    position: relative;
}


.header .navbar .nav > li.dropdown.open-droupdown>a {
    color: #be6846;
    background: #fff;
}
.theme2 .header .navbar .nav > li.dropdown.open-droupdown>a {
    border-bottom: 2px solid #0083A9;
    color: #0083A9;
}
.theme2 .header .navbar .nav > li.dropdown.open-droupdown>a {
    border-bottom: 2px solid #772583;
    color: #772583;
}

.footer-mobile-menus .accordion-heading .accordion-toggle{color:#83B7DE;font-size:14px;font-family:var(--montserrat);font-weight: 700;border-style:none;padding:10px 0;}
.footer-mobile-menus .accordion-heading{border-style:none;}
.footer-mobile-menus .accordion-group{border-style:none none solid;border-radius:0px;padding:0;border-bottom:2px solid #83B7DE;}
.footer-mobile-menus .accordion-group .accordion-body .accordion-inner{border-style:none;padding:0 0 20px;}
.footer-mobile-menus .accordion-group .accordion-body .accordion-inner ul{list-style:none;padding:0;margin:0;}
.footer-mobile-menus .accordion-group .accordion-body .accordion-inner ul li a{color:#ffffff;line-height:1.2;font-size:16px;font-family:var(--montserrat);padding:4px 0;display:block;}
.footer-mobile-menus .accordion-heading .accordion-toggle:after{content:"\f078";font-weight:900;font-family:'Font Awesome 6 Free';display:inline-block;left:auto;top:12px;right:0px;position:absolute;border-style:none;width:auto;height:auto;color:#ffffff;transform:scaleY(-1);}
.footer-mobile-menus .accordion-heading .accordion-toggle{position:relative;letter-spacing:0.06em;text-transform:uppercase;}
.footer-mobile-menus .accordion-heading .accordion-toggle.collapsed:after{transform:scaleY(1);}

.footer .row.d-flex-wrap>div.col2 {
	/* display: none; */
	padding: 0 15px;
}
.important-dates-sec .span4 {
	flex: 0 0 39%;
	width: 39%;
}
.member-card .owl-dots {
    top: 275px;
}

.member-card .owl-carousel .owl-nav button.owl-prev, .member-card .owl-carousel .owl-nav button.owl-prev:hover, .member-card .owl-carousel .owl-nav button.owl-next, .member-card .owl-carousel .owl-nav button.owl-next:hover {
    top: 286px;
}
.quick-links-sec .container .span8,
.quick-links-sec .container .span4 {
    flex: 0 0 100%;
    max-width: 100%;
}
.quick-link-right-box {
    padding-top: 30px;
    margin: 30px 0;
    padding-left: 0;
    text-align: center;
}

.quick-link-right-box:before {
    left: 0;
    height: 4px;
    width: 100%;
    top: 0;
}
.header .navbar .nav>li.dropdown>a:after {
	font-size: 100%;
}


.captionFrame ul li:nth-child(1) .banner-img-wrap {
    width: 500px;
    height: 500px;
    left: 0px;
}

.captionFrame ul li:nth-child(1):before {
    width: 510px;
    height: 510px;
    left: -8px;
    top: 0;
    top: -34px;
}

.captionFrame ul li:nth-child(1) {
    top: auto;
    bottom: 220px;
    left: 50%;
    right: auto;
    text-align: center;
    transform: translateX(-50%);
}

.heroBanner {
    min-height: auto;
}

.captionFrame ul {
    padding-bottom: 230px;
}
/* .quick-link-box {
    padding: 75px 20px 20px;
}

.quick-link-box span.left-icon {
    position: absolute;
    top: 20px;
    left: 20px;
} */
.headerlogin.dropdown:hover  .nav-member-center {
    border-radius: 25px;
}
.bannerInner .banner-img-wrap {
    width: 480px;
    min-width: 480px;
    height: 480px;
    left: 0;
}
    .circle-wrap:before {
        width: 490px;
        min-width: 490px;
        height: 490px;
        left: 50%;
        top: 0;
        transform: translateX(-50%);
    }

.circle-wrap {
    top: auto;
    bottom: -60px;
    display: flex;
    justify-content: center;
    left: 0;
}
.inner-page-content .container {
	padding: 0 15px;
}
.rightMenus li.headerlogin {
    width: auto;
    text-align: center;
}

.rightMenus ul {
    /* flex-wrap: wrap; */
	justify-content: center;
}

.header .nav-collapse li.logout-btn {
    /* display: none; */
	        width: auto;
}
.quick-links-wrapper li {
    padding: 15px 20px;
    min-height: auto;
}

.captionFrame ul li:nth-child(1):before {
    left: -258px;
}
.captionFrame ul li:nth-child(1) .banner-img-wrap {
    left: -250px;
}
.footer .footer-links ul + h3 {
	margin-top: 0;
}
.footer .footer-links ul {
	margin-bottom: 30px;
}
.inner-page-content .row.d-flex-wrap {
    flex-direction: column-reverse;
}
.eventbox-col:not(.open) .eventbox-info {
    display: none;
}
.eventbox-col:not(.open) .event-head {
    border-bottom: none;
}
.eventbox-col  .event-head:after {
    content: "\f107";
    display: inline-block;
    font-weight: 900;
    font-family: 'Font Awesome 5 Pro';
    position: absolute;
    right: 0;
    top: 8px;
    padding: 15px;
    z-index: 1;
}
.leftInner  .eventbox-col.adv-box {
	display: none;
}

.contact-links ul:nth-child(2) {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.contact-links ul:nth-child(2) i {
    width: 40px !important;
    flex: 0 0 40px;
}
ul.social-list {
	justify-content: center;
	border-bottom: 1px solid #ffffff;
	margin: 0 0 0px;
}
.footer-title {
	text-align: center;
}

 }
/* max979px */
 @media only screen and (min-width:768px) {
	.for-mobile {
		display: none;
	}
 }
 @media only screen and (max-width:767px) {
	.evTitleZoneD{
		padding-left: 25px !important;
	}
	p, .BodyText, body, html {
		font-size: 16px;
	}
	p.BodyTextLarge, .BodyTextLarge {
		font-size: 18px;
	}
	.btns-wrap .MAJButton {
		margin-bottom: 15px;
	}
	.BulletList-row .BulletList {
		-webkit-flex: 0 0 100%;
		flex: 0 0 100%;
		max-width: 100%;
	}
	.BulletList-row {
		flex-wrap: wrap;
	}
	.BulletList-row  .BulletList ul {
		margin-bottom: 0;
	}
	.sponsors-link ul a {
		padding: 12px 20px;
		font-size: 16px;
	}

	.row.row-flex>.span4,
	.row.row-flex>.span8 {
		flex: 0 0 100%;
		-webkit-flex: 0 0 100%;
		max-width: 100%;
		width: 100%;
	}
	.footer .row.d-flex-wrap>div.col2, .footer .row.d-flex-wrap>div.col3, .footer .row.d-flex-wrap>div.col4, .footer .row.d-flex-wrap>div.col5 {
	}
	
	.footer .row.d-flex-wrap>div.col2, .footer .row.d-flex-wrap>div.col3 {
		flex: 0 0 100%;
		max-width: 100%;
		margin: 0;
		padding: 0 15px;
	}
	.friendsSliderBox .owl-carousel .owl-item ul li {
		max-width: 46%;
	}
	.home3 .footer .row.d-flex-wrap>div.col5, .home3 .footer .row.d-flex-wrap>div.col2 {
		flex: 0 0 50%;
		max-width: 50%;
		padding-top: 30px;
	}
	
	.footer .row.d-flex-wrap {
		justify-content: center;
	}
	
	.footer .row.d-flex-wrap>div.col5 {
		text-align: center;
		flex: 0 0 100%;
		max-width: 100%;
		padding-top: 0px;
	}
	
	.footer .row.d-flex-wrap>div.col5.footer-links h3:after {
		margin: 15px auto 15px;
	}
	
	.footer .row.d-flex-wrap>div.col5.footer-links .social-list {
		justify-content: center;
		margin-left: 10px;
	}
	.sponsors-img-list ul li {
		max-width: 50%;
	}
	.whats-new-sec .flex-row>div.span4,
	.whats-new-sec .flex-row>div.span8 {
		flex: 0 0 100%;
		max-width: 100%;
	}
	.newscard:not(:first-child) {
		margin-top: 30px;
	}
	.whats-new-sec .flex-row>div.span8 {
		margin-top: 30px;
	}
	.newscard .newstag {
		font-size: 16px;
		line-height: 1.1;
	}
	.SectionHeader {
		/* font-size: 35px; */
		/* text-align: center; */
	}
	.info-iconbox {
		padding: 80px 50px 30px 20px;
	}
	.info-iconbox .iconlink {
		width: 40px;
		height: 40px;
		font-size: 20px;
	}
	.info-iconbox img {
		width: 40px;
		height: 40px;
	}
	.mt-40 {
		margin-top: 25px !important;
	}
	.newscard .newstag {
		width: 55px;
	}
	.friendsLogoBox .owl-carousel .owl-nav button.owl-prev, 
	.friendsLogoBox .owl-carousel .owl-nav button.owl-prev:hover {
		margin-top: -15px;
		opacity: 1;
	}
	
	.friendsLogoBox .owl-carousel .owl-nav button.owl-next, 
	.friendsLogoBox .owl-carousel .owl-nav button.owl-next:hover {
		margin-top: -15px;
		opacity: 1;
	}
	.newscard {
		padding-left: 55px;
	}
	
	.newscard .news-inner-wrap {
		padding: 15px 15px 15px 75px;
	}
	
	.newscard .news-inner-wrap img {
		width: 40px;
		height: 40px;
	}
	.newscard .news-inner-wrap h2 {
		font-size: 22px;
	}
	.learnMoreButton {
    font-size: 16px;
}

.learnMoreButton:before {
    padding: 2px 8px;
}
	blockquote, blockquote.pull-right {
		font-size: 20px;
	}
	.upcoming-event-sec .flex-row>div {
		width: 100%;
		margin-bottom: 30px !important;
		height: auto;
	}
	.event-list .sbm-event .sbm-e-head span {
		min-width: auto;
		color: #495761;
		font-size: 14px;
	}
	
	.event-list .sbm-event .sbm-e-head {
		justify-content: center;
	}
	
	.event-list .sbm-event .sbm-e-head span:first-child:after {
		position: relative;
		margin: 0 20px;
		color: #33383A;
	}
	
	.event-list .sbm-event .sbm-e-head span>i {
		margin-right: 5px;
	}
	
	.sbm-event h4 {
		color: #33383A;
		font-weight: 400;
		font-size: 16px;
	}
	
	.side-title-center {
		position: relative;
		border-bottom: none;
	}
	
	.side-title-center>img {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		opacity: 0.2;
		width: 70px;
		height: 70px;
		object-fit: contain;
	}
	
	.events h3 {
		font-size: 20px;
		color: #083372;
		padding: 30px 0;
	}
	.img-card {
		padding: 20px;
		max-width: 400px;
		margin: 0 auto 0px;
	}
	
	.img-card img {
		width:100%;
	}
	.img-card .img-holder {
		max-width: 100%;
		margin: 0 auto;
	}
	
	.img-card .img-holder img {
		width: 100%;
	}
	
	.sidebar .events {
		display: none;
	}
	.infoicon-sec .flex-row .col-3 {
		flex: 0 0 50%;
		max-width: 50%;
		-webkit-flex: 0 0 50%;
		margin-bottom: 30px;
	}
	.for-desktop {
		display: none;
	}
	.copyright-block {
	margin-top: 0px;
	}	
.xs767 { display: block !important; }
.xsHidden767, .xsHidden { display: none !important; }
.pd_70 { padding: 30px 0px; }
.header {
}

.header .navbar .nav li.dropdown .megaMenuSection .formframe input {font-size: 18px;height: 40px;padding: 0 15px;}

.header .nav-collapse {height: 100%;/* max-height: calc(100vh - 78px); */}
.header .navbar .nav li>a, .header .navbar .nav li>a, .header .navbar .nav li .dropdown-menu>li>a {font-size: 15px;padding: 10px 20px;line-height: 28px;}
.header .navbar .nav li.dropdown .memberSection li form a:last-child {margin-left: 0px;font-weight: normal;width: 100%;padding: 0px 0px 0 10px;line-height: 1.6;height: auto;margin: 0;}

.header .nav-collapse.collapse {left: 0;}

.sidebar { width: 100%; max-width: 100%; }


.captionBtnBox, .captionBtnFrame {width: 100%;max-width: 100%;}
.captionBtnFrame { padding: 20px 20px; }


.top-inner h1 { width: 50%; font-size: 22px; }
.captionFrame ul li h1 { text-align: center; }
.captionBtnBox ul li a .textBox h2 {
	font-size: 18px;
	font-weight: 400;
	margin-bottom: 5px;
}
.captionFrame {max-width: 100%;margin-left: 0;padding: 0 0 30px;}
.captionFrame h1 { margin-top: 0px; font-size: 32px; line-height: 36px; }
.header .navbar .container, .container { width: 100%; padding: 0px 15px; margin: 0 auto; }
.header .navbar-inner { position: relative; top: 0; margin: 0 auto; width: 100%; }
.header .navbar-brand { margin-left: 0px; }
.navbar .navbar-brand img { margin-left: 0px; }
.captionFrame h3 { font-size: 22px; }

.sliderFrame { margin: 55px 0 35px; }
.slider .owl-carousel .owl-dots { bottom: 20px; }

.navbar .navbar-brand {}
.header .navbar .nav>li:last-child>a {/* height: 60px; *//* line-height: 60px; *//* font-size: 18px; */}
.captionBtnBox ul li { margin-bottom: 15px; }


.header .navbar-brand {}
.header .navbar .btn-navbar.collapsed {padding: 30px 0;}
.header .navbar .btn-navbar {
	padding: 31px 0px 35px;
	margin: 0;
}


.headerSpace {}
.Highlight { padding: 15px 15px; margin-bottom: 20px; }
.section-HeaderText {
	font-size: 25px;
	margin-bottom: 30px;
}
.BlackLine:before, .WhiteLine:before {
	width: 150px;
	bottom: -10px;
}
.friendsSliderBox.friendsSliderBox-mobile {
	display: block;
}
.friendsSliderBox .HeaderText {margin-bottom: 15px;color: #6C6C6C;font-weight: 600;font-size: 14px;margin-top: 0;}
.friendsSliderBox .owl-carousel ul li {
	width: auto;
	padding: 0;
}
.friendsSliderBox.friendsSliderBox-mobile .owl-nav {
	margin: 0;
}
.friendsSliderBox {
	margin-bottom: 20px;
}
.eventBoxFrame {
	padding: 30px 10px 50px 10px;
	margin-bottom: 0px;
}
.eventBox .LAFJButton {
	display: none;
}
.eventBoxFrame button {
	left: 0;
	margin: 0 auto;
}
.event-slider {
	margin-bottom: 30px;
}
.friendsLogoBox.pd_70,
.become-member-section.pd_70 {
	padding-bottom: 50px;
}
.member-boxleft {
	width: 100%;
}
.member-boxright {
	padding-left: 0;
	margin-top: 20px;
}
.become-member-section .member-left {
	text-align: center;
}
.become-member-section .member-right {
	padding: 24px 0 0 0;
	margin: 40px 0 0 0;
	text-align: center;
}
.member-boxleft h3, .member-boxright h3 { font-size: 22px; }
.member-right .member-boxright {
	width: 280px; margin: 0;
}
.member-right a.LAFJButton {
	width: 100%;
	display: block;
}

.footer .footer-links, .footer .contact-links { float: left; }
.footer-links h3, .contact-links h3 {
	margin: 0 0 10px 0;
}
.footer .footer-info .foot-logo:after {margin: 15px auto 14px;}
ul.follow-us {
	margin-top: 20px; position: static;
}
ul.follow-us li:first-child { display: none; }
.footer-links ul li::before {
	display: none;
}
.footer-links ul li {
	margin-bottom: 10px;
	padding-left: 0;
}

.friendsSliderBox .owl-carousel ul li a { padding: 0 25px; }
/*******************/
.TitleText {font-size: 30px;}
.quicklink-mobile, .event-mobile {
	padding: 10px 15px;
	margin: 15px 15px 0;
}
.DiamondBullets ul li a {font-size: 18px;padding: 15px 40px 15px 15px;}
.content-info {
	padding: 20px 15px;
}
.HeaderText {
	font-size: 24px;
}
.HeaderTextSmall {
	line-height: 1.3;
}
.Highlight {
	padding: 30px 30px;
	margin: 30px 0;
}
.Highlight p {
	/* margin-bottom: 30px; */
}

.membership-headlinebox { display: none; }
.BlackBorder {
	padding: 14px 18px;
}
.membership-headlinebox h5 { font-size: 20px; }
.inner-page-content .sidebar {padding: 15px;background: #fff;}
.sponsors-boxtwo {
	margin: 30px 0 20px 0;
	padding: 0;
}
.sponsors-boxtwo img { width: 100%; }
.sponsors-boxthree { width: 100%; }
.captionBtnBox.captionBtnBox-mb { display: block; }
.eventBoxFrame .HeaderTextSmall {
	margin-bottom: 35px;
}
.footer-info a > img {
	width: 280px;
}
.events {
    background: #F6F1E4;
    padding: 15px 15px;
    margin-bottom: 5px;
}
.sponsors-box { display: none; }
.events .friendsLogoBox {
	background: #fff;
}
.forgot-mb {
	display: block;
	font-size: 14px;
font-weight: 500;
color: #535353;
text-decoration: underline;
margin-top: 10px;
}
.bannerInner {
	overflow: hidden;
	min-height: 200px;
	padding-bottom: 300px;
}
.bannerInner img {
	width: auto;
	height: 100%;
	max-width: inherit;
}
.textBox p {
	color: #fff;
	margin: 0;
	line-height: 18px; font-size: 14px;
}
.friendsLogoBox  { background: #fff; }
.captionBtnBox ul li a .textBox { max-width: 100%; }
.BlackBorder, .primary-btnmb {
	margin: 0px auto;
	width: 250px;
	display: block;
}
.events .friendsLogoBox {
	display: block;
}
.foot-logo-wrap {
    flex-flow: column;
    align-items: center;
}
.footer .footer-info ul.social-list {
	margin-top: 30px;
}
.footer .row.d-flex-wrap>div.col1 {
	flex: 0 0 100%;
	max-width: 100%;
	padding-right: 15px;
	margin: 0;
}
.footer>.container {
	max-width: 450px;
}
.copyright ul li:first-child {
	width: 100%;
}
.copyright ul li:first-child:after {
	display: none;
}
.copyright ul {
	font-size: 14px;
	text-align: center;
	justify-content: center;
}
.footer .footer-links {
	padding-left: 0;
}
.footer .foot-icon {
	left: 15px;
}
.flex-sec .flex-row>.col {
	-webkit-flex: 0 0 100%;
	flex: 0 0 100%;
	max-width: 100%;
	margin: 15px 0;
}
.flex-sec .flex-row {
	flex-wrap: wrap;
}
.flex-sec .container .eventbox-item-in {
	padding: 20px 15px;
}
.eventbox-item ul li {
	font-size: 12px;
}
.eventbox-item p {
	font-size: 18px;
}
.eventbox-item ul li {
	padding: 0 15px;
}
.header .navbar .nav li.dropdown .megaMenuSection h2 {
	font-size: 28px;
}
.flex-sec .flex-row .col-4 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-top: 30px;
}
.breadcrumd-list ul li:not(:last-child):after {
	margin: 0 10px;
}
.sponsors-sec p {
	padding: 0;
}
.follow-us-btn {
    padding: 7px 12px;
} 
.fb-wid-logo-wrap img {
    flex: 0 0 40px;
    -webkit-flex: 0 0 40px;
    max-width: 40px;
}
.fb-wid-logo-wrap .fb-right-wrap {
	flex: 0 0 calc(100% - 40px);
    max-width: calc(100% - 40px);
}
.footer-links-wrap {
    border-style: none !important;
    border-style: solid none none none;
    padding: 0 20px;
    padding-top: 30px;
}
.footer-links-wrap .footer-form {
	flex: 0 0 100%;
    -webkit-flex: 0 0 100%;
}
.friendsSliderBox .owl-item li {
    padding-left: 60px;
    padding-right: 60px;
}
.HeaderTextFoundation,
.ColumnHeader,
.event-head h4 {
    font-size: 18px;
}
.SubHeading {
	font-size: 20px;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxtwo p {
	padding-right: 0;
}
.hbf-nav-sec h2 {
    font-size: 32px;
}
.heroBanner {
    padding: 50px 0 30px;
}

.left-img-card {
    display: block;
}

.left-img-card .li-img, .left-img-card .li-content {
    flex: 0 0 100%;
    max-width: 100%;
}

.numberInfo {
    margin: 30px 0 0;
}

.footer-list-links {
    display: block;
}
.img-left-card {
    display: block;
    padding: 30px;
}

.img-left-card .ilc-img, .img-left-card .ilc-content {
    flex: 0 0 100%;
    max-width: 100%;
}

.img-left-card .ilc-content .pc-content-inner {
    padding: 0;
    border-style: none;
}
.quicklink {
	margin-top: 30px;
}
.tab-wrap .content-wrap, .tab-wrap .img-wrap {
    flex: 0 0 100%;
    max-width: 100%;
}

.tab-wrap {
    display: block;
}

.tab-wrap .content-wrap {
    padding: 0;
    margin-top: 30px;
    margin-bottom: 30px;
}
.img-left-card .ilc-content {
    padding: 25px 0 0;
}
.member-sec .span6 {
	flex: 0 0 100%;
	max-width: 100%;
}
.member-card ul li:nth-child(2) img {
    height: 380px;
    object-fit: cover;
}
.member-card .owl-dots {
	top: 398px;
}

.member-card .owl-carousel .owl-nav button.owl-prev, .member-card .owl-carousel .owl-nav button.owl-prev:hover,  .member-card .owl-carousel .owl-nav button.owl-next, .member-card .owl-carousel .owl-nav button.owl-next:hover {
    top: 406px;
}
.news-sec .row.d-flex-wrap .span3 {
	flex: 0 0 100%;
	max-width: 100%;
}
.important-dates-sec .span4 {
	/* display: none; */
	flex: 0 0 100%;
	width: 100%;
	padding: 0 0 !important;
}
.important-dates-sec .span8 {
    margin: 0;
    flex: 0 0 100%;
    max-width: 100%;
}
.quick-links-sec {
	padding-left: 15px;
	padding-right: 15px;
}
.member-sec .span6:not(:first-child) {
    margin-top: 30px;
}
.circle-wrap {
    margin: 0;
    bottom: -20px;
}
.quick-links-wrapper li {
	padding: 30px 20px;
	flex: 0 0 100%;
	max-width: 100%;
	border-style: solid none;
}
.quick-links-wrapper li:last-child {
	border-style: none;
	padding-bottom: 10px;
}
.d-flex .left-icon-title {
	justify-content: center;
}
.important-dates-sec .HeaderText {
    flex: 0 0 auto;
    width: auto;
}
.date-card .dc-date span {
    font-size: 19px;
    font-family: 'Montserrat';
    font-weight: 900;
}

.date-card .dc-date {
    font-size: 14px;
    flex: 0 0 60px;
    max-width: 60px;
    text-align: center;
    margin: 0;
}

.date-card {
    display: flex;
    padding: 15px;
}

.date-card .dc-content {
    margin: 0;
    padding: 0 0 0 20px;
}
.date-card p.InfoText {
	margin-top: 20px;
	margin-left: -60px;
	font-size: 13px;
}
.important-dates-sec .btn-wrap {
	justify-content: center;
}

    .footer .footer-links, .footer .contact-links {
        width: 100%;
        flex: 0 0 100%;
        max-width: 100%;
	}
	.footer .container {
		padding: 0 15px;
	}
	.member-card ul, .member-card ul li {
    text-align: center;
    justify-content: center;
}
.news-sec .row.d-flex-wrap .span3:last-child {
	margin-bottom: 0;
}
.bannerInner .breadcrumb {
    justify-content: center;
    text-align: center;
}

.bannerInner .TitleText, .bannerInner p {
    text-align: center;
}

.bannerInner .btn-wrap {
    justify-content: center;
}
}
/* max767px */

 @media only screen and (max-width:600px) {
	.infoicon-sec .flex-row .col-3 {
		flex: 0 0 100%;
		max-width: 100%;
		-webkit-flex: 0 0 100%;
		margin-bottom: 30px;
	}
	
	.footer .row.d-flex-wrap>div.col2, .footer .row.d-flex-wrap>div.col3 {
		flex: 0 0 100%;
		max-width: 100%;
		padding-left: 15px;
		padding-right: 15px;
	}

	.date-card-wrap .date-card-col {
		flex: 0 0 100%;
		max-width: 100%;
	}

 }
 @media only screen and (max-width:375px) {
	.friendsSliderBox .owl-carousel .owl-item ul li {
		max-width: 60%;
	}
.HeaderTextSmall {margin-bottom: 0px;}
a.HeaderTextSmall, h4 a { font-size: 14px; }
p, .BodyText {font-size: 16px;color: #434343;line-height: 1.4;}
.bannerInner .HeaderTextSmall { font-size: 16px; }

.sidebar { width: 100%; padding: 0; }
.contentdivFrame { padding: 0px 0px 20px; }

.header .navbar .container, .container { padding: 0px 15px; }
.navbar .navbar-brand img.xsVisible { left: 0px; }
.slider .owl-carousel .owl-dots { bottom: 15px; }
.captionBtnBox ul li { margin-bottom: 10px; }


.sliderFrame { padding: 0px; }
.BodyTextLarge { font-size: 14px; }

.top-header { background: #5b9cde; }
.top-inner h1 { width: 100%; }
.footer-links ul li a, .contact-links ul li a, .contact-links ul li span, .footer-links.contact-links ul li {
	font-size: 14px;
}
.contact-links ul li i {
	font-size: 16px;
	width: 20px;
}
.header .navbar .nav li.dropdown .memberSection li a.LAFJButton {
	padding: 15px 20px;
}
}
@media only screen and (max-width:320px) {
.getinvolved-left {
	width: 120px;
}
.getinvolved-right {
	width: calc(100% - 125px);
}
}
