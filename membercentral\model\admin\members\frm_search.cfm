<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfset local.targetOrigin = "#(application.objPlatform.isRequestSecure())?'https':'http'#://#(application.MCEnvironment eq "production")?arguments.event.getValue('mc_siteInfo.mainhostname'):application.objPlatform.getCurrentHostname()#">

<cfsavecontent variable="local.dataJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/memberAdmin.js#local.assetCachingKey#"></script>
	<script language="javascript">
		var #toScript(arguments.event.getValue('mc_siteinfo.orgid'),"mcma_orgid")#
		var #toScript(session.cfcuser.memberdata.memberID,"mcma_actormemberid")#

		function hideAlert() { $('##MAerr').html('').hide(); };
		function showAlert(msg) { $('##MAerr').html(msg).show(); };
		function closeBox() {
			MCModalUtils.hideModal();
		}
		function fnCreateMember() {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Add Member',
				iframe: true,
				contenturl: '#local.editInfoURL#&memberID=0',
				strmodalbody : {
					classlist: 'p-2'
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'fnClickSaveMember',
					extrabuttonlabel: 'Save Information',
				}
			});
		}
		function fnClickSaveMember() {
			let targetMessage = { messagetype: 'MCModalEvent', eventname: 'savemember' };
			let targetOrigin = '#local.targetOrigin#';
			$('##MCModalBodyIframe')[0].contentWindow.postMessage(targetMessage,targetOrigin);
		}
		function doMSearch() {
			$('##frmMember button[name="btnSubmit"]').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Please Wait...').prop('disabled',true);
			if (validateMSearch()) document.forms['frmMember'].submit();
			else {
				$('##frmMember button[name="btnSubmit"]').html('Find Members').prop('disabled',false);
				return false;
			}
		}
		function checkFilledFields(scope) {
			inputs = scope.find('input[type="text"], select').not('.skipfillcheck');  
			filled = inputs.filter(function(){
				return $.trim($(this).val()).length > 0;
			});
			return filled.length;
		}
		$(function() {
			$('##frmMember input').keyup(function(event) {
				if (event.keyCode == 13) doMSearch();
			});
			<cfif arguments.event.getValue('err',0) is 1>
				showAlert('There are no results based on your search criteria.');
			</cfif>
			mca_setupCalendarIcons('frmMember');
			mca_setupSelect2();
			mca_loadlastmemviewed();
			<cfloop query="local.qryGetClassifications">
				<cfif val(local.qryGetClassifications.allowSearch)>
					mca_setupSelect2ByID('mg_gid_#local.qryGetClassifications.groupSetID#');
				</cfif>
			</cfloop>
			<cfif val(arguments.event.getValue('addMember',0))>
				fnCreateMember();
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.dataJS#">

<cfoutput>
<h4>Find Members</h4>

<cfif checkRights(arguments.event,'AddAll') OR checkRights(arguments.event,'AddOrg')>
	<div class="toolButtonBar">
		<div><a href="javascript:fnCreateMember();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Add a new member record."><i class="fa-regular fa-user-plus"></i> Add Member</a></div>
	</div>
</cfif>

<div class="row pt-3">
	<div class="col-md-8">
		<cfif ArrayLen(local.xmlFields.xmlRoot.xmlChildren) is 0>
			<div class="alert alert-danger" role="alert">Searching is not available at this time.</div>
		<cfelse>
			<cfset local.showMatchingOption = false>

			<form name="frmMember" id="frmMember" method="post" action="#local.listURL#" onSubmit="return false">
				<div id="MAerr" class="alert alert-danger mb-3" role="alert" style="display:none;"></div>

				<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
					<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
						<cfcase value="TEXTBOX">
							<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>
								<div class="form-row">
									<div class="col">
										<div class="form-label-group mb-2">
											<select name="#local.thisfield.xmlattributes.fieldCode#_radius" id="#local.thisfield.xmlattributes.fieldCode#_radius" class="custom-select">
												<cfloop list="5,10,25,50,100" index="local.thisrad">
													<option value="#local.thisrad#" <cfif local.thisrad eq 10>selected</cfif>>within #local.thisrad# miles</option>
												</cfloop>
											</select>
											<label for="#local.thisfield.xmlattributes.fieldCode#_radius">
												#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#
											</label>
										</div>
									</div>
									<div class="col">
										<div class="form-label-group mb-2">
											<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="form-control">
											<label for="#local.thisfield.xmlattributes.fieldCode#">
												of postal code
											</label>
										</div>
									</div>
								</div>
							<cfelse>
								<div class="form-label-group mb-2">
									<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="form-control">
									<cfif local.thisfield.xmlAttributes.dataTypeCode eq "STRING">
										<cfset local.showMatchingOption = true>
									</cfif>
									<label for="#local.thisfield.xmlattributes.fieldCode#">
										#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
										<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
											<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
										</cfif>
									</label>
								</div>
							</cfif>
						</cfcase>
						<cfcase value="RADIO,SELECT,CHECKBOX">
							<div class="form-label-group mb-2">
								<cfif ReFindNoCase('mat?_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
									<cfset local.qryStates = application.objMember.getStates(local.rc.mc_siteinfo.orgID)>
									<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
										<option value=""></option>
										<cfoutput query="local.qryStates" group="countryID">
											<optgroup label="#local.qryStates.country#">
											<cfoutput>
												<option value="#local.qryStates.statecode#">#local.qryStates.stateName# &nbsp;</option>
											</cfoutput>
											</optgroup>
										</cfoutput>
									</select>
								<cfelseif local.thisfield.xmlattributes.fieldCode eq "m_recordtypeid">
									<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
										<option value=""></option>
										<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
											<option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOpt.xmlAttributes.columnValueString#</option>
										</cfloop>
									</select>
								<cfelseif local.thisfield.xmlattributes.fieldCode eq "m_membertypeid">
									<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
										<option value=""></option>
										<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
											<option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOpt.xmlAttributes.columnValueString#</option>
										</cfloop>
									</select>
								<cfelseif local.thisfield.xmlattributes.fieldCode eq "m_status">
									<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
										<option value=""></option>
										<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
											<option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOpt.xmlAttributes.columnValueString#</option>
										</cfloop>
									</select>														
								<cfelse>
									<cfset local.multiSelect = local.thisfield.xmlAttributes.dbObjectAlias EQ 'vwmd' AND local.thisfield.xmlAttributes.dataTypeCode NEQ 'BIT'>
									<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" <cfif local.multiSelect>class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Select options"<cfelse>class="custom-select"</cfif>>
										<cfif NOT local.multiSelect>
											<option value=""></option>
										</cfif>
										<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt">
											<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
											<cfcase value="STRING">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
												<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueString>
											</cfcase>
											<cfcase value="DECIMAL2">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
												<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueDecimal2>
											</cfcase>
											<cfcase value="INTEGER">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
												<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueInteger>
											</cfcase>
											<cfcase value="DATE">
												<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
												<cfset local.thisOptColDisplay = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
											</cfcase>
											<cfcase value="BIT">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
												<cfset local.thisOptColDisplay = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
											</cfcase>
											<cfdefaultcase>
												<cfset local.thisOptColValue = "">
												<cfset local.thisOptColDisplay = "">
											</cfdefaultcase>
											</cfswitch>
											<option value="#local.thisfield.xmlAttributes.dbObjectAlias EQ 'vwmd' ? local.thisOpt.xmlAttributes.valueID : local.thisOptColValue#">#local.thisOptColDisplay#</option>
										</cfloop>
									</select>
								</cfif>
								<label for="#local.thisfield.xmlattributes.fieldCode#">
									#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
									<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
										<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
									</cfif>
								</label>
							</div>
						</cfcase>
						<cfcase value="DATE">
							<div class="form-label-group mb-2">
								<div class="input-group dateFieldHolder">
									<input type="text"  name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" class="form-control dateControl">
									<div class="input-group-append">
										<span class="input-group-text cursor-pointer calendar-button" data-target="#local.thisfield.xmlattributes.fieldCode#"><i class="fa-solid fa-calendar"></i></span>
										<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('#local.thisfield.xmlattributes.fieldCode#');"><i class="fa-solid fa-circle-xmark"></i></a></span>
									</div>
									<label for="#local.thisfield.xmlattributes.fieldCode#">
										#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
										<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
											<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
										</cfif>
									</label>
								</div>
							</div>
							<cfsavecontent variable="local.datejs">
								<script language="javascript">
									$(function() {
										mca_setupDatePickerField('#local.thisfield.xmlattributes.fieldCode#');
										mca_setupCalendarIcons('frmLocator');
									});
								</script>
							</cfsavecontent>
							<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
						</cfcase>
					</cfswitch>
				</cfloop>

				<cfloop query="local.qryGetClassifications">
					<cfif val(local.qryGetClassifications.allowSearch)>
						<cfset local.qryGetGroupSetGroup = local.objMemberSettingsAdmin.getGroupSetGroup(local.qryGetClassifications.groupSetID)>
						<div class="form-label-group mb-2">
							<div class="input-group flex-nowrap">
								<select class="form-control form-control-sm" name="mg_gid_#local.qryGetClassifications.groupSetID#" id="mg_gid_#local.qryGetClassifications.groupSetID#" multiple="true" data-toggle="custom-select2" placeholder="Select options">
									<cfloop query="local.qryGetGroupSetGroup">
										<option value="#local.qryGetGroupSetGroup.groupsetGroupID#">#local.qryGetGroupSetGroup.groupLabel#</option>
									</cfloop>
								</select>
								<label for="mg_gid_#local.qryGetClassifications.groupSetID#">
									<cfif len(trim(local.qryGetClassifications.name))>#local.qryGetClassifications.name#<cfelse>#local.qryGetClassifications.groupSetName#</cfif>
								</label>
							</div>
						</div>
					</cfif>
				</cfloop>
				<div class="form-group row pt-2">
					<cfif local.showMatchingOption>
						<div class="col-8">
							<label for="fs_match">Find matches
								<select name="fs_match" id="fs_match" class="form-control form-control-sm d-inline w-auto mx-2 skipfillcheck">
									<option value="s" <cfif local.qrySettings.defaultSearchOption EQ 's'>selected</cfif>>beginning with</option>
									<option value="c" <cfif local.qrySettings.defaultSearchOption EQ 'c'>selected</cfif>>containing</option>
									<option value="e" <cfif local.qrySettings.defaultSearchOption EQ 'e'>selected</cfif>>exactly matching</option>
								</select>
								the terms I entered.
							</label>
						</div>
					</cfif>
					<div class="<cfif local.showMatchingOption>col-4<cfelse>col-12</cfif> text-right">
						<button type="button" name="btnSubmit" class="btn btn-sm btn-primary" onclick="doMSearch()">Find Members</button>
					</div>
				</div>
				<cfif local.showReqFlag>
					<div class="small text-dim"><i>* required field</i></div>
				</cfif>
			</form>
		</cfif>
	</div>
	<div class="col-md-4" id="divLastMemVisit"></div>
</div>

<script id="mca_lastmemvisit_template" type="text/x-handlebars-template">
	<div class="card card-box mb-5">
		<div class="card-header bg-light">
			<div class="card-header--title">
				<b class="text-uppercase">Last Members Viewed</b>
			</div>
		</div>
		<ul class="list-group list-group-flush">
			{{##each members}}
				<li class="list-group-item px-2 py-1">
					<div class="align-box-row">
						<div class="mr-4 d-none d-xl-block">
							<a href="#local.editURL#&memberID={{memberid}}" class="m-0" data-toggle="tooltip" title="View member record">
								<div class="dot-badge"></div>
								<div class="rounded">
									<img class="rounded" style="max-width:50px;" src="{{##if hasmemberphotothumb}}/memberphotosth/{{membernumber}}.jpg?cb=#getTickCount()#{{else}}/assets/common/images/directory/default.jpg{{/if}}" alt="">
								</div>
							</a>
						</div>
						<div class="flex-grow-1 line-height-sm">
							<a href="#local.editURL#&memberID={{memberid}}">{{membername}} ({{membernumber}})</a>
							<div class="text-black-50 mt-1 mb-2">{{company}}&nbsp;</div>
						</div>
					</div>
				</li>
			{{/each}}
		</ul>
	</div>
</script>
</cfoutput>

<cfsavecontent variable="local.jsFunc">
	<cfoutput>
	<script language="javascript">
		function validateMSearch() {
			hideAlert();
			var _CF_this = document.forms['frmMember'];
			var locateErr = '';
			#local.jsValidation#

			if(locateErr.length == 0 && !checkFilledFields($('##frmMember'))) 
				locateErr = 'Find Members by selecting one or more filters.';

			if (locateErr.length > 0) {
				showAlert(locateErr);
				return false;
			}

			return true;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.jsFunc#">