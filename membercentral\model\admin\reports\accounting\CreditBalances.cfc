<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// call common report controller
			reportController(event=arguments.event);

			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>		
	
	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "">

		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfsavecontent variable="local.js">
				<cfoutput>
					<script language="javascript">
						function forceRequired() {
							var validResult = true;
							var reportType = $("input[name='frmReportType']:checked").val();
							if (reportType == 'postedBatch' && $('##frmDateTo').val() == '') {
								validResult = false;
								rptShowAlert('Select the On or Before Date.');
							}

							return validResult;
						}
						function onChangeReportType(option) {
							$('.filterFields').addClass('d-none');
							$('##' + option + '-filterFields').removeClass('d-none');
							var isRealTime = (option == 'realTime');
							if(isRealTime){
								if ($('input[name=rolldate]:checked').val() == '1') {
									$("##rolldate0").prop("checked", true);
									rollDateRdo(0);
								}
								$("##frmDisplay").val('summary').trigger("change");
							}
							$('##divStepRollDates').toggleClass('d-none', isRealTime);
						}
						function onChangeDisplayOption(option){
							if(option.length) {
								$('button##btnReportBarscreen, button##btnReportBarpdf').toggleClass('d-none', option == 'detail');
							}
						}
						$(function() {
							mca_setupDatePickerField('frmDateTo');
							mca_setupCalendarIcons('frmReport');
							mca_setupSelect2();

							$("input[name='frmReportType']").change(function() {
								onChangeReportType($(this).val());
							}).filter(":checked").trigger("change");

							$("##frmDisplay").change(function() {
								onChangeDisplayOption($(this).val());
							}).trigger("change");
						});
					</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
					
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfset local.qryAvailableMerchantProfiles = getAvailableMerchantProfiles(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>

					<cfset local.frmReportType = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmreporttype/text())")>
					<cfset local.frmProfileIDList = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmprofileidlist/text())")>
					<cfset local.frmDateTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmdateto/text())")>
					<cfset local.frmDisplay = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmdisplay/text())")>
					<cfif not len(local.frmReportType)>
						<cfset local.frmReportType = "realTime">
					</cfif>
					<cfif not len(local.frmDateTo)>
						<cfset local.frmDateTo = "#dateformat(dateadd("d",-1,"#month(now())#/1/#year(now())#"),"m/d/yyyy")#">
					</cfif>
					<cfif not len(local.frmDisplay)>
						<cfset local.frmDisplay = "summary">
					</cfif>

					<cfform name="frmReport" id="frmReport" method="post">
					<input type="hidden" name="reportAction" id="reportAction" value="">
					<div class="mb-5 stepDIV">
						<h5>Report Mode</h5>
						<div class="form-group mt-2">
							<div class="form-check">
								<input class="form-check-input" type="radio" name="frmReportType" id="realTimeBalance" value="realTime"<cfif local.frmReportType eq "realTime"> checked</cfif>>
								<label class="form-check-label" for="realTimeBalance">Report on Real-Time Credit Balances</label>
								<div class="form-text text-dark">Shows all current credit balances including unallocated payments on open and non-posted batches.</div>
								<div id="realTime-filterFields" class="filterFields mt-2">
									<div class="form-group row d-flex align-items-center">
										<label for="frmProfileID1" class="col-2 col-form-label">Limit to these Pay Profiles:</label>
										<div class="col">
											<select name="frmProfileID1" id="frmProfileID1" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="Any Payment Profile">
												<optgroup label="#local.qryAvailableMerchantProfiles.siteName#">
												<cfset local.mpsiteID = local.qryAvailableMerchantProfiles.siteID>
												<cfloop query="#local.qryAvailableMerchantProfiles#">
													<cfif local.mpsiteID neq local.qryAvailableMerchantProfiles.siteID>
														</optgroup><optgroup label="#local.qryAvailableMerchantProfiles.siteName#">
														<cfset local.mpsiteID = local.qryAvailableMerchantProfiles.siteID>
													</cfif>
													<option value="#local.qryAvailableMerchantProfiles.profileID#"<cfif local.frmReportType eq "realTime" and listFind(local.frmProfileIDList,local.qryAvailableMerchantProfiles.profileID)> selected</cfif>>#local.qryAvailableMerchantProfiles.profileName#</option>
												</cfloop>
												</optgroup>
											</select>
										</div>
									</div>
								</div>
							</div>
							<div class="form-check mt-3">
								<input class="form-check-input" type="radio" name="frmReportType" id="postedBatchDates" value="postedBatch"<cfif local.frmReportType eq "postedBatch"> checked</cfif>>
								<label class="form-check-label" for="postedBatchDates">Report on Credit Balances Based on Posted Batch Dates</label>
								<div class="form-text text-dark">Shows credit balances based on posted batches with deposit dates on or before the specified date.</div>
								<div id="postedBatch-filterFields" class="filterFields mt-2 d-none">
									<div class="form-group row d-flex align-items-center">
										<label for="frmProfileID2" class="col-2 col-form-label">Limit to these Pay Profiles:</label>
										<div class="col">
											<select name="frmProfileID2" id="frmProfileID2" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="Any Payment Profile">
												<optgroup label="#local.qryAvailableMerchantProfiles.siteName#">
												<cfset local.mpsiteID = local.qryAvailableMerchantProfiles.siteID>
												<cfloop query="#local.qryAvailableMerchantProfiles#">
													<cfif local.mpsiteID neq local.qryAvailableMerchantProfiles.siteID>
														</optgroup><optgroup label="#local.qryAvailableMerchantProfiles.siteName#">
														<cfset local.mpsiteID = local.qryAvailableMerchantProfiles.siteID>
													</cfif>
													<option value="#local.qryAvailableMerchantProfiles.profileID#"<cfif local.frmReportType eq "postedBatch" and listFind(local.frmProfileIDList,local.qryAvailableMerchantProfiles.profileID)> selected</cfif>>#local.qryAvailableMerchantProfiles.profileName#</option>
												</cfloop>
												</optgroup>
											</select>
										</div>
									</div>
									<div class="form-group row">
										<label for="frmDateTo" class="col-2 col-form-label">Batch posted on or before:</label>
										<div class="col-3">
											<div class="input-group input-group-sm">
												<input type="text" name="frmDateTo" id="frmDateTo" class="form-control form-control-sm dateControl rolldate" value="#local.frmDateTo#" mcrdtxt="On or Before" placeholder="On or Before">
												<div class="input-group-append">
													<span class="input-group-text"><i class="fa-solid fa-calendar"></i></span>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group row">
										<label for="frmDisplay" class="col-2 col-form-label">Display Setting:</label>
										<div class="col-3">
											<select name="frmDisplay" id="frmDisplay" class="form-control form-control-sm">
												<option value="summary" <cfif local.frmDisplay eq "summary">selected</cfif>>Sum by Payment Profile</option>
												<option value="detail" <cfif local.frmDisplay eq "detail">selected</cfif>>Individual Payment Detail</option>
											</select>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					#showStepRollingDates(event=arguments.event)#
					#showStepFieldsets(event=arguments.event)#
					#showButtonBar(event=arguments.event,validateFunction='forceRequired')#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(local.mc_siteInfo.orgcode) & "/memberphotosth/">

			<cfset local.frmReportType = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmreporttype/text())")>
			<cfset local.frmProfileIDList = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmprofileidlist/text())")>
			<cfset local.frmDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdateto/text())")>
			<cfset local.frmDisplay = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdisplay/text())")>
			<cfset local.frmShowPhotos = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@img)")>
			<cfset local.frmShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mn)")>
			<cfset local.frmShowCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mc)")>
			<cfset local.strReport = generateData(qryReportInfo=arguments.qryReportInfo, reportAction=arguments.reportAction, siteCode=arguments.siteCode)>

			<cfif local.strReport.qryBalances.recordcount>
				<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=local.strReport.qryBalances.mc_outputFieldsXML)>
				<!--- remove fields from qryOutputFields that are handled manually --->
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					select *
					from [local].qryOutputFields
					where fieldcodeSect NOT IN ('mc','m','ma','mat')
					or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated')
				</cfquery>
			</cfif>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>

				<cfif local.strReport.qryBalances.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfoutput>
						<cfif structKeyExists(local.strReport, "qryNonPostedBatches") and val(local.strReport.qryNonPostedBatches.batchCount) gt 0>
							<cfoutput>
								<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show" role="alert">
									<span class="font-size-lg d-block d-40 mr-2 text-center">
										<i class="fa-solid fa-triangle-exclamation"></i>
									</span>
									<span>
										There are #local.strReport.qryNonPostedBatches.batchCount# non-posted batches in this reporting period. Transactions on these batches will not be considered on this report.
									</span>
									<button type="button" class="close" data-dismiss="alert" aria-label="Close">
										<span aria-hidden="true">&times;</span>
									</button>
								</div>
							</cfoutput>
						</cfif>

						<cfif local.strReport.qryBalances.recordcount is 0>
							<cfoutput><div class="text-dark">No credit balances to report.</div></cfoutput>
						<cfelse>
							<cfoutput>
							<div class="card card-box mb-3">
								<div class="card-body p-2">
									<table class="table table-borderless table-sm">
										<thead>
											<tr>
												<th class="text-left"<cfif local.frmShowPhotos is 1> colspan="2"</cfif><cfif arguments.reportAction eq "pdf"> width="40%"</cfif>>Member</th>
												<th class="text-left"<cfif arguments.reportAction eq "pdf"> width="60%"</cfif>>Credit Balance by Payment Profile</th>
											</tr>
										</thead>
										<tbody>
											<cfoutput query="local.strReport.qryBalances" group="memberID">
												<tr valign="top">
													<cfif local.frmShowPhotos is 1>
														<td class="w-5 align-top">
															<cfif val(local.strReport.qryBalances.memberID) gt 0>
																<cfif local.strReport.qryBalances.hasMemberPhotoThumb is 1>
																	<cfif arguments.reportAction eq "screen">
																		<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.strReport.qryBalances.MemberNumber)#.jpg">
																	<cfelse>
																		<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.strReport.qryBalances.MemberNumber)#.jpg">
																	</cfif>
																<cfelse>
																	<cfif arguments.reportAction eq "screen">
																		<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
																	<cfelse>
																		<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
																	</cfif>
																</cfif>
															</cfif>
														</td>
													</cfif>
													<td class="align-top">
														<cfif val(local.strReport.qryBalances.memberID) gt 0>
															<cfif arguments.reportAction eq "screen" and len(local.memberLink)>
																<a href="#local.memberLink#&memberid=#local.strReport.qryBalances.memberID#&tab=transactions" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.strReport.qryBalances["Extended MemberNumber"]#<cfelse>#local.strReport.qryBalances["Extended Name"]#</cfif></a><br/>
															<cfelse>
																<div class="font-weight-bold"><cfif local.frmShowMemberNumber is 1>#local.strReport.qryBalances["Extended MemberNumber"]#<cfelse>#local.strReport.qryBalances["Extended Name"]#</cfif></div>
															</cfif>
															<cfif local.frmShowCompany is 1 AND len(local.strReport.qryBalances.memberCompany)>#local.strReport.qryBalances.memberCompany#<br/></cfif>
															<cfloop query="local.qryOutputFields">
																<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
																	<cfset local.AddrToShow = local.strReport.qryBalances[local.qryOutputFields.dbfield][local.strReport.qryBalances.currentrow]>
																	<cfloop condition="Find(', , ',local.AddrToShow)">
																		<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
																	</cfloop>
																	<cfif len(local.AddrToShow)>
																		#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
																	</cfif>
																</cfif>
															</cfloop>
															<cfloop query="local.qryOutputFieldsForLoop">
																<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
																	#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.strReport.qryBalances[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryBalances.currentrow])#<br/>
																<cfelseif len(local.strReport.qryBalances[local.qryOutputFieldsForLoop.fieldLabel][local.strReport.qryBalances.currentrow])>
																	<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
																		#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.strReport.qryBalances[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryBalances.currentrow], "m/d/yyyy")#<br/>
																	<cfelse>
																		#local.qryOutputFieldsForLoop.fieldlabel#: #local.strReport.qryBalances[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryBalances.currentrow]#<br/>
																	</cfif>
																</cfif>
															</cfloop>
														<cfelse>
															<strong>#local.strReport.qryBalances.memberName#</strong>
														</cfif>
													</td>
													<td class="align-top">
														<table class="table table-sm w-100">
															<cfoutput>
																<tr>
																	<td class="w-25 text-right pr-2">#dollarFormat(local.strReport.qryBalances.creditAmount)#</td>
																	<td>#local.strReport.qryBalances.profileName#</td>
																</tr>
															</cfoutput>
															<tr>
																<td class="font-weight-bold w-25 text-right pr-2">#dollarFormat(local.strReport.qryBalances.totalcreditAmount)#</td>
																<td class="font-weight-bold">Total Credit Balance</td>
															</tr>
														</table>
													</td>
												</tr>
											</cfoutput>
										</tbody>
									</table>
								</div>
							</div>
							</cfoutput>
						</cfif>
					</cfoutput>
				</cfif>
				<cfoutput>
					#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
					#showRawSQL(reportAction=arguments.reportAction, qryName="local.strReport.qryBalances", strQryResult=local.strReport.qryBalancesResult)#
					</div>
				</cfoutput>
			</cfsavecontent>

		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>
	
		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.frmReportType = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmreporttype/text())")>
		<cfset local.frmDisplay = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdisplay/text())")>

		<cftry>
			<cfset local.strReport = generateData(qryReportInfo=arguments.qryReportInfo, reportAction=arguments.reportAction, siteCode=arguments.siteCode)>

			<cfset local.arrInitialReportSort = arrayNew(1)>
			<cfif local.frmReportType eq "postedBatch" and local.frmDisplay eq "detail">
				<cfset arrayAppend(local.arrInitialReportSort, { field='Member', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='MemberNumber', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Company', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Total Member Credit Balance', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Payment Credit Balance', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Payment Profile', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Payment Amount', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Payment Detail', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Payment Date', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Batch Deposit Date', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Batch Name', dir='asc' })>
			<cfelse>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Member', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='MemberNumber', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Company', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Total Member Credit Balance', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Payment Profile Credit Balance', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Payment Profile', dir='asc' })>
			</cfif>

			<cfset local.strReportQry = { qryReportFields=local.strReport.qryBalances, strQryResult=local.strReport.qryBalancesResult }>
			<cfset local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML)>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="generateData" access="package" output="false" returntype="struct" hint="also invoked from pending payments report">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="siteCode" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.reportAction = arguments.reportAction>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cfset local.frmReportType = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmreporttype/text())")>
		<cfset local.frmProfileIDList = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmprofileidlist/text())")>
		<cfset local.frmDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdateto/text())")>
		<cfset local.frmDisplay = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdisplay/text())")>

		<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
			reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
			existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
		<cfif local.strSQLPrep.ruleErr>
			<cfthrow message="There was an error in the report criteria.">
		</cfif>
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strReturn.qryBalances" result="local.strReturn.qryBalancesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..##tmpCredits') IS NOT NULL
					DROP TABLE ##tmpCredits;
				IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
					DROP TABLE ##tmp_membersForFS;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;

				CREATE TABLE ##tmpCredits (memberID int, memberName varchar(300), memberNumber varchar(50), memberCompany varchar(200), 
					hasMemberPhotoThumb int, totalCreditAmount decimal(18,2), creditAmount decimal(18,2), profileID int, profileName varchar(100),
					paymentTransactionID int, paymentDetail varchar(500), paymentTransactionDate datetime, paymentAmount decimal(18,2),
					batchID int, batchDate date, batchName varchar(400), rowNum int);
				CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY);
				CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

				DECLARE @orgID int, @outputFieldsXML xml;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.orgID#">;

				INSERT INTO ##tmpCredits (memberID, memberName, memberNumber, memberCompany, 
					hasMemberPhotoThumb, totalCreditAmount, creditAmount, profileID, profileName,
					paymentTransactionID, paymentDetail, paymentTransactionDate, paymentAmount,
					batchID, batchDate, batchName, rowNum)
				EXEC dbo.tr_report_creditBalance @orgID=@orgID,
					@reportType=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.frmReportType#">,
					@payProfileList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.frmProfileIDList#">,
					<cfif local.frmReportType eq "postedBatch">
						@endDate=<cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmDateTo#">,
						@displayType=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.frmDisplay#">,
					<cfelse>
						@endDate=NULL,
						@displayType=NULL,
					</cfif>
					@includeTotals = #local.reportAction eq "customcsv" ? 0 : 1#;
				
				-- fill source table to get fieldset data
				INSERT INTO ##tmp_membersForFS (memberID)
				SELECT DISTINCT memberID
				FROM ##tmpCredits
				WHERE memberID IS NOT NULL;
				
				-- get fieldset data and set back to snapshot because proc ends in read committed
				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID,
					@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
					@existingFields='memberId,m_lastname,m_firstname,m_membernumber,m_company',
					@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
					@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
					@membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembersFS', @linkedMembers=0, 
					@mode='<cfif local.reportAction eq 'customcsv'>export<cfelse>report</cfif>', @outputFieldsXML=@outputFieldsXML OUTPUT;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				<cfif local.reportAction eq 'customcsv'>
					<cfif local.frmReportType eq "postedBatch" and local.frmDisplay eq "detail">
						select tmp.memberName as [Member], tmp.MemberNumber, tmp.memberCompany as [Company], 
							cast(tmp.totalCreditAmount as decimal(18,2)) as [Total Member Credit Balance], 
							cast(tmp.creditAmount as decimal(18,2)) as [Payment Credit Balance], 
							tmp.profileName as [Payment Profile], tmp.paymentAmount as [Payment Amount], 
							tmp.paymentDetail as [Payment Detail], tmp.paymentTransactionDate as [Payment Date],
							tmp.batchDate as [Batch Deposit Date], tmp.batchName as [Batch Name],
							tmpFS.*
						INTO ###local.tempTableName#
						FROM ##tmpCredits as tmp
						INNER JOIN ##tmpMembersFS as tmpFS on tmpFS.memberID = tmp.memberID;
					<cfelse>
						select tmp.memberName as [Member], tmp.MemberNumber, tmp.membercompany as [Company], 
							cast(tmp.totalCreditAmount as decimal(18,2)) as [Total Member Credit Balance], 
							cast(tmp.creditAmount as decimal(18,2)) as [Payment Profile Credit Balance], 
							tmp.profileName as [Payment Profile],
							tmpFS.*
						INTO ###local.tempTableName#
						FROM ##tmpCredits as tmp
						INNER JOIN ##tmpMembersFS as tmpFS on tmpFS.memberID = tmp.memberID;
					</cfif>

					#generateFinalBCPTable(tblName="###local.tempTableName#", dropFields="memberID")#
				<cfelse>
					SELECT tmp.memberName, tmp.memberNumber, tmp.memberCompany, tmp.hasMemberPhotoThumb, tmp.profileName,
						tmp.creditAmount, tmp.totalCreditAmount, tmpFS.*,
						CASE WHEN rowNum = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
					FROM ##tmpCredits AS tmp
					LEFT OUTER JOIN ##tmpMembersFS AS tmpFS ON tmpFS.memberID = tmp.memberID
					ORDER BY rowNum;
				</cfif>

				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..##tmpCredits') IS NOT NULL
					DROP TABLE ##tmpCredits;
				IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
					DROP TABLE ##tmp_membersForFS;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif local.reportAction neq 'customcsv' and local.frmReportType eq "postedBatch">
			<!--- are there any non-posted batches in this reporting period? --->
			<cfquery name="local.strReturn.qryNonPostedBatches" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select count(*) as batchCount
					from dbo.tr_batches
					where orgID = <cfqueryparam value="#local.mc_siteInfo.orgID#" cfsqltype="CF_SQL_INTEGER">
					and statusID <> 4
					and (batchCode is null or batchCode <> 'PENDINGPAYMENTS')
					and depositDate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.frmDateTo# 23:59:59.997">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getAvailableMerchantProfiles" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var qryAvailableMerchantProfiles = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryAvailableMerchantProfiles">
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT p.profileID, p.profileName, s.siteID, s.siteName
			FROM dbo.mp_profiles AS p
			INNER JOIN dbo.mp_gateways AS g ON p.gatewayID = g.gatewayID
			INNER JOIN dbo.sites as s on s.siteID = p.siteID
			WHERE s.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			AND p.status IN ('A','I')
			AND g.isActive = 1
			AND g.gatewayType <> 'PayLater'
			ORDER BY s.siteName, s.siteID, p.profileName, p.profileID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryAvailableMerchantProfiles>
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();

		local.strFields = structNew();
		local.strFields.frmreporttype = { label="Pay Profile", value=arguments.event.getValue('frmReportType','') };
		local.isRealTime = local.strFields.frmreporttype.value eq "realTime";
		local.strFields.frmprofileidlist = { label="Pay Profile", value=arguments.event.getValue('frmProfileID#local.isRealTime ? "1" : "2"#','') };
		local.strFields.frmdateTo = { label="On or Before", value=local.isRealTime ? "" : arguments.event.getValue('frmDateTo','') };
		local.strFields.frmdisplay = { label="Display Option", value=local.isRealTime ? "" : arguments.event.getValue('frmDisplay','') };

		reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
		return returnAppStruct('','echo');
		</cfscript>
	</cffunction>

</cfcomponent>