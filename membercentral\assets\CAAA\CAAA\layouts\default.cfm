<cfoutput>
<!doctype html>
<html lang="en">
    <head>
        <cfinclude template="head.cfm">		
    </head>
	
    <cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
       <body class="home innerPage">
            <!-- wrapper start -->
            <div id="wrapper" class="wrapper">
                <!--Header Start-->
                <cfinclude template="header.cfm">
                <!--Header End-->  
				<cfset local.zone = "M"><!-- Top bar links -->
				<cfset local.zoneMcontent = ''>
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<cfset local.zoneMcontent = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))>
				</cfif>

				<cfset local.bannerImage =  ''>
				<cfif application.objCMS.getZoneItemCount(zone='L',event=event)>
					<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['L'],1)>
						<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['L'][1].data))>							
							<cfset local.bannerImage = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['L'][1].data,"<p>",""),"</p>",""))>
						</cfif>
					</cfif>
				</cfif>
				<cfif local.bannerImage eq ''>
					<cfif application.objCMS.getZoneItemCount(zone='K',event=event)>
						<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['K'],1)>
							<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['K'][1].data))>							
								<cfset local.bannerImage = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['K'][1].data,"<p>",""),"</p>",""))>
							</cfif>
						</cfif>
					</cfif>
				</cfif>

				<div id="zoneInnerImage" class="hide">	
					#local.bannerImage#
				</div>			
                
				 <!-- slider start -->
				<div class="bannerInner sec-pd70 bannerInnerWrap">
					
					<div class="container bannerInnerContainer">
						<div class="row d-flex-wrap">
						<div class="span6">
							<ol class="breadcrumb">
								<li class="breadcrumb-item">
									<a href="/">
									<img src="./images/icon-home.png" alt="">
									</a>
								</li>
								<cfif len(event.getValue('mc_pageDefinition.sectionName','')) AND event.getValue('mc_pageDefinition.sectionName','') NEQ "Root" AND event.getValue('mc_pageDefinition.sectionName','') NEQ event.getValue('mc_pageDefinition.pageName')>
                                	<li class="breadcrumb-separator">
										<i class="fa fa-angle-right"></i>
									</li>
									<li class="breadcrumb-item bcSection">
									<a href="<cfif len(event.getValue('mc_pageDefinition.sectionCode',''))>/?pg=#event.getValue('mc_pageDefinition.sectionCode')#
										<cfelse>javascript:void(0);</cfif>">
										#event.getValue('mc_pageDefinition.sectionName',event.getValue('mc_pageDefinition.pageName'))#</a>
									</li>
                                </cfif>
								<li class="breadcrumb-separator">
									<i class="fa fa-angle-right"></i>
								</li>
								<li class="breadcrumb-item active" aria-current="page" style="color:##fff; font-weight:600; text-transform:uppercase;">
									#event.getValue('mc_pageDefinition.PAGEFULLTITLE',event.getValue('mc_pageDefinition.pageName'))#
								</li>
							</ol>
							<h2 class="TitleText ">#event.getValue('mc_pageDefinition.pageTitle',event.getValue('mc_pageDefinition.pageName'))#</h2>
							<p class="bannerDesc"></p>
							<div class="btn-wrap bannerBtns">
							
							</div>
						</div>
						<span class="span6 bannerRightWrap">
							<div class="circle-wrap">
								<div class="banner-img-wrap">
									
								</div>
							</div>
						</span>
						</div>
					</div>
				</div>
				<!-- slider End -->

				
				<cfset local.PageClass = 'span12 removeFlex'>
				<cfif application.objCMS.getZoneItemCount(zone='M',event=event)>
					<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['M'],1)>
						<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['M'][1].data))>
							<cfset local.PageClass = 'span9'>
						</cfif>
					</cfif>
				</cfif>
				<cfif application.objCMS.getZoneItemCount(zone='N',event=event)>
					<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['N'],1)>
						<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['N'][1].data))>
							<cfset local.PageClass = 'span9'>
						</cfif>
					</cfif>
				</cfif>
				<cfif application.objCMS.getZoneItemCount(zone='O',event=event)>
					<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['O'],1)>
						<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['O'][1].data))>
							<cfset local.PageClass = 'span9'>
						</cfif>
					</cfif>
				</cfif>
				<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "full">
					<cfset local.PageClass = 'span12 removeFlex'>
				</cfif>

				<cfset local.hasZoneM = 0>
				<cfset local.hasZoneN = 0>
				<cfset local.hasZoneO = 0>
				<cfset local.zone = "M">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<cfset local.hasZoneM = 1>
				</cfif>	
				<cfset local.zone = "N">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<cfset local.hasZoneN = 1>
				</cfif>
				<cfset local.zone = "O">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<cfset local.hasZoneO = 1>
				</cfif>
				<cfif local.hasZoneM eq 0 && local.hasZoneN eq 0 && local.hasZoneO eq 0>
					<cfset local.PageClass = 'span12 removeFlex'>
				</cfif>

		
				<!-- Main Content Start -->
				<div class="inner-page-content">
					<div class="container">

						<div class="row d-flex-wrap">
						<div class="#local.PageClass# inner-content-area">	
							<cfif application.objCMS.getZoneItemCount(zone='Main',event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Main'], 1)>
								#application.objCMS.renderZone(zone='Main',event=event, mode='div')#
							</cfif>	
						</div>
						<cfif local.PageClass eq 'span9'>
							<div class="span3 leftInner widget">
								<div class="eventbox-row">
									<div class="eventbox-col zoneMWrap hidden">
										<div class="eventbox-img">
										<div class="event-head">
											<div class="event-icon">
											</div>
											<h4 class="ColumnHeader"></h4>
										</div>
										</div>
										<div class="eventbox-info">
										
										</div>
									</div>
									<div class="eventbox-col zoneNWrap hidden">
										<div class="eventbox-img">
											<div class="event-head">
												<div class="event-icon"></div>
												<h4 class="ColumnHeader"></h4>
											</div>
											</div>
											<div class="eventbox-info">
											<div class="inner-space-30 zoneNWrapCnt">
												
											</div>
											<div class="eventbox-item eventbox-item-link">
												
											</div>
										</div>
									</div>
									<div class="eventbox-col adv-box hidden zoneOObjCnt">
										
									</div>
								</div>
							</div>
						</cfif>
						</div>
					</div>
				</div>
				 <!-- Main Content End -->
				<cfset local.zone = "M"><!--Upcoming Events -->
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneMObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
				
				<cfset local.zone = "N"><!--Upcoming Events -->
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneNObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
				<cfset local.zone = "O">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneOObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>

                <!--Footer Start-->
                <cfinclude template="footer.cfm">				
                <!--Footer End-->                
                <cfinclude template="toolBar.cfm">
            </div>
            <!-- wrapper end --> 
        </body>
    <cfelse>
        <body style="background:none!important;background:unset!important;padding:20px;" class="innerPage-content">
            #application.objCMS.renderZone(zone='Main',event=event)#
        </body>		
    </cfif>
    <cfinclude template="foot.cfm">   
</html>
</cfoutput>