<cfset local.jsValidation = "">
<cfset local.showReqFlag = false>
<cfset local.xmlFields = attributes.data.xmlFields>
<cfset local.fieldsetInfo = attributes.data.fieldsetInfo>
<cfset local.qrySettings = attributes.data.qrySettings>

<cfoutput>
<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

	<div class="container-fluid">
		<div class="page-header span12"><h2><small>#attributes.data.memberDirectoryInfo.applicationInstanceName#</small></h2></div>

		<cfif ArrayLen(local.xmlFields.xmlRoot.xmlChildren) is 0 and arrayLen(attributes.data.arrClassifications) is 0>
			<div class="span12"><br/>Searching is not available at this time.</div>
		<cfelse>
			<cfform id="frmMemberDirectory" name="frmMemberDirectory" method="post" action="/?#attributes.data.baseQueryString#&dirAction=SearchResults" class="form-horizontal">
				<div class="span12" id="MD_err_div" style="display:none;"></div>

				<cfset local.showMatchingOption = false>
				<cfloop array="#attributes.data.arrFormFields#" index="local.thisfield">
					<cfoutput>
						<div class="control-group span12">
							<cfif StructKeyExists(local.thisfield, "isPostalCode") AND local.thisfield.isPostalCode>
								<cfset local.labelfor = "#local.thisfield.fieldCode#_radius" />
							<cfelse>
								<cfset local.labelfor = "#local.thisfield.fieldCode#" />
							</cfif>
							<label class="control-label" for="#local.labelfor#">
								<cfif local.thisfield.isRequired is 1>*&nbsp;</cfif>#encodeForHTML(local.thisfield.fieldLabel)#:
								<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.fieldDescription))>
									&nbsp;
									<img src="/assets/common/images/help-icon.jpg" onMouseOver="Tip('#JSStringFormat(replace(trim(local.thisfield.fieldDescription),chr(34),"'","ALL"))#');" onMouseOut="UnTip();" />
								<cfelse>
									&nbsp;
								</cfif>
							</label>
							<div class="controls">
								<cfswitch expression="#local.thisfield.displayTypeCode#">
									<cfcase value="TEXTBOX,TEXTAREA,HTMLCONTENT">
										<cfif StructKeyExists(local.thisfield, "isPostalCode") AND local.thisfield.isPostalCode>
											Within 
											<cfselect name="#local.thisfield.fieldCode#_radius" id="#local.thisfield.fieldCode#_radius">
												<cfloop list="5,10,25,50,100" index="local.thisrad">
													<option value="#local.thisrad#">#local.thisrad#</option>
												</cfloop>
											</cfselect>
											#attributes.data.memberDirectoryInfo.distanceLabel# of #encodeForHTML(local.thisfield.fieldLabel)#
											<cfinput type="text" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" value="" autocomplete="off" size="8">
										<cfelse>
											<cfif local.thisfield.displayTypeCode eq "TEXTBOX">
												<cfinput type="text" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" value="" autocomplete="off" size="24">
											<cfelse>
												<cfinput type="text" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" value="" autocomplete="off" size="48">
											</cfif>
											<cfif local.thisfield.dataTypeCode eq "STRING">
												<cfset local.showMatchingOption = true>
											</cfif>
										</cfif>
									</cfcase>
									<cfcase value="RADIO,SELECT,CHECKBOX">
										<cfif StructKeyExists(local.thisfield, 'qryData')>
											<cfselect query="local.thisfield.qryData" value="#local.thisfield.optValue#" display="#local.thisfield.optDisplay#" name="#local.thisfield.fieldCode#"  id="#local.thisfield.fieldCode#" queryPosition="below"><option value=""></option></cfselect>
										<cfelse>
											<cfset local.isMultiSelect = structKeyExists(local.thisfield,"multiSelect") AND local.thisfield.multiSelect>
											<select name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" <cfif local.isMultiSelect>multiple="multiple"</cfif>>
												<cfif NOT local.isMultiSelect>
													<option value=""></option>
												</cfif>
												<cfloop array="#local.thisfield.arrOptions#" index="local.thisOpt">
													<cfif isStruct(local.thisOpt)>
														<option value="#local.thisOpt.val#">#local.thisOpt.label#</option>
													<cfelse>
														<option value="#local.thisOpt#"><cfif local.thisfield.dataTypeCode eq "BIT">#YesNoFormat(local.thisOpt)#<cfelse>#local.thisOpt#</cfif></option>
													</cfif>
												</cfloop>
											</select>
											<cfif local.isMultiSelect>
												<cfsavecontent variable="local.jQueryMultiselect">
													<cfoutput>
													<script type="text/javascript">
														$(function(){
															$("###local.thisfield.fieldCode#").multiselect({ header: "Choose options below",  selectedList: 10, minWidth: 200 });
														});
													</script>	
													</cfoutput>
												</cfsavecontent>
												<cfhtmlhead text="#application.objCommon.minText(local.jQueryMultiselect)#">
											</cfif>
										</cfif>
									</cfcase>
									<cfcase value="DATE">
										<cfinput type="text" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" value="" autocomplete="off" size="12">
										<cfsavecontent variable="local.datejs">
											<cfoutput>
											<script language="javascript">
												$(function() { 
													mca_setupDatePickerField('#local.thisfield.fieldCode#');
													$("##btnClear#local.thisfield.fieldCode#").click( function(e) { mca_clearDateRangeField('#local.thisfield.fieldCode#');return false; } );
												});
											</script>
											<style type="text/css">
											###local.thisfield.fieldCode# { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
											</style>
											</cfoutput>
										</cfsavecontent>
										<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">							
									</cfcase>
								</cfswitch>
							</div>
						</div>
					</cfoutput>
				</cfloop>

				<cfoutput>
				<cfloop array="#attributes.data.arrClassifications#" index="local.thisClassification">
					<div class="control-group span12">
						<label class="control-label" for="mg_gid">
							#encodeForHTML(local.thisClassification.name)#: &nbsp;
						</label>
						<div class="controls">
							<cfselect name="mg_gid" id="mg_gid" query="local.thisClassification.qryClassificationslinks" display="groupName" value="groupId" queryPosition="below">
								<option value=""></option>
							</cfselect>
						</div>
					</div>
				</cfloop>
				</cfoutput>
				
				<cfif local.showMatchingOption>
					<div class="control-group span12" role="radiogroup" aria-labelledby="fs_match_label">
						<div style="padding-left:25px;padding-top:5px;">
							Find matches
							<select name="fs_match" id="fs_match">
								<option value="s" <cfif local.qrySettings.defaultSearchOption EQ 's'>selected</cfif>>beginning with</option>
								<option value="c" <cfif local.qrySettings.defaultSearchOption EQ 'c'>selected</cfif>>containing</option>
								<option value="e" <cfif local.qrySettings.defaultSearchOption EQ 'e'>selected</cfif>>exactly matching</option>
							</select>
							the terms I entered.
						</div>
					</div>
				</cfif>
				
				<button name="btnSubmit" type="button" class="clearfix btn btn-large btn-primary span3" onClick="doMDSearch()">Search</button>

				<cfset local.showIsRequiredFootnote = false>
				<cfloop array="#attributes.data.arrFormFields#" index="local.thisfield">
					<cfif local.thisfield.isRequired is 1>
						<cfset local.showIsRequiredFootnote = true>
						<cfswitch expression="#local.thisfield.displayTypeCode#">
						<cfcase value="TEXTBOX">
							<cfsavecontent variable="local.jsValidation">
								<cfoutput>
								#local.jsValidation#
								if(!_CF_hasValue(_CF_this['#local.thisfield.fieldCode#'], "TEXT", false)) locateErr += "<i>#htmlEditFormat(local.thisfield.fieldLabel)#</i> is required.<br/>";
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="RADIO,SELECT,CHECKBOX">
							<cfsavecontent variable="local.jsValidation">
								<cfoutput>
								#local.jsValidation#
								if (_CF_this['#local.thisfield.fieldCode#'].options[_CF_this['#local.thisfield.fieldCode#'].selectedIndex].value.length == 0) locateErr += "<i>#htmlEditFormat(local.thisfield.fieldLabel)#</i> is required.<br/>";
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="DATE">
							<cfsavecontent variable="local.jsValidation">
								<cfoutput>
								#local.jsValidation#
								if(!_CF_hasValue(_CF_this['#local.thisfield.fieldCode#'], "DATEFIELD", false) || !_CF_checkdate(_CF_this['#local.thisfield.fieldCode#'].value, true)) locateErr += "<i>#htmlEditFormat(local.thisfield.fieldLabel)#</i> is required and must be a valid date.<br/>";
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						</cfswitch>
						<cfset local.showReqFlag = true>
					</cfif>
				</cfloop>

				<cfif local.showIsRequiredFootnote>
					<div class="control-group">
						<div class="controls">
							<i>* required field</i>
						</div>
					</div>
				</cfif>

			</cfform>
		</cfif>
		<br/>
		<cfif len(attributes.data.searchContentStruct.rawContent)>
			<div class="span12 well well-large">#attributes.data.searchContentStruct.rawContent#</div>
		</cfif>
	</div>
</cfoutput>


<cfsavecontent variable="local.jsFunc">
	<style type="text/css">
		.alert { background:#fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid #f00; border-bottom:2px solid #f00; }
	</style>
	<script language="javascript">
	hideMDAlert = function() {
		var abox = document.getElementById('MD_err_div');
			abox.innerHTML = '';
			abox.style.display = 'none';
	};
	showMDAlert = function(msg) {
		var abox = document.getElementById('MD_err_div');
			abox.innerHTML = msg;
			abox.className = 'alert';
			abox.style.display = '';
	};
	validateMDSearch = function() {
		var _CF_this = document.forms['frmMemberDirectory'];
		var locateErr = '';
		<cfoutput>#local.jsValidation#</cfoutput>
		if (locateErr.length > 0) {
			showMDAlert(locateErr);
			return false;
		} else {
			hideMDAlert();
			return true;
		}
	};
	doMDSearch = function() {
		if (validateMDSearch()) document.forms['frmMemberDirectory'].submit();
	};
	
	$(function() {
		$('#frmMemberDirectory input').keyup(function(event) {
			if (event.keyCode == 13)
				doMDSearch();
		});
	});
	</script>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.jsFunc)#">