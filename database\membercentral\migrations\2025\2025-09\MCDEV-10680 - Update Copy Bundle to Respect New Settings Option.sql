USE seminarWeb
GO

ALTER PROC dbo.sw_copyBundle
@bundleID int,
@incDetails bit,
@incRates bit,
@incSeminars bit,
@incSettings bit = 0,
@recordedByMemberID int,
@newbundleID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @orgCode varchar(10), @participantID int, @siteID int, @isSWOD bit, @programCode varchar(15),
		@resourceTypeID int, @siteResourceID int, @semWebCatalogSiteResourceID int;

	SELECT @resourceTypeID = memberCentral.dbo.fn_getResourceTypeID('SWBundle');

	SELECT @isSWOD = b.isSWOD, @orgID = mcs.orgID, @siteID = mcs.siteID, 
		@participantID = p.participantID, @orgCode=p.orgCode
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = b.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	WHERE b.bundleID = @bundleID;
	
	SELECT @semWebCatalogSiteResourceID = ai.siteResourceID
	FROM memberCentral.dbo.cms_applicationInstances AS ai 
	INNER JOIN memberCentral.dbo.cms_applicationTypes AS apt ON apt.applicationTypeID = ai.applicationTypeID 
		and apt.applicationTypeName = 'SemWebCatalog' 
	INNER JOIN memberCentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID
		AND sr.siteResourceID = ai.siteResourceID 
		AND sr.siteResourceStatusID = 1
	WHERE ai.siteID = @siteID;

	EXEC memberCentral.dbo.getUniqueCode @uniqueCode=@programCode OUTPUT;

	BEGIN TRAN;
		EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,  
			@isVisible=1, @parentSiteResourceID=@semWebCatalogSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;
			
		-- insert new bundle
		INSERT INTO dbo.tblBundles (participantID, isSWOD, bundleName, bundleSubTitle, programCode, dateCatalogStart, dateCatalogEnd, dateCreated, 
			isPriceBasedOnActual, freeRateDisplay, siteResourceID, submittedByMemberID, customTextEnabled, customTextContent)
		SELECT participantID, isSWOD, 'Copy of ' + bundleName, bundleSubTitle, @programCode, dateCatalogStart, dateCatalogEnd, getdate(), 
			isPriceBasedOnActual, freeRateDisplay, @siteResourceID, @recordedByMemberID,
			CASE WHEN @incSettings = 1 THEN ISNULL(customTextEnabled, 0) ELSE 0 END,
			CASE WHEN @incSettings = 1 THEN customTextContent ELSE NULL END
		FROM dbo.tblBundles
		where bundleID = @bundleID;
			SELECT @newbundleID = SCOPE_IDENTITY();

		SET @programCode = 'SWB-' + CAST(@newbundleID AS varchar(10));

		UPDATE dbo.tblBundles
		SET programCode = @programCode
		WHERE bundleID = @newbundleID;

		-- auto opt in the publisher
		INSERT INTO dbo.tblBundlesOptIn (bundleID, participantID, isPriceBasedOnActual, freeRateDisplay, dateAdded, isActive)
		SELECT bundleID, participantID, isPriceBasedOnActual, freeRateDisplay, GETDATE(), 1
		FROM dbo.tblBundles
		WHERE bundleID = @newbundleID;

		-- learning objectives
		IF @incDetails = 1 BEGIN
			INSERT INTO dbo.tblLearningObjectives (bundleID, objective, objectiveOrder)
			SELECT @newbundleID, objective, objectiveOrder
			FROM dbo.tblLearningObjectives
			WHERE bundleID = @bundleID
			ORDER BY objectiveOrder;
		END

		-- copy rates
		IF @incRates = 1
			EXEC dbo.swb_copyRates @participantID=@participantID, @copyFromBundleID=@bundleID, @copyToBundleID=@newbundleID, 
				@recordedByMemberID=@recordedByMemberID;

		-- bundled items
		IF @incSeminars = 1
			INSERT INTO dbo.tblBundledItems (bundleID, seminarID, itemOrder)
			SELECT @newbundleID, seminarId, itemOrder
			FROM dbo.tblBundledItems
			WHERE bundleID = @bundleID;

		-- record fee for bundle creation
		EXEC dbo.swb_addBillingLogForBundleCreation @orgCode=@orgCode, @bundleID=@newbundleID, @recordedByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	SELECT '{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + cast(@orgID as varchar(10)) + ',
		"SITEID":' + cast(@siteID as varchar(10)) + ',
		"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
		"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
		"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('SWB-' + cast(bundleID as varchar(20)) + ' [' + bundleName + '] has been created as ['+ CASE WHEN @isSWOD = 1 THEN 'SWOD' ELSE 'SWL' END +'] by copying SWB-' + cast(@bundleID as varchar(20)) + '.'),'"','\"') + '" } }'
	FROM dbo.tblBundles
	WHERE bundleID = @newbundleID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
