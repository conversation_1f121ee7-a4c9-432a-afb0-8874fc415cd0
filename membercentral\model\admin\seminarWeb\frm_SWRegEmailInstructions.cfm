<cfsavecontent variable="local.instructionsJS">
	<cfoutput>
	<script language="javascript">
		function validateSendInstructionForm(){
			mca_hideAlert('err_resendinstr');
			
			if ($('##existingRegMails').val().length == 0 && $('##emailToUse').val().length == 0) {
				mca_showAlert('err_resendinstr', 'Enter a valid e-mail address');
				return false;
			}

			top.$("##btnMCModalSave").prop('disabled',true).text('Resending Confirmation...');
			return true;
		}
		<cfif ListFindNoCase("SWL,SWOD",local.SWType)>
			function toggleImportantText(){
				var hidFrame = frames['previewFrame'].document;
				$(hidFrame).find('##customtextimportant, ##customtext ').html('');
				writeCustom($('##customtext').val());
			}
		</cfif>
		function writePreview() {
			var hidFrame = frames['previewFrame'].document;
			hidFrame.open();
			hidFrame.write("#JSStringFormat(local.strEmailContent.templateDisp)#");
			hidFrame.close();
			$('##previewFrame').removeClass('d-none');
		}
		function writeCustom(txt) {
			var hidFrame = frames['previewFrame'].document;
			var isImportantText = $('##isImportantText').is(':checked') || 0;
			var ct = hidFrame.getElementById(isImportantText ? 'customtextimportant' : 'customtext');
			if (ct) ct.innerHTML = txt.replace(/\r\n|\r|\n/g,"<br/>") + (txt.length ? '<br/><br/>' : '');
		}

		$(function() { 
			writePreview();
			mca_setupTagsInput(['emailToUse'], 'err_resendinstr', "#application.regEx.email#", 'email address');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.instructionsJS#">
	
<cfoutput>
<cfif local.showInfoAlert>
	<div class="alert alert-info" role="alert"><i class="fa-solid fa-circle-info"></i> This email is scheduled for delivery at these timeframes: #local.formattedTimeframes# before the scheduled webinar. You can edit this schedule by visiting the Scheduled Tasks tab from your webinar list view.</div>
</cfif>
<form name="frmInstructions" id="frmInstructions" action="#local.sendInstructionsLink#" class="px-3 mt-2" method="POST" onsubmit="return validateSendInstructionForm();">
<cfif local.SWType EQ "SWB">
	<input type="hidden" name="eID" value="#local.orderID#">
<cfelse>
	<input type="hidden" name="eID" value="#local.enrollmentID#">
</cfif>
<input type="hidden" name="existingRegMails" id="existingRegMails" value="<cfif len(local.registrantEmail) OR len(local.registrantOverrideEmail)>#local.registrantEmail#;#local.registrantOverrideEmail#</cfif>">

<div id="err_resendinstr" class="alert alert-danger mb-2 d-none"></div>

<cfif len(local.registrantEmail) or len(local.registrantOverrideEmail)>
	<div class="mb-2 form-text small text-dim">We'll send to these addresses:</div>
</cfif>
<div class="form-row">
	<div class="col">
		<cfif len(local.registrantEmail)>
			<span class="badge badge-primary badge-pill">Primary</span> #local.registrantEmail#
		<cfelse>
			<span class="badge badge-dark badge-pill">No Primary Email Address Defined</span>
		</cfif>
	</div>
</div>
<cfif len(local.registrantOverrideEmail)>
	<div class="form-row mt-2">
		<div class="col">
			<span class="badge badge-warning badge-pill">Override</span> #local.registrantOverrideEmail#
		</div>
	</div>
</cfif>
<div class="form-group mt-3">
	<div class="form-label-group">
		<input type="text" name="emailToUse" id="emailToUse" value="" class="form-control">
		<label for="emailToUse">Include these additional addresses</label>
	</div>
</div>

<div class="d-flex mb-2">
	<div class="small text-dim">Custom Text: (will appear below opening paragraph)</div>
	<cfif ListFindNoCase("SWL,SWOD,SWB",local.SWType)>
		<div class="custom-control custom-switch ml-auto">
			<input type="checkbox" name="isImportantText" id="isImportantText" value="1" class="custom-control-input" onchange="toggleImportantText();">
			<label class="custom-control-label" for="isImportantText">Important Text</label>
		</div>
	</cfif>
</div>
<div class="form-label-group">
	<textarea name="customtext" id="customtext" rows="5" class="form-control" onkeyup="writeCustom(this.value);"></textarea>
</div>

<!--- hidden submit triggered from parent --->
<button type="submit" class="d-none"></button>
</form>

<iframe name="previewFrame" id="previewFrame" class="px-3 d-none" height="390" marginwidth="0" marginheight="0" frameborder="0" style="border:0;width:100%;"></iframe>
</cfoutput>