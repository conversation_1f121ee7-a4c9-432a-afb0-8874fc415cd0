<cfif val(local.panelid1)>
	<cfset local.qryGetSubPanelsFilter1 = this.objAdminReferrals.getPanelChildrenByID(panelid=local.panelid1, isActive=1)>
</cfif>	
<cfif val(local.panelid2)>
	<cfset local.qryGetSubPanelsFilter2 = this.objAdminReferrals.getPanelChildrenByID(panelid=local.panelid2, isActive=1)>
</cfif>	
<cfif val(local.panelid3)>
	<cfset local.qryGetSubPanelsFilter3 = this.objAdminReferrals.getPanelChildrenByID(panelid=local.panelid3, isActive=1)>
</cfif>
<cfoutput>
<div class="row">
	<div class="col-md-12">
		<div id="changePanelWarning" class="alert alert-danger alert-dismissible fade show hidden" role="alert"></div>
	</div>
</div>
<div class="row" id="filterFS">
	<div class="col-md-12">
		<div id="clientMsgWrapper" class="alert alert-danger alert-dismissible fade show hidden" role="alert">
			<span id="clientMsg"></span>
			<button type="button" class="close" aria-label="Close" data-hide="alert">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
		<div class="card card-box mb-5">
			<div class="card-header bg-secondary font-weight-bold">
				Filters
			</div>
			<div class="card-body">
				<div class="form-group row align-items-center">
					<div class="col-md-9 col-xl-6">
						<div class="row">
							<label for="panelid1" class="col-md-4 col-xl-5 col-form-label">Primary Panel *</label>
							<div class="col-md-7">
								<div class="input-group input-group-sm">
									<select name="panelid1" id="panelid1" class="form-control form-control-sm">
										<option value="0">Select a Panel</option>
										<cfloop query="local.qryGetPanelsFilter">
											<option value="#local.qryGetPanelsFilter.panelID#" <cfif local.panelid1 is local.qryGetPanelsFilter.panelID>selected</cfif>>#local.qryGetPanelsFilter.name#</option>
										</cfloop>
									</select>
									<cfif currTab eq 'filters' and  (local.callPaymentProcessed or local.clientPaymentSaleRecorded)>			
										<button type="button" class="btn btn-link" name="btnChangePanel" id="btnChangePanel">Change Panel</button>
									</cfif>
									<cfinput type="hidden" name="panelChangedInd"  id="panelChangedInd" value="0" />
									<cfinput type="hidden" name="prevPanelID"  id="prevPanelID" value="#local.prevPanelID#" />
								</div>
							</div>
						</div>
					</div>
					<div class="col-md-9 col-xl-6">
						<div class="row">
							<label for="subpanelid1" class="col-md-4 col-xl-5 col-form-label text-xl-center text-md-left">Sub-Panel</label>
							<div class="col-md-7">
								<div class="input-group input-group-sm">
									<select name="subpanelid1" id="subpanelid1" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Select Option">
										<cfif val(local.panelid1)>
											<cfloop query="local.qryGetSubPanelsFilter1">
												<option value="#local.qryGetSubPanelsFilter1.panelID#" <cfif listFind(local.subpanelid1,local.qryGetSubPanelsFilter1.panelID)>selected</cfif>>#local.qryGetSubPanelsFilter1.name#</option>
											</cfloop>
										</cfif>
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="form-group row align-items-center">
					<div class="col-md-9 col-xl-6">
						<div class="row">
							<label for="panelid2" class="col-md-4 col-xl-5 col-form-label">Secondary Panel</label>
							<div class="col-md-7">
								<div class="input-group input-group-sm">
									<select name="panelid2" id="panelid2" class="form-control form-control-sm">
										<option value="0">Select a Panel</option>
										<cfloop query="local.qryGetPanelsFilter">
											<option value="#local.qryGetPanelsFilter.panelID#" <cfif local.panelid2 is local.qryGetPanelsFilter.panelID>selected</cfif>>#local.qryGetPanelsFilter.name#</option>
										</cfloop>
									</select>
								</div>
							</div>
						</div>
					</div>
					<div class="col-md-9 col-xl-6">
						<div class="row">
							<label for="subpanelid2" class="col-md-4 col-xl-5 col-form-label text-xl-center text-md-left">Sub-Panel</label>
							<div class="col-md-7">
								<div class="input-group input-group-sm">
									<select name="subpanelid2" id="subpanelid2" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Select Option">
										<cfif val(local.panelid2)>
											<cfloop query="local.qryGetSubPanelsFilter2">
												<option value="#local.qryGetSubPanelsFilter2.panelID#" <cfif listFind(local.subpanelid2,local.qryGetSubPanelsFilter2.panelID)>selected</cfif>>#local.qryGetSubPanelsFilter2.name#</option>
											</cfloop>
										</cfif>
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="form-group row align-items-center">
					<div class="col-md-9 col-xl-6">
						<div class="row">
							<label for="panelid3" class="col-md-4 col-xl-5 col-form-label">Tertiary Panel</label>
							<div class="col-md-7">
								<div class="input-group input-group-sm">
									<select name="panelid3" id="panelid3" class="form-control form-control-sm">
										<option value="0">Select a Panel</option>
										<cfloop query="local.qryGetPanelsFilter">
											<option value="#local.qryGetPanelsFilter.panelID#" <cfif local.panelid3 is local.qryGetPanelsFilter.panelID>selected</cfif>>#local.qryGetPanelsFilter.name#</option>
										</cfloop>
									</select>
								</div> 
							</div>
						</div>
					</div>
					<div class="col-md-9 col-xl-6">
						<div class="row">
							<label for="subpanelid3" class="col-md-4 col-xl-5 col-form-label text-xl-center text-md-left">Sub-Panel</label>
							<div class="col-md-7">
								<div class="input-group input-group-sm">
									<select name="subpanelid3" id="subpanelid3" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Select Option">
										<cfif val(local.panelid3)>
											<cfloop query="local.qryGetSubPanelsFilter3">
												<option value="#local.qryGetSubPanelsFilter3.panelID#" <cfif listFind(local.subpanelid3,local.qryGetSubPanelsFilter3.panelID)>selected</cfif>>#local.qryGetSubPanelsFilter3.name#</option>
											</cfloop>
										</cfif>
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
				<cfif ArrayLen(local.xmlFields.xmlRoot.xmlChildren)>	
					<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
						<cfset local.thisFieldValue = evaluate("local.#local.thisfield.xmlattributes.fieldCode#") />
						<div class="form-group row align-items-center" id="customFieldSet">
							<div class="col-md-9 col-xl-6">
								<div class="row">
									<label for="#local.thisfield.xmlattributes.fieldCode#" class="col-md-4 col-xl-5 col-form-label">
										#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1>&nbsp;*</cfif>
										<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
											<i class="fa-solid fa-circle-question float-right" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
										</cfif>
									</label>
									<div class="col-md-7">
										<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
											<cfcase value="TEXTBOX">
												<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>	
													<cfset local.thisRadiusValue = evaluate("local.#local.thisfield.xmlattributes.fieldCode#_radius") />												
													<cfsavecontent variable="local.postalcodeJs">
														<cfoutput>
															<script language="javascript">
																$(function() { 
																	$('###local.thisfield.xmlattributes.fieldCode#_postalcode .zip_field_link').on('click', function() {
																		$('###local.thisfield.xmlattributes.fieldCode#').val('#local.thisFieldValue#');
																	});
																});
															</script>
														</cfoutput>
													</cfsavecontent>
													<cfhtmlhead text="#application.objCommon.minText(local.postalcodeJs)#">
													<div class="d-flex align-items-center">
														<div style="width:120px;">Within</div> 
														<select name="#local.thisfield.xmlattributes.fieldCode#_radius" id="#local.thisfield.xmlattributes.fieldCode#_radius" class="form-control form-control-sm skipfillcheck mx-1" <cfif local.thisfield.xmlattributes.allowMultiple> multiple="true" data-toggle="custom-select2" placeholder="Select Option"</cfif>>
															<cfloop list="5,10,25,50,100" index="local.thisrad">
																<option value="#local.thisrad#" <cfif listFindNoCase(local.thisRadiusValue,local.thisrad)>selected="selected"</cfif>>#local.thisrad#</option>
															</cfloop>
														</select>
														<div class="text-nowrap">miles of</div>
														<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="form-control form-control-sm ml-1 pl-1 pr-1 text-center">
													</div>
													<div class="d-flex align-items-center" id="#local.thisfield.xmlattributes.fieldCode#_postalcode">
														<a href="##" class="zip_field_link">Use client zip code</a>
													</div>
												<cfelse>
													<div class="input-group">
														<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisFieldValue#" autocomplete="off" class="form-control form-control-sm">
													</div>
												</cfif>
											</cfcase>
											<cfcase value="RADIO,SELECT,CHECKBOX">
												<cfif ReFindNoCase('ma_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
													<cfset local.qryStates = application.objMember.getStates(local.rc.mc_siteinfo.orgID)>
													<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="form-control form-control-sm">
														<option value=""></option>
														<cfoutput query="local.qryStates" group="countryID">
															<optgroup label="#local.qryStates.country#">
															<cfoutput>
																<option value="#local.qryStates.statecode#">#local.qryStates.stateName# &nbsp;</option>
															</cfoutput>
															</optgroup>
														</cfoutput>
													</select>
												<cfelseif listFindNoCase("m_recordtypeid,m_membertypeid,m_status", local.thisfield.xmlattributes.fieldCode)>
													<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="form-control form-control-sm" <cfif local.thisfield.xmlattributes.allowMultiple> multiple="true" data-toggle="custom-select2" placeholder="Select Option"</cfif>>
														<option value=""></option>
														<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
															<option value="#local.thisOpt.xmlAttributes.valueID#" <cfif listFindNoCase(local.thisFieldValue,local.thisOpt.xmlAttributes.valueID)>selected="selected"</cfif>>#local.thisOpt.xmlAttributes.columnValueString#</option>
														</cfloop>
													</select>
												<cfelse>
													<cfset local.multiSelect = local.thisfield.xmlAttributes.dbObjectAlias EQ 'vwmd' AND local.thisfield.xmlAttributes.dataTypeCode NEQ 'BIT'>
													<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="form-control form-control-sm"<cfif local.multiSelect> multiple="true" data-toggle="custom-select2" placeholder="Select options"</cfif>>
														<cfif NOT local.multiSelect>
															<option value=""></option>
														</cfif>
														<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
															<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
															<cfcase value="STRING">
																<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
																<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueString>
															</cfcase>
															<cfcase value="DECIMAL2">
																<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
																<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueDecimal2>
															</cfcase>
															<cfcase value="INTEGER">
																<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
																<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueInteger>
															</cfcase>
															<cfcase value="DATE">
																<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
																<cfset local.thisOptColDisplay = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
															</cfcase>
															<cfcase value="BIT">
																<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
																<cfset local.thisOptColDisplay = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
															</cfcase>
															<cfdefaultcase>
																<cfset local.thisOptColValue = "">
																<cfset local.thisOptColDisplay = "">
															</cfdefaultcase>
															</cfswitch>
															<cfset local.thisOptVal = local.thisfield.xmlAttributes.dbObjectAlias EQ 'vwmd' ? local.thisOpt.xmlAttributes.valueID : local.thisOptColValue>
															<option value="#local.thisOptVal#" <cfif listFindNoCase(local.thisFieldValue,local.thisOptVal)>selected="selected"</cfif>>#local.thisOptColDisplay#</option>
														</cfloop>
													</select>
												</cfif>
											</cfcase>
											<cfcase value="DATE">
												<div class="input-group input-group-sm">
													<input class="form-control form-control-sm dateControl" type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisFieldValue#">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="#local.thisfield.xmlattributes.fieldCode#"><i class="fa-solid fa-calendar"></i></span>
													</div>
													<button type="button" class="btn btn-pill btn-secondary btn-sm ml-2 py-0" name="btnClear#local.thisfield.xmlattributes.fieldCode#" id="btnClear#local.thisfield.xmlattributes.fieldCode#">clear</button>
												</div>
												<cfsavecontent variable="local.datejs">
													<cfoutput>
													<script language="javascript">
														$(function() { 
															mca_setupDatePickerField('#local.thisfield.xmlattributes.fieldCode#');
															$("##btnClear#local.thisfield.xmlattributes.fieldCode#").click( function(e) { mca_clearDateRangeField('#local.thisfield.xmlattributes.fieldCode#'); } );
														});
													</script>
													</cfoutput>
												</cfsavecontent>
												<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">							
											</cfcase>
										</cfswitch>
									</div>
								</div>
							</div>
						</div>
						<cfif local.thisfield.xmlattributes.isRequired is 1>
							<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
							<cfcase value="TEXTBOX,DATE">
								<cfsavecontent variable="local.jsValidation">
									<cfoutput>
									#local.jsValidation#
									if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) locateErr += "- #htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# is required.<br>";
									</cfoutput>
								</cfsavecontent>
							</cfcase>
							<cfcase value="RADIO,SELECT,CHECKBOX">
								<cfsavecontent variable="local.jsValidation">
									<cfoutput>
									#local.jsValidation#
									if (_CF_this['#local.thisfield.xmlattributes.fieldCode#'].selectedIndex == -1) locateErr += "- #htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br>";
									</cfoutput>
								</cfsavecontent>
							</cfcase>
							</cfswitch>
							<cfset local.showReqFlag = true>
						</cfif>
					</cfloop>
				</cfif>
				<div class="form-group row align-items-center">
					<div class="col-md-9 col-xl-6">
						<div class="row">
							<cfoutput>
								<cfloop query="local.qryGetClassifications">
									<cfif val(local.qryGetClassifications.allowSearch)>
										<label for="mg_gid_#local.qryGetClassifications.groupSetID#" class="col-md-4 col-xl-5 col-form-label"><cfif len(trim(local.qryGetClassifications.name))>#local.qryGetClassifications.name#<cfelse>#local.qryGetClassifications.groupSetName#</cfif></label>
										
										<div class="col-sm-7">
											<div class="input-group input-group-sm">
												<cfset local.qryGetGroupSetGroup = this.objAdminReferrals.getGroupSetGroup(local.qryGetClassifications.groupSetID) />
												<cfset local.thisGroupIDValue = evaluate("local.mg_gid_#local.qryGetClassifications.groupSetID#") />
												<select class="form-control form-control-sm" name="mg_gid_#local.qryGetClassifications.groupSetID#" id="mg_gid_#local.qryGetClassifications.groupSetID#" data-toggle="custom-select2" placeholder="Select Option" multiple="true">
													<cfloop query="local.qryGetGroupSetGroup">
														<option value="#local.qryGetGroupSetGroup.groupsetGroupID#" <cfif listFind(local.thisGroupIDValue,local.qryGetGroupSetGroup.groupsetGroupID)>selected="selected"</cfif>>#local.qryGetGroupSetGroup.labelOverride#</option>
													</cfloop>
												</select>
											</div>
										</div>
									</cfif>
								</cfloop>
							</cfoutput>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col-sm-12 text-left fl">* indicates a required field</div>
	</div>
</div>
</cfoutput>
<cfloop list="#local.clientFieldNames#" index="local.thisField">
	<cfif isDefined("form.#local.thisField#")>
		<cfinput type="hidden" name="#local.thisField#"  id="#local.thisField#" value="#evaluate("form." & local.thisField)#" />
	</cfif>
</cfloop>

<cfif (local.callPaymentProcessed or local.clientPaymentSaleRecorded)>	
	<cfloop list="#local.filterFieldNames#" index="local.thisField">
		<cfif isDefined("form.#local.thisField#")>
			<cfif not arguments.event.valueExists('copy')>
				<cfinput type="hidden" name="#local.thisField#"  id="#local.thisField#" value="#evaluate("form." & local.thisField)#" class="filterHidden" />
			<cfelse>
				<cfinput type="hidden" name="#local.thisField#"  id="#local.thisField#" value="" class="filterHidden" />
			</cfif>
		</cfif>
	</cfloop>
</cfif>

<cfloop list="#local.lawyerFieldNames#" index="local.thisField">
	<cfif isDefined("form.#local.thisField#")>
		<cfinput type="hidden" name="#local.thisField#"  id="#local.thisField#" value="#evaluate("form." & local.thisField)#" />
	</cfif>
</cfloop>
<cfsavecontent variable="local.jsFunc">
	<cfoutput>
	<script language="javascript">
		function validateCustomField() {
			var _CF_this = document.forms['frmClient'];
			var locateErr = '';
			#local.jsValidation#

			return locateErr;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.jsFunc#">