<cfcomponent>
	
	<cffunction name="getFormTypes" access="public" output="false" returntype="query">
		<cfargument name="applicationTypeID" type="numeric" required="true">

		<cfset var qryFormTypes = "">

		<cfstoredproc datasource="#application.dsn.tlasites_formbuilder.dsn#" procedure="up_getFormsTypes">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.applicationTypeID#">
			<cfprocresult name="qryFormTypes" resultset="1">
		</cfstoredproc>
				
		<cfreturn qryFormTypes>
	</cffunction>

	<cffunction name="copyForm" access="public" output="false" returntype="struct">
		<cfargument name="formID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.tlasites_formbuilder.dsn#" procedure="up_copyform">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="OUT" cfsqltype="CF_SQL_INTEGER" variable="local.newFormID">
			</cfstoredproc>
					
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteForm" access="public" returntype="struct" output="no">
		<cfargument name="formID" type="numeric" required="Yes">
	
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="up_deleteForm" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFormDetails" access="package" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="formID" type="numeric" required="true">

		<cfset var local = StructNew()>
	
		<cfstoredproc procedure="up_getForm" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
			<cfprocresult name="local.qryForm" resultset="1">
		</cfstoredproc>

		<cfif (arguments.formID GT 0 AND local.qryForm.siteID is not arguments.siteID) AND (arguments.formID GT 0 AND local.qryForm.formTypeAbbr neq 'S') >
			<cfset local.qryForm = queryNew("formID","integer")>
		</cfif>

		<cfreturn local.qryForm>
	</cffunction>

	<cffunction name="getFormSites" access="public" output="false" returntype="query">
		<cfset var qrySitesForForms = "">
	
		<cfquery name="qrySitesForForms" datasource="#application.dsn.membercentral.dsn#">
			SELECT s.siteID, s.siteCode, s.siteName
			FROM seminarWeb.dbo.tblParticipants as tp
			INNER JOIN dbo.sites s ON s.siteCode = tp.orgcode
			WHERE tp.isActive = 1
			ORDER BY s.siteCode
		</cfquery>

		<cfreturn qrySitesForForms>
	</cffunction>

	<cffunction name="saveSettings" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="formID" type="numeric" required="true">	
		<cfargument name="isPublished" type="boolean" required ="true">
		<cfargument name="formtitle" type="string" required="true">	
		<cfargument name="formIntro" type="string" required="true">
		<cfargument name="formClose" type="string" required="true">
		<cfargument name="passingPct" type="numeric" required ="true">
		<cfargument name="formTypeAbbr" type="string" required ="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.tlasites_formbuilder.dsn#" name="local.qrySaveForm">
			SET NOCOUNT ON;

			DECLARE @formID int = <cfqueryparam value="#arguments.formID#" cfsqltype="CF_SQL_INTEGER">;

			IF NOT EXISTS (select formID from dbo.tblForms where formID = @formID) BEGIN
				DECLARE @formTypeID int;
				select @formTypeID = formTypeID  from tblFormTypes WHERE formTypeAbbr = <cfqueryparam value="#trim(arguments.formTypeAbbr)#" cfsqltype="CF_SQL_CHAR">;

				INSERT INTO dbo.tblForms (formTypeID, formTitle, formIntro, formClose, passingPct, isPublished, siteID, dateCreated, dateLastModified, submitBtnText)
				VALUES (@formTypeID,
					<cfqueryparam value="#arguments.formTitle#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#arguments.formIntro#" cfsqltype="CF_SQL_LONGVARCHAR">,
					<cfqueryparam value="#arguments.formClose#" cfsqltype="CF_SQL_LONGVARCHAR">,
					<cfqueryparam value="#arguments.passingPct#" cfsqltype="CF_SQL_TINYINT">,
					<cfqueryparam value="#arguments.isPublished#" cfsqltype="CF_SQL_BIT">,
					<cfqueryparam value="#VAL(arguments.siteID)#" cfsqltype="CF_SQL_INTEGER">,
					getdate(), getdate(), 'Submit'
				);
					select @formID = SCOPE_IDENTITY();
					
				-- add generic section
				INSERT INTO dbo.tblSections (formID, sectionTitle, sectionDesc, sectionOrder)
				VALUES (@formID, '', null, 1);
			
			END ELSE
				UPDATE dbo.tblForms
				SET isPublished = <cfqueryparam value="#arguments.isPublished#" cfsqltype="CF_SQL_BIT">,
					formTitle = <cfqueryparam value="#arguments.formTitle#" cfsqltype="CF_SQL_VARCHAR">,
					formIntro = <cfqueryparam value="#arguments.formIntro#" cfsqltype="CF_SQL_LONGVARCHAR">,
					formClose = <cfqueryparam value="#arguments.formClose#" cfsqltype="CF_SQL_LONGVARCHAR">,
					passingPct = <cfqueryparam value="#arguments.passingPct#" cfsqltype="CF_SQL_TINYINT">,
					dateLastModified = getdate()
				WHERE formID = <cfqueryparam value="#VAL(arguments.formID)#" cfsqltype="CF_SQL_INTEGER">;

			select @formID as formID;
		</cfquery>

		<cfreturn local.qrySaveForm.formID>
	</cffunction>

	<cffunction name="getSection" access="public" returntype="query">
		<cfargument name="sectionID" type="numeric" required="Yes">

		<cfset var local = StructNew()>

		<cfstoredproc procedure="up_getSection" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.SectionID#">
			<cfprocresult name="local.qrySection" resultset="1">
		</cfstoredproc>

		<cfreturn local.qrySection>
	</cffunction>

	<cffunction name="saveSection" access="public" output="false" returntype="void">
		<cfargument name="formID" type="numeric" required="true">	
		<cfargument name="sectionID" type="numeric" required="true">			
		<cfargument name="sectionTitle" type="string" required="true">	
		<cfargument name="sectionDesc" type="string" required="true">	
		<cfargument name="randomizeQuestions" type="boolean" required ="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.tlasites_formbuilder.dsn#" name="local.qryUpdateSection">
			SET NOCOUNT ON;

			DECLARE @formID int = <cfqueryparam value="#arguments.formID#" cfsqltype="CF_SQL_INTEGER">;

			IF NOT EXISTS (
				select sectionID 
				from dbo.tblSections 
				where sectionID = <cfqueryparam value="#arguments.sectionID#" cfsqltype="CF_SQL_INTEGER"> 
				and formID = @formID)
			BEGIN 
				DECLARE @sectionID int;
			
				INSERT INTO dbo.tblSections (formID, sectionTitle, sectionDesc, randomizeQuestions, sectionOrder)
				VALUES (
					@formID,
					<cfqueryparam value="#trim(arguments.sectionTitle)#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#trim(arguments.sectionDesc)#" cfsqltype="CF_SQL_LONGVARCHAR">,
					<cfqueryparam value="#VAL(arguments.randomizeQuestions)#" cfsqltype="CF_SQL_BIT">,
					99
				);
				select @sectionID = SCOPE_IDENTITY();
			
				EXEC dbo.up_reorderSections @formID=@formID;
			
				UPDATE dbo.tblForms
				SET dateLastModified = getdate()
				WHERE formID = @formID;

				select @sectionID as sectionID;
			END
			ELSE
			BEGIN
				UPDATE dbo.tblSections
				SET sectionTitle = <cfqueryparam value="#trim(arguments.sectionTitle)#" cfsqltype="CF_SQL_VARCHAR">,
					sectionDesc = <cfqueryparam value="#trim(arguments.sectionDesc)#" cfsqltype="CF_SQL_LONGVARCHAR">,
					randomizeQuestions = <cfqueryparam value="#VAL(arguments.randomizeQuestions)#" cfsqltype="CF_SQL_BIT">
				WHERE sectionID = <cfqueryparam value="#arguments.sectionID#" cfsqltype="CF_SQL_INTEGER">;
			
				UPDATE dbo.tblForms
				SET dateLastModified = getdate()
				WHERE formID = @formID;
			END
		</cfquery>
	</cffunction>

	<cffunction name="doSectionMove" access="public" output="false" returntype="struct">
		<cfargument name="formID" type="numeric" required="yes">
		<cfargument name="sectionID" type="numeric" required="yes">
		<cfargument name="dir" type="string" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="up_moveSection" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sectionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeQuestion" access="public" returntype="struct" output="no">
		<cfargument name="formID" type="numeric" required="Yes">
		<cfargument name="questionID" type="numeric" required="Yes">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="up_removeQuestion" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeSection" access="public" returntype="struct" output="no">
		<cfargument name="formID" type="numeric" required="Yes">
		<cfargument name="sectionID" type="numeric" required="Yes">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="up_removeSection" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.SectionID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getQuestion" access="public" returntype="query" output="no">
		<cfargument name="questionID" type="numeric" required="Yes">
	
		<cfset var local = StructNew()>
	
		<cfstoredproc procedure="up_getQuestion" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.QuestionID#">
			<cfprocresult name="local.qryQuestion" resultset="1">
		</cfstoredproc>
	
		<cfreturn local.qryQuestion>
	</cffunction>

	<cffunction name="getQuestionTypes" access="public" returntype="query" output="no">
		<cfargument name="ft" type="string" required="Yes">
	
		<cfset var local = StructNew()>
	
		<cfstoredproc procedure="up_getQuestionTypes" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" value="#arguments.ft#">
			<cfprocresult name="local.qryQuestionTypes" resultset="1">
		</cfstoredproc>
	
		<cfreturn local.qryQuestionTypes>
	</cffunction>

	<cffunction name="checkExportLabel" access="public" output="false" returntype="struct">
		<cfargument name="formID" type="numeric" required="true" />
		<cfargument name="QuestionID" type="numeric" required="true" />
		<cfargument name="ExportLabel" type="string" required="true" />
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qrycheckExportLabel" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			select *
			from dbo.tblQuestions as q
			inner join dbo.tblSections as s on s.sectionID = q.sectionID
			where s.formID = <cfqueryparam value="#arguments.formID#" cfsqltype="cf_sql_integer" />
			and q.ExportLabel = <cfqueryparam value="#lTrim(rTrim(arguments.ExportLabel))#" cfsqltype="cf_sql_varchar" />
			and q.isEnabled = 1
			<cfif arguments.QuestionID>
				and q.questionID <> <cfqueryparam value="#arguments.QuestionID#" cfsqltype="cf_sql_integer" />
			</cfif>
		</cfquery>
		
		<cfset local.data.success = true>
		<cfset local.data.html = "true">

		<cfif local.qrycheckExportLabel.recordCount>
			<cfset local.data.html =  "false">
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getOptions" access="public" returntype="query" output="no">
		<cfargument name="questionID" type="numeric" required="Yes">
	
		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="up_getOptions" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">
			<cfprocresult name="local.qryOptions" resultset="1">
		</cfstoredproc>
	
		<cfreturn local.qryOptions>
	</cffunction>

	<cffunction name="getOptionsX" access="public" returntype="query" output="no">
		<cfargument name="questionID" type="numeric" required="Yes">
	
		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="up_getOptionsX" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">
			<cfprocresult name="local.qryOptionsX" resultset="1">
		</cfstoredproc>
	
		<cfreturn local.qryOptionsX>
	</cffunction>

	<cffunction name="saveQuestion" access="public" output="false" returntype="struct">
		<cfargument name="formID" type="numeric" required="true">	
		<cfargument name="sectionID" type="numeric" required="true">
		<cfargument name="questionID" type="numeric" required="true">			
		<cfargument name="questionTypeID" type="numeric" required="true">	
		<cfargument name="questionText" type="string" required="true">	
		<cfargument name="exportLabel" type="string" required="true">	
		<cfargument name="isRequired" type="boolean" required ="true">
		<cfargument name="displayQuestionNumber" type="boolean" required ="true">
		<cfargument name="displayQuestionText" type="boolean" required ="true">
		<cfargument name="howToAnswer" type="boolean" required ="true">
		<cfargument name="optionResponseLimit" type="numeric" required="true">	
		<cfargument name="randomizeOptions" type="boolean" required ="true">	
		<cfargument name="arrOptions" type="array" required="true">
		<cfargument name="arrOptionsx" type="array" required="true">

		<cfset var local = StructNew()>

		<cfset local.returnStruct = { "questionID":0, "errmsg":"" }>
	
		<cfparam name="arguments.randomizeOptions" default="0">
		<cfparam name="arguments.isRequired" default="0">
		<cfparam name="arguments.isDisplayedInline" default="0">
		<cfparam name="arguments.displayQuestionNumber" default="0">
		<cfparam name="arguments.howToAnswer" default="0">
		<cfparam name="arguments.optionResponseLimit" default="0">
		<cfparam name="arguments.exportLabel" default="">
		<cfparam name="arguments.displayQuestionText" default="1">

		<cftry>
			<cfquery name="local.qryOverallSave" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @formID int, @questionID int, @sectionID int, @questionTypeID int, @questionText varchar(max), @exportLabel varchar(70),
						@randomizeOptions bit, @isrequired bit, @isDisplayedInline bit, @displayQuestionNumber bit, @displayQuestionText bit,
						@controlStyle varchar(30), @optionResponseLimit int, @newQuestionID int, @optionID int, @optionText varchar(1000),
						@responseText varchar(1000), @isCorrect bit, @showInput bit, @inputText varchar(500), @optionOrder int, @newOptionID int;
					set @formID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.formID)#">;
					set @questionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.questionID)#">;
					set @sectionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.sectionID)#">;
					set @questionTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.questionTypeID)#">;
					set @questionText = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#trim(arguments.questionText)#">;
					set @exportLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.exportLabel)#">;
					set @randomizeOptions = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#val(arguments.randomizeOptions)#">;
					set @isrequired = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#val(arguments.isRequired)#">;
					set @isDisplayedInline = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#val(arguments.isDisplayedInline)#">;
					set @displayQuestionNumber = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#val(arguments.displayQuestionNumber)#">;
					set @displayQuestionText = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#val(arguments.displayQuestionText)#">;
					set @controlStyle = 'largeBox';
					<cfif listFind("4,6",val(arguments.questionTypeID))>
						<cfif arguments.howToAnswer is 0>
							set @optionResponseLimit = 9999;
						<cfelse>
							set @optionResponseLimit = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.optionResponseLimit)#">;
						</cfif>
					<cfelse>
						set @optionResponseLimit = null;
					</cfif>

					BEGIN TRAN;
						EXEC dbo.up_saveQuestion @formID=@formID, @questionID=@questionID, @sectionID=@sectionID, @questionTypeID=@questionTypeID,
							@questionText=@questionText, @exportLabel=@exportLabel, @randomizeOptions=@randomizeOptions, @isrequired=@isrequired,
							@isDisplayedInline=@isDisplayedInline, @displayQuestionNumber=@displayQuestionNumber, @displayQuestionText=@displayQuestionText,
							@controlStyle=@controlStyle, @optionResponseLimit=@optionResponseLimit, @newQuestionID=@newQuestionID OUTPUT;

						<cfif listfind("3,4,5,6,7,8,11",arguments.questionTypeID)>
							<cfif arrayLen(arguments.arrOptions)>
								<cfloop array="#arguments.arrOptions#" index="local.thisOption">
									set @optionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.thisOption.optionID)#">;
									set @optionText = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(trim(local.thisOption.optionText),1000)#">;
									set @responseText = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(trim(local.thisOption.responseText),1000)#">;
									set @isCorrect = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.thisOption.isCorrect#">;
									set @showInput = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.thisOption.showInput#">;
									set @inputText = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(trim(local.thisOption.inputText),500)#">;
									set @optionOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.thisOption.optionOrder)#">;
									set @newOptionID = null;

									EXEC dbo.up_saveOption @formID=@formID, @questionID=@newQuestionID, @optionID=@optionID, @optionText=@optionText,
										@responseText=@responseText, @isCorrect=@isCorrect, @showInput=@showInput, @inputText=@inputText, @optionOrder =@optionOrder,
										@newOptionID=@newOptionID OUTPUT;
								</cfloop>
							</cfif>
							<cfif arrayLen(arguments.arrOptionsx)>
								<cfloop array="#arguments.arrOptionsx#" index="local.thisOption">
								set @optionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.thisOption.optionID)#">;
								set @optionText = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(trim(local.thisOption.optionText),1000)#">;
								set @newOptionID = null;

								EXEC dbo.up_saveOptionX @formID=@formID, @questionID=@newQuestionID, @optionID=@optionID, @optionText=@optionText, 
									@newOptionID=@newOptionID OUTPUT;
								</cfloop>
							</cfif>
						</cfif>
					COMMIT TRAN;

					select @newQuestionID as newQuestionID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.questionID = local.qryOverallSave.newQuestionID>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"message") and findNoCase("Column label must be unique", cfcatch.message)>
				<cfset local.returnStruct.errmsg = "Column label must be unique.">
			<cfelse>
				<cfset local.returnStruct.errmsg = cfcatch.message>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="removeOption" access="public" returntype="struct" output="no">
		<cfargument name="formID" type="numeric" required="Yes">
		<cfargument name="optionID" type="numeric" required="Yes">
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="up_removeOption" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.optionID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>	

	<cffunction name="removeOptionX" access="public" returntype="struct" >
		<cfargument name="formID" type="numeric" required="Yes">
		<cfargument name="optionID" type="numeric" required="Yes">
		<cfset var local = structNew()>
		<cftry>
			<cfstoredproc procedure="up_removeOptionX" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.optionID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>	
		<cfreturn local.data>
	</cffunction>

	<cffunction name="prefillOptionsX" access="public" returntype="struct" output="no">
		<cfargument name="questionID" type="numeric" required="Yes">
		<cfargument name="prefillType" type="string" required="Yes">
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="up_prefillOptionsX" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.QuestionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.prefillType#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="prefillOptions" access="public" returntype="struct" output="no">
		<cfargument name="questionID" type="numeric" required="Yes">
		<cfargument name="prefillType" type="string" required="Yes">
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="up_prefillOptions" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.QuestionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.prefillType#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="sortOptions" access="public" returntype="struct" output="no">
		<cfargument name="formID" type="numeric" required="Yes">
		<cfargument name="questionID" type="numeric" required="Yes">
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="up_sortOptions" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>	
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="sortOptionsX" access="public" returntype="struct" output="no">
		<cfargument name="formID" type="numeric" required="Yes">
		<cfargument name="questionID" type="numeric" required="Yes">
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="up_sortOptionsX" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>	
		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveNewOption" access="public" returntype="struct" output="no">
		<cfargument name="formID" type="numeric" required="Yes">
		<cfargument name="questionID" type="numeric" required="Yes">
		<cfargument name="optionText" type="string" required="Yes">
		<cfset var local = structNew()>
		<cftry>		
			<cfset saveOption(arguments.formID,arguments.questionID,0,left(trim(arguments.OptionText),1000),'',0,0,'',0)>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>	
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="saveNewOptionX" access="public" returntype="struct" output="no">
		<cfargument name="formID" type="numeric" required="Yes">
		<cfargument name="questionID" type="numeric" required="Yes">
		<cfargument name="optionText" type="string" required="Yes">
		<cfset var local = structNew()>
		<cftry>
			<cfset saveOptionX(arguments.formID,arguments.questionID,0,left(trim(arguments.OptionText),100))>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>	
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="saveOption" access="private" returntype="void" output="no">
		<cfargument name="formID" type="numeric" required="yes">
		<cfargument name="questionID" type="numeric" required="yes">
		<cfargument name="optionID" type="numeric" required="yes">
		<cfargument name="optionText" type="string" required="yes">
		<cfargument name="responseText" type="string" required="yes">
		<cfargument name="isCorrect" type="boolean" required="yes">
		<cfargument name="showInput" type="boolean" required="yes">
		<cfargument name="inputText" type="string" required="yes">	
		<cfargument name="optionOrder" type="numeric" required="yes">
	
		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="up_saveOption" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.optionID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.optionText#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.responseText#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isCorrect#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.showInput#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.inputText#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.optionOrder#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newOptionID">
		</cfstoredproc>
	</cffunction>

	<cffunction name="saveOptionX" access="private" returntype="void" output="no">
		<cfargument name="formID" type="numeric" required="yes">
		<cfargument name="questionID" type="numeric" required="yes">
		<cfargument name="optionID" type="numeric" required="yes">
		<cfargument name="optionText" type="string" required="yes">
	
		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="up_saveOptionX" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.optionID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.optionText#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newOptionID">
		</cfstoredproc>
	</cffunction>

	<cffunction name="removeResponse" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="Yes">
		<cfargument name="formID" type="numeric" required="Yes">
		<cfargument name="rid" type="numeric" required="Yes">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.strResponse = getResponse(siteID=arguments.mcproxy_siteID, formID=arguments.formID, responseID=arguments.rid)>
			<cfif local.strResponse.isValidResponse>
				<cfif local.strResponse.qryResponse.signupSiteID eq arguments.mcproxy_siteID OR application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					<cfstoredproc procedure="up_removeResponses" datasource="#application.dsn.tlasites_formbuilder.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.rid#">
					</cfstoredproc>
					<cfset local.data.success = true>
				<cfelse>
					<cfthrow>
				</cfif>
			<cfelse>
				<cfthrow>
			</cfif>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doOptionMove" access="public" output="false" returntype="struct">
		<cfargument name="formID" type="numeric" required="yes">
		<cfargument name="questionID" type="numeric" required="yes">
		<cfargument name="optionID" type="numeric" required="yes">
		<cfargument name="dir" type="string" required="yes">

		<cfset var local = structNew()>
		<cftry>
			<cfstoredproc procedure="up_moveOption" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.optionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doConvertQuestionType" access="public" output="false" returntype="struct">
		<cfargument name="questionID" type="numeric" required="yes">
		<cfargument name="questionTypeID" type="numeric" required="yes">
		<cfargument name="newQuestionTypeID" type="numeric" required="yes">
		<cfset var local = structNew()>
		<cftry>
			<cfstoredproc procedure="up_changeQuestionType" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.questionTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.newQuestionTypeID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getResponse" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="formID" type="numeric" required="true">
		<cfargument name="responseID" type="numeric" required="true">

		<cfset var local = StructNew()>
		<cfset local.strReturn = { isValidResponse=false }>
	
		<!--- this will verify form belongs to site and response belongs to form --->
		<cfset local.strReturn.qryForm = getFormDetails(siteID=arguments.siteID, formID=arguments.formID)>
		<cfif local.strReturn.qryForm.recordcount is 1>
			<cfstoredproc procedure="up_getResponseByResponseID" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.responseID#">
				<cfprocresult name="local.strReturn.qryResponse" resultset="1">
				<cfprocresult name="local.strReturn.qrySections" resultset="2">
				<cfprocresult name="local.strReturn.qryQuestions" resultset="3">
				<cfprocresult name="local.strReturn.qryResponseDetail" resultset="4">
			</cfstoredproc>
			<cfif local.strReturn.qryResponse.recordcount is 1>
				<cfset local.strReturn.isValidResponse = true>
			</cfif>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getFormPreviewDetails" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="formID" type="numeric" required="true">

		<cfset var local = StructNew()>
		<cfset local.strReturn = { isValidForm=false }>
	
		<!--- this will verify form belongs to site  --->
		<cfset local.strReturn.qryForm = getFormDetails(siteID=arguments.siteID, formID=arguments.formID)>
		<cfif local.strReturn.qryForm.recordcount is 1>
			<cfstoredproc procedure="up_getFormDetails" datasource="#application.dsn.tlasites_formbuilder.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">				
				<cfprocresult name="local.strReturn.qrySections" resultset="1">
				<cfprocresult name="local.strReturn.qryQuestions" resultset="2">
				<cfprocresult name="local.strReturn.qryoptions" resultset="3">				
				<cfprocresult name="local.strReturn.qryoptionsx" resultset="4">
			</cfstoredproc>
			
			<cfset local.strReturn.isValidForm = true>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getEvaluationContextFromNav" access="package" output="false" returntype="struct">
		<cfargument name="navigationID" type="numeric" required="true">

		<cfset var strReturn = structNew()>

		<cfset strReturn.parentNavigationID = XMLSearch(application.adminNavigationXML,"string(//navitem[@navigationID=#arguments.navigationID#]/../@navigationID)")>
		<cfset strReturn.parentNavigationName = XMLSearch(application.adminNavigationXML,"string(//navitem[@navigationID=#arguments.navigationID#]/../@navName)")>
		<cfif strReturn.parentNavigationName EQ 'SeminarWeb'>
			<cfset strReturn.applicationTypeID = application.objApplications.getApplicationTypeIDFromName(applicationTypeName='SemWebCatalog')>
			<cfset strReturn.contextTitle = 'Evaluations/Exams'>
			<cfset strReturn.breadcrumb = 'SeminarWeb: Evaluations/Exams'>
		<cfelse>
			<cfset strReturn.applicationTypeID = application.objApplications.getApplicationTypeIDFromName(applicationTypeName='Events')>
			<cfset strReturn.contextTitle = 'Evaluations'>
			<cfset strReturn.breadcrumb = 'Events: Evaluations'>
		</cfif>

		<cfreturn strReturn>
	</cffunction>

</cfcomponent>