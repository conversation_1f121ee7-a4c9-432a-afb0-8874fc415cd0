<cfscript>
	variables.applicationReservedURLParams = "TestMode";
	local.customPage.baseURL				= "/?#getBaseQueryString(false)#";
	
	// PAGE CUSTOM FIELDS-----------------------------------------------------------------------------------------------------
	local.arrCustomFields = [];
	local.tmpField = { name="joinOnlineNonAttorneyActiveSubscriptionError", type="STRING", desc="Error Message for already Active Subscription", value='Records indicate you are currently a member. Please click <a href="/?pg=login">here</a> to log in.' }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="joinOnlineNonAttorneyBilledSubscriptionError", type="STRING", desc="Error Message for existing Billed Subscription", value="You need to renew your membership via the 'managesubscribers' page." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="DuesInformation", type="CONTENTOBJ", desc="Explanation of dues and membership requirements", value="*A valid email address is required for membership. Law Student / Graduate Applicant" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="confirmationEmailTxt", type="CONTENTOBJ", desc="confirmation email text", value="<b>Your membership term will begin after application review to ensure you meet the eligibility requirements. An email notification will be sent once the membership account has been approved and credit card has been processed.</b>" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="screenConfirmationTxt", type="CONTENTOBJ", desc="screen confirmation text", value="<b>Your membership term will begin after application review to ensure you meet the eligibility requirements. An email notification will be sent once the membership account has been approved and credit card has been processed.</b>" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send member confirmations from", value='<EMAIL>'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send staff confirmations to", value='<EMAIL>,<EMAIL>'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListServeAgreement", type="CONTENTOBJ", desc="CAALA List Agreement", value="" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListServeTopInfoText", type="CONTENTOBJ", desc="List Serve Top Info Text", value="To join and participate in a CAALA List Serve you must maintain current status as an attorney member of the Association. Once your membership has been verified, you will receive a notification and instructions via e-mail and you will be able to send and receive e-mail messages from the CAALA LIST SERVE(s) to which you have subscribed." };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListServeAddText", type="STRING", desc="Text displayed for add List Subscription", value='Please choose which of the following List Serves you would like to subscribe to:'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListSubscriptionAddons", type="STRING", desc="Available List Subscriptions that Display on the Join Form", value='8130FFBF-E48F-4B5E-AE48-F32404F71DCF'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AvailableListSubscriptions", type="STRING", desc="Name of list subscriptions available separated by |", value='CAALA-WOMEN,CAALA-LEGALSTAFF'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListAgreementTitle", type="STRING", desc="List Serves Agreement Title", value='List Serve Subscriptions'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ListAgreementFields", type="STRING", desc="Fields that display when a List Subscription is checked", value='7BB927E0-571E-4686-AE53-554A2DE6F2D0'}; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.customPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
		
	// SET PAGE DEFAULTS: ----------------------------------------------------------------------------------------------------
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=event,
		formName='frmJoinNonAttorney',
		formNameDisplay='Membership Form',
		orgEmailTo = local.customPageFields.ConfirmationEmailStaffTo,
		memberEmailFrom = local.customPageFields.ConfirmationEmailStaffFrom
	));

	local.qryGender = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID,columnName="gender");
	
	local.qryParalegalRates  =  application.objCustomPageUtils.sub_getRate( siteID = local.siteID, rateUID ='F00411C1-F839-4BEC-B571-E1B1C30C2558');
		
	// GATEWAY INFORMATION: --------------------------------------------------------------------------------------------------
	local.profile_1._profileCode 	= 'AUTHCIM';
	local.profile_1._profileID 		= application.objCustomPageUtils.acct_getProfileID(siteid=local.siteID,profileCode=local.profile_1._profileCode);
	
	// GET MEMBER DATA: ------------------------------------------------------------------------------------------------------
	local.memberData 				= getMemberData(orgID=local.orgID,siteID=local.siteID,memberID=local.memberID);

	// SUBSCRIPTIONS: --------------------------------------------------------------------------------------------------------
	local.objSubs					= CreateObject('component','model.admin.subscriptions.subscriptions');
	
	//INIT DUES STRUCT PER TYPE-------------------------------------------------------------------------------------------------
	local.strDues = structNew();
		
	local.strDues.paralegal 	= structNew();
	local.strDues.paralegal.txt = 'Legal Staff';
	
	local.strDues.student = structNew();
	local.strDues.student.txt = 'Law Student / Graduate Applicant';
	local.strDues.student.amt = 0;
	
	local.isPaymentRequired = false;
	
	if(event.getValue('memberCategory',0) GT 0) {
		switch(event.getValue('memberCategory')){					
			case '1':  local.membershipCategory 	= local.strDues.paralegal.txt; local.membershipDues = local.qryParalegalRates.rateAmt; 	break;
			case '2':  local.membershipCategory 	= local.strDues.student.txt; local.membershipDues = local.strDues.student.amt; 	break;
		}
		local.totalAmount = local.membershipDues;
		if(local.totalAmount GT 0) {
			local.isPaymentRequired = true;
		}				
		if(NOT local.isPaymentRequired) {
			event.setValue('isSubmitted',2);
		}
	}
	local.listServeCertAgreePrefFieldSetUID = local.customPageFields.ListAgreementFields;
	local.lawStudentIDFieldSetUID = "F5FD8F9E-DBAD-4938-8A4C-F1FCE1EE85AB";
	local.qryOrgMemberFields = application.objCustomPageUtils.getOrgMemberFields(orgID=local.orgID);
	if(local.qryOrgMemberFields.usePrefixList is 1)
		local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.orgID)
</cfscript>
<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
			##paymentTable.control-group { font-size: 11pt; }
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##dbdedf; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:11pt; color:##000; }
			.frmButtons{ padding:5px 0; border-top:3px solid ##03608B; border-bottom:3px solid ##03608B; }
			
			.TitleText { font-family:Tahoma; font-size:16pt; color:##000; font-weight:bold; padding: 15px 0px;}
			.CPSection{ border:0.5px solid ##56718c; margin-bottom:15px; }
			.CPSectionTitle { font-size:12pt; height:auto; font-weight:bold; color:##000; padding:10px; background:##bbb; }
			.CPSectionContent{ padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##ececec; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##ececec; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##ececec;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			.subCPSectionText { font-size:8.5pt; }
			
			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}
			
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b { font-weight:bold; }
			
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			
			.BB { border-bottom:0.5px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			
			.tsAppBodyText { color:##000000;}
			select.tsAppBodyText{color:##666;}
			
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			.form-horizontal .control-group { margin-bottom: 5px; }
			.marginLeftZero {margin-left: 0px !important;}
			@media screen and (max-width: 979px){
				.input-xxlarge { width:100% !important; }
			}	
			.frmField { padding-left:20px; padding-right:10px;}
			label.control-label.label-width { width: 100%; text-align: left; }
			.txtSize .control-label { text-align: left;width: 100%; }
			.txtSize .controls { margin-left: 0px; } 
			.row-width{ padding:5px 0px } 
			.span6.pLeft { padding-left: 20px; } 
			.centerit { position: absolute; top: 50%; margin-left: 15%; } 
			.centerit button { position: relative; top: -15px; height: 30px; } 
			.center-holder{ position: relative; } 
			.accntPopup > div:last-child > div.span12{margin-left: 2.564102564102564%;} 
			.accntPopup > div:first-child > div { left:40%; position:absolute; } 
			@media only screen and (max-width:767px){ 
			.span6,.span5{ display:inline!important; width:50%!important; float:left!important; } 
			} 
			@media only screen and (max-width:560px){ 
			.accntPopup > div:first-child > div { left:25%; } 
			} 
			@media only screen and (max-width:400px){ 
			.accntPopup > div:first-child > div { left:12%; } 
			} 
			@media only screen and (max-width:345px){ .accntPopup > div:first-child > div { left:1%; } 
			.accntPopup > div:first-child > div button{ font-size:11px; } 
			} 
			@media screen and (max-width: 359px){ 
			.centerit{margin-left: 1%;} 
			} 
			@media screen and (max-width: 767px){ 
			.r.span3{ text-align: left; padding-left: 20px; } 
			.centerit{margin-left: 10%;} 
			} 
			@media screen and (max-width: 368px){ .centerit{margin-left: 1%;} }
			##graduationdate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }
		</style>
	</cfsavecontent>
	<cfsavecontent variable="local.pageJS">
		#local.pageJS#
		<script  type="text/javascript">
			function alignAcntLookUpBox(){
				var _heightRight = $('.accntPopup > div:last-child').height();
				var _top = _heightRight / 2 - 10;
				$('.accntPopup > div:first-child').height(_heightRight);
				$('.accntPopup > div:first-child > div').css('top',_top+'px');
			}
			$(function() {
				alignAcntLookUpBox();
				$(window).on("resize load",function(){
					alignAcntLookUpBox();
				});
			});
		</script>
	</cfsavecontent>
	#local.pageJS#
	#local.pageCSS#
	
	<div id="customPage" class="row-fluid">
		<div class="TitleText c row-fluid" style="padding-bottom:15px;">#local.Organization# - #local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<!--- FORM: --->
			<cfcase value="0">
				<cfset local.qryListSubData =  application.objCustomPageUtils.sub_getSubscriptionsDetailsFromTypeUID(subTypeUID='#local.customPageFields.ListSubscriptionAddons#',siteID=local.siteID)/>
				<cfset local.listServeCertAgreePrefFieldSet = application.objCustomPageUtils.renderFieldSet(uid=local.listServeCertAgreePrefFieldSetUID, mode="collection", strData={})>
				<cfset local.lawStudentIDFieldSet = application.objCustomPageUtils.renderFieldSet(uid=local.lawStudentIDFieldSetUID, mode="collection", strData={})>
				<cfset local.qryLawStudentID = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Law Student ID")>
				<cfset local.qryStates	= application.objCommon.getStates()>
				<script type="text/javascript">
					$(function() {
						$(window).on("resize load", function() {
							var windowWidth = $(window).width();
							if(windowWidth < 585) {
								$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
							} else{
								$.colorbox.resize({innerWidth:550, innerHeight:330});
							}
						});
					});
					function selectMember() {
						var windowWidth = $(window).width();
						var _popupWidth = 550;
						if(windowWidth < 585) {
							_popupWidth = windowWidth - 30;
						} 
						$.colorbox( {innerWidth:_popupWidth, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
					}
					function resizeBox(newW,newH) { 
						var windowWidth = $(window).width();
						var _popupWidth = newW;
						if(windowWidth < 585) {
							_popupWidth = windowWidth - 30;
						}
						$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
					}
					function addMember(memObj) {
						$.colorbox.close();
						assignMemberData(memObj);
					}
					function _FB_validateForm() {
						var _CF_this = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						
						if (!_FB_hasValue(_CF_this['title'], 'RADIO')) arrReq[arrReq.length] 																= 'Please select a Title';
						if (!_FB_hasValue(_CF_this['firstName'], 'TEXT')) arrReq[arrReq.length] 															= 'Please enter a First Name';
						if (!_FB_hasValue(_CF_this['lastName'], 'TEXT')) arrReq[arrReq.length] 																= 'Please enter a Last Name';
						if (!_FB_hasValue(_CF_this['email'], 'TEXT')) arrReq[arrReq.length] 																= 'Please enter an Email Address';
						
						if (!_FB_hasValue(_CF_this['address1'], 'TEXT')) arrReq[arrReq.length] 																= 'Please enter an Address';
						if (!_FB_hasValue(_CF_this['city'], 'TEXT')) arrReq[arrReq.length] 																	= 'Please enter a City';
						if (!_FB_hasValue(_CF_this['state'], 'TEXT')) arrReq[arrReq.length] 																= 'Please enter a State';
						if (!_FB_hasValue(_CF_this['zip'], 'TEXT')) arrReq[arrReq.length] 																	= 'Please enter a Zip Code';
						
						if (!_FB_hasValue(_CF_this['phone'], 'TEXT')){
							arrReq[arrReq.length] 	= 'Please enter a Phone number';
						}else if(!checkPhFormat(_CF_this['phone'])){
							arrReq[arrReq.length] = 'Enter phone number, formatted xxx-xxx-xxxx (e.g. ************)';
						}
						if ($('##gender').val().length == 0) arrReq[arrReq.length] 		= 'Please select your Gender.';								

						if (!_FB_hasValue(_CF_this['memberCategory'], 'RADIO')) arrReq[arrReq.length] 											= 'Please select a Membership Category';
						if ( getSelectedRadio(document.getElementsByName('memberCategory')) == 0 ){
							if (!_FB_hasValue(_CF_this['paralegalLawFirm'], 'TEXT')) arrReq[arrReq.length] 										= 'Please enter a Law Firm/Office';
						}
						if ( getSelectedRadio(document.getElementsByName('memberCategory')) == 1 ){
							if (!_FB_hasValue(_CF_this['lawSchool'], 'TEXT')) arrReq[arrReq.length] 											= 'Please enter a Law School';
							if (!_FB_hasValue(_CF_this['graduationdate'], 'TEXT')) arrReq[arrReq.length] 											= 'Please enter a Graduation Date';
						}
						if( $('input[name=listServeNames]:checkbox:checked').length > 0 ) {						
							#local.listServeCertAgreePrefFieldSet.jsValidation#
						}
						if ( getSelectedRadio(document.getElementsByName('memberCategory')) == 1 ){
							#local.lawStudentIDFieldSet.jsValidation#
						}

						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
					function checkPhFormat(x){
						var numberEntered = x.value.toString();
						var pattern = /\d{3}-\d{3}-\d{4}/;
						var match = pattern.test(numberEntered);
						
						if ( match == false ){
							return false;
						} else {
							return true;
						}
					}
					function assignMemberData(memObj){
						var _CF_this = document.forms["#local.formName#"];
						var er_change = function(r) {
							var results = r;
							if( results.success ){
								_CF_this['memberNumber'].value 	= results.membernumber;
								_CF_this['memberID'].value 			= results.memberid;

								_CF_this['firstName'].value 		= results.firstname;
								_CF_this['lastName'].value 			= results.lastname;
								_CF_this['firmName'].value			= results.company;
								_CF_this['address1'].value 			= results.address1;
								_CF_this['city'].value 					= results.city;
								_CF_this['state'].value 				= results.statecode;
								_CF_this['zip'].value 					= results.postalcode;
								_CF_this['phone'].value 				= results.phone;
								_CF_this['email'].value 				= results.email;

								if ( results.prefix != '' ){
									$("input[value='"+results.prefix+"']").prop('checked',true);
								}	
								titleChange();
								if ( results.memberid > 1 ){ 
									AJAXCheckSubs(results.memberid , 'A');
								}
									
								// un hide form   
								document.getElementById('formToFill').style.display 			= '';
							} else{ /*alert('not success');*/ }
						};
						/************************************************************************************************/
						var objParams = { memberNumber:memObj.memberNumber};
						TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
					}
					function AJAXCheckSubs(member, status){
						$('##hasDuesSubs').hide();
						var _status = status;
						var stopProgress = function(r){
						var isActiveSubs= false;
						var isBilledSubs= false;
							if ( r.data.typename.length ){
								$.each(r.data.typename,function(x){
										if ( r.data.typename[x] == 'Membership Dues' ){
											$('##formToFill').hide();
											if(_status == 'A'){
												isActiveSubs = true;
											} else {
												isBilledSubs = true;
											}
										}
								});
							}
							if(!isActiveSubs && _status == 'A' ) {
								AJAXCheckSubs(member,'O');
							} else if(!isBilledSubs && _status == 'O') {
								return true;
							} else if(isActiveSubs) {
								$('##hasDuesSubsMessage').html("#ReplaceNoCase(local.customPageFields.joinOnlineNonAttorneyActiveSubscriptionError,'"',"'","ALL")#");
								$('##hasDuesSubs').show();
								return false;
							} else if(isBilledSubs){
								$('##hasDuesSubsMessage').html("#ReplaceNoCase(local.customPageFields.joinOnlineNonAttorneyBilledSubscriptionError,'"',"'","ALL")#");
								$('##hasDuesSubs').show();
								return false;
							}
						};
						var params = { memberID:member, status:_status, distinct: true };
						TS_AJX('CUSTOM_FORM_UTILITIES','sub_getSubscriptions',params,stopProgress);
					}
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}
					function checkURL (abc) {
						var string = abc.value;
						if (!~string.indexOf("http") && string.length > 0){
							string = "http://" + string;
						}
						abc.value = string;
						return abc
					}
					function duesFields(){
						if ( getSelectedRadio(document.getElementsByName('memberCategory')) == 0 ){
							$('##paralegalLawFirm').attr('disabled',false);
							$('##lawSchool').attr('disabled',true).val('');
							$('##graduationdate').attr('disabled',true).val('');
							hideShowSubscribeMe('CAALA-LEGALSTAFF',false);	
						}
						else if ( getSelectedRadio(document.getElementsByName('memberCategory')) == 1 ){	
							$('##lawSchool').attr('disabled',false);
							$('##graduationdate').attr('disabled',false);
							$('##paralegalLawFirm').attr('disabled',true).val('');
							hideShowSubscribeMe('CAALA-LEGALSTAFF',true);
						}
						else{
							$('##paralegalLawFirm').attr('disabled',true).val('');
							$('##lawSchool').attr('disabled',true).val('');
							$('##graduationdate').attr('disabled',true).val('');
							hideShowSubscribeMe('CAALA-LEGALSTAFF',false);
						}
					}
					function titleChange(){
						var selectedTitle = $("input[name='title']:checked").val();
						if (selectedTitle === "Mr."){
							hideShowSubscribeMe('CAALA-WOMEN',true);					
						}else{
							hideShowSubscribeMe('CAALA-WOMEN',false);
						}
					}
					function hideShowSubscribeMe(val,isHide){
						var checkbox = $('input[name="listServeNames"][value="'+val+'"]');
						if(isHide){
							if (checkbox.is(':checked')) {
								checkbox.trigger("click");
							}
							checkbox.prop('checked', false).parent().parent().parent().hide();
							if($("input[name='title']:checked").val()== "Mr." && getSelectedRadio(document.getElementsByName('memberCategory')) == 1){
								$("##listServeNameHolder").hide();
							}
							else {
								$("##listServeNameHolder").show();
							}
						}else{
							checkbox.parent().parent().parent().show();
							$("##listServeNameHolder").show();
						}
					}
					$(document ).ready(function() {	
						var $listServeAgreementBox = $('##listServeAgreementBox');
						$listServeAgreementBox.hide();
					
						$('input[name=listServeNames]').click(
							function() {
								if ($('input[name=listServeNames]:checkbox:checked').length > 0) {
									$listServeAgreementBox.show();									
								} else {
									$listServeAgreementBox.hide();						
								}
							}	
						);
						
						$(document).on('change','input[name="title"]',function(){
							titleChange();
						});
						mca_setupDatePickerField('graduationdate');
						$("##btnClearbarDate").click( function(e) { mca_clearDateRangeField('graduationdate'); } );
						$(".lawStudentIDFieldSetHolder .tsAppSectionContentContainer td.tsAppBodyText:last").html('<input type="file" name="md_#local.qryLawStudentID.COLUMNID#" id="md_#local.qryLawStudentID.COLUMNID#" value="Select file">');
					});	
				</script>
				<div class="r i frmText">*Denotes required field</div>
				<cfform name="#local.formName#"  id="#local.formName#" class="form-horizontal" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();" enctype="multipart/form-data">
					<input type="hidden" name="isSubmitted" value="1" />
					<input type="hidden" name="memberID" value="#session.cfcUser.memberData.memberID#" />
					<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />
					
					<!--- ACCOUNT LOCATOR: ============================================================================================================================== --->
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection row-fluid">
							<div class="CPSectionTitle BB row-fluid">Account Lookup / Create New Account</div>
							<div class="frmRow1 row-fluid" style="padding:10px;">
								<div class="row-fluid accntPopup">
									<div class="span5 c" style="position:relative">
										<div>
											<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
										</div>
									</div>
									<div class="span6 frmText">
										<div class="span12">Click the <span class="b">Account Lookup</span> button to the left.</div>
										<div class="span12">Enter the search criteria and click <span class="b">Continue</span>.</div>
										<div class="span12">If you see your name, please press the <span class="b">Choose</span> button next to your name.</div>
										<div class="span12">If you do not see your name, click the <span class="b">Create an Account</span> link.</div>
									</div>
								</div>
							</div>
						</div>
					</cfif>
					
					<div id="hasDuesSubs" style="display:none;">
						<div class="CPSection row-fluid">
							<div class="CPSectionTitle BB row-fluid">Alert!</div>
							<div class="frmRow1 row-fluid" style="padding:10px;">
								<div class="row-fluid center-holder">
									<div class="span12" id="hasDuesSubsMessage"></div>
								</div>
							</div>
						</div>
					</div>
						
					<div id="formToFill" <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>style="display:none;"<cfelse>style="display:;"</cfif>>
					
						<!--- PERSONAL INFORMATION: ========================================================================================================================= --->
						<div class="CPSection row-fluid">
							<div class="CPSectionTitle BB row-fluid">Personal Information</div>
							<div id="sectionContent" class="tsAppBodyText frmRow1 frmText CPSectionContent P">
								<div class="control-group">
									<label class="control-label" for="title">* Title:</label>
									<div class="controls">
										<label class="radio inline" for="title">
											<input name="title" type="radio" value="Mr." class="tsAppBodyText" <cfif #session.cfcUser.memberData.prefix# eq 'Mr.'>selected="true"</cfif> />Mr. &nbsp;&nbsp;
										</label>
										<label class="radio inline" for="title">
											<input name="title" type="radio" value="Mrs." class="tsAppBodyText" <cfif #session.cfcUser.memberData.prefix# eq 'Mrs.'>selected="true"</cfif> />Mrs. &nbsp;&nbsp;
										</label>
										<label class="radio inline" for="title">
											<input name="title" type="radio" value="Ms." class="tsAppBodyText" <cfif #session.cfcUser.memberData.prefix# eq 'Ms.'>selected="true"</cfif> />Ms. &nbsp;&nbsp;
										</label>
										<label class="radio inline marginLeftZero" for="title">
											<input name="title" type="radio" value="Dr." class="tsAppBodyText" <cfif #session.cfcUser.memberData.prefix# eq 'Dr.'>selected="true"</cfif> />Dr. &nbsp;&nbsp;
										</label>
										<label class="radio inline marginLeftZero" for="title">
											<input name="title" type="radio" value="Honorable" class="tsAppBodyText" <cfif #session.cfcUser.memberData.prefix# eq 'Honorable'>selected="true"</cfif> />Honorable &nbsp;&nbsp;
										</label>
									</div>
								</div>	
								<div class="control-group">
									<label class="control-label" for="firstName">* First Name:</label>
									<div class="controls">
										<input class="input-xlarge tsAppBodyText" size="40" name="firstName" id="firstName" type="text" value="#session.cfcUser.memberData.firstname#" >
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="middleName">Middle Initial:</label>
									<div class="controls">
										<input size="10" name="middleName" type="text" value="" class="tsAppBodyText input-medium" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="lastName">* Last Name:</label>
									<div class="controls">
										<input size="40" name="lastName" type="text" value="#session.cfcUser.memberData.lastname#" class="tsAppBodyText input-xlarge" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="firmName">Firm/Business Name:</label>
									<div class="controls">
										<input size="60" name="firmName" type="text" value="#session.cfcUser.memberData.Company#" class="tsAppBodyText input-xxlarge" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="email">* Email:</label>
									<div class="controls">
										<input size="60" name="email" type="text" value="#session.cfcUser.memberData.email#" class="tsAppBodyText input-xxlarge" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="address1">* Address Line 1:</label>
									<div class="controls">
										<cfinput class="input-xxlarge tsAppBodyText" size="60" name="address1" id="address1" type="text" value="#local.data.address.address1#" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="city">* City:</label>
									<div class="controls">
										<cfinput class="input-xlarge tsAppBodyText" size="25" name="city" id="city" type="text" value="#local.data.address.city#" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="state">* State:</label>
									<div class="controls">
										<cfselect class="tsAppBodyText largeBox" name="state" id="state">
											<option value=""> - Please Select - </option>
											<cfloop query="local.qryStates">
												<cfif local.qryStates.country eq "United States">
													<option value="#local.qryStates.StateCode#" <cfif local.qryStates.StateCode eq local.data.address.stateCode>selected</cfif>>#local.qryStates.stateName#</option>
												</cfif>
											</cfloop>
										</cfselect>
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="zip">* Zip:</label>
									<div class="controls">
										<cfinput class="input-medium tsAppBodyText" size="10" maxlength="15" name="zip" id="zip" type="text" value="#local.data.address.postalCode#" />
									</div>
								</div>	
								<div class="control-group">
									<label class="control-label" for="phone">* Phone:</label>
									<div class="controls">
										<input size="13" maxlength="13" placeholder="xxx-xxx-xxxx" name="phone" type="text" value="#local.data.phone.phone#" class="tsAppBodyText input-xlarge" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="website">Website:</label>
									<div class="controls">
										<cfinput value="" class="tsAppBodyText input-xxlarge" name="website"  id="website" type="text" size="50" validate="regular_expression"  onblur="checkURL(this)" pattern="#application.regEx.url#" message="Please enter a valid website address.\r\n   Example: http://www.websitename.com" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="referredBy">Referred By:</label>
									<div class="controls">
										<cfinput class="input-xxlarge tsAppBodyText" size="60" name="referredBy" id="referredBy" type="text" value="" />
									</div>
								</div>
								<div class="control-group">
									<label class="control-label" for="gender">* Gender:</label>
									<div class="controls">
										<select name="gender" id="gender">
											<option></option>
											<cfoutput><cfloop array="#local.qryGender.columnValueArr#" index="local.qryGender">
												<option value="#local.qryGender.valueid#">#local.qryGender.columnValueString#</option>
											</cfloop></cfoutput>
										</select>
									</div>
								</div>
							</div>						
						</div>
						
						<div class="CPSection row-fluid">
							<div class="CPSectionTitle BB row-fluid">* Membership Dues</div>
							<div id="sectionContent" class="tsAppBodyText frmText row-fluid CPSectionContent P">
								<div class="row-fluid frmRow1">
									<div class="span9">
										<label class="radio inline" for="memberCategoryParalegal"><input type="radio" name="memberCategory" id="memberCategoryParalegal" value="1" onclick="duesFields();">#local.strDues.paralegal.txt#</label>
									</div>	
									<div class="r span3">#dollarFormat(local.qryParalegalRates.rateAmt)#</div>
								</div>
								<div class="row-fluid control-group">
									<div class="span12 row-fluid">									
										<div class="span6 pLeft">								
											<label class="control-label label-width" for="paralegalLawFirm">Law Firm/Office (must be employed by an attorney member of CAALA):</label>
										</div>			
										<div class="controls span6 frmField">
											<input type="text" name="paralegalLawFirm" id="paralegalLawFirm" size="40" value="" disabled="true" class="tsAppBodyText input-xlarge">
										</div>
									</div>
								</div>	
								<div class="row-fluid frmRow1">
									<div class="span9">
										<label class="radio inline" for="memberCategoryParalegal"><input type="radio" name="memberCategory" value="2" onclick="duesFields();">#local.strDues.student.txt#**</label>
									</div>	
									<div class="r span3">#dollarFormat(local.strDues.student.amt)#</div>
								</div>
								<div class="row-fluid control-group">
									<div class="span12 row-fluid">									
										<div class="span6 pLeft">								
											<label class="control-label label-width" for="lawSchool">Law School Name: </label>
										</div>	
										<div class="controls span6 frmField">
											<input type="text" name="lawSchool" id="lawSchool" size="40" value="" disabled="true" class="tsAppBodyText input-xlarge" />
										</div>
									</div>
								</div>
								<div class="row-fluid control-group">
									<div class="span12 row-fluid">									
										<div class="span6 pLeft">								
											<label class="control-label label-width" for="graduationdate">Expected Graduation Date: </label>
										</div>	
										<div class="controls span6 frmField">
											<input type="text" name="graduationdate" id="graduationdate" size="16"  autocomplete="off" value=""  disabled="true" class="tsAppBodyText input-xlarge" />
											<input type="button" class="tsAppBodyButton" name="btnClearbarDate"  id="btnClearbarDate" value="clear" />
										</div>
									</div>
								</div>
								
							</div>
						</div>
						
						<cfif NOT structIsEmpty(local.qryLawStudentID)>
						<div class="CPSection row-fluid">
							<div id="lawStudentID" class="tsAppBodyText frmRow1 frmText CPSectionContent P">
								<div class="row-fluid">
									<span class="lawStudentIDFieldSetHolder">
										<div class="row-fluid">
											<div class="tsAppSectionHeading">#local.lawStudentIDFieldSet.fieldSetTitle#</div>
											<div class="tsAppSectionContentContainer">
												<div class="tsAppSectionHeading">#local.lawStudentIDFieldSet.fieldSetContent#</div>
											</div>
										</div>
									</span>
								</div>
							</div>
						</div>
						</cfif>
						
						<div class="CPSection row-fluid">
							<div id="sectionContent" class="tsAppBodyText frmRow1 frmText CPSectionContent P">
								<div class="row-fluid">
									#local.customPageFields.DuesInformation#
								</div>
							</div>
						</div>
												
						<div class="CPSection row-fluid" id="listServeNameHolder">
							<div class="CPSectionTitle BB row-fluid">#local.customPageFields.ListAgreementTitle#</div>
							<div id="sectionContent" class="tsAppBodyText frmRow1 row-fluid frmText CPSectionContent P">
								<!--- List serve subs --->
								<div class="row-fluid">
									<div colspan="2" class="bodyText row-fluid">
										#local.customPageFields.ListServeTopInfoText#
									</div>
									<br />
									<div class="row-fluid">#local.customPageFields.ListServeAddText#</div>
									<div class="row-fluid">
										<cfloop query="#local.qryListSubData#">
											<cfif ListFind(local.customPageFields.AvailableListSubscriptions, local.qryListSubData.subscriptionName)>
												<div class="control-group">
													<div class="controls span12">
														<label class="control-label label-width" for="listServeNames">
															<cfinput type="checkbox" name="listServeNames"  id="listServeNames" value="#local.qryListSubData.subscriptionName#"> Subscribe me to the #local.qryListSubData.subscriptionName# List Serve
														</label>
													</div>
												</div>
											</cfif>
										</cfloop>
									</div>		
								</div>
								
								<div id="listServeAgreementBox" style="display: ruby;">
									#local.customPageFields.ListServeAgreement#
									<span class="listServeCertAgreePrefFieldSetHolder">
										<div class="row-fluid">
											<div class="tsAppSectionHeading">#local.listServeCertAgreePrefFieldSet.fieldSetTitle#</div>								
											<div class="tsAppSectionContentContainer">										
												#local.listServeCertAgreePrefFieldSet.fieldSetContent#									
											</div>
										</div>
									</span>
								</div>
							</div>
						</div>							
						<!--- BUTTONS: =================================================================================================================================== --->
						<div id="formButtons">
							<div class="row-fluid">
								<div align="center" class="frmButtons">
									<input type="submit" value="Continue" class="btn btn-default" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" class="btn btn-default" name="cancel"/>
								</div>
							</div>
						</div>						
					</div>
					<cfinclude template="/model/cfformprotect/cffp.cfm" />
				</cfform>
	
				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
				</cfif>
			</cfcase>
			
			<cfcase value="1">
				<cfset local.result = saveMemberData(event=arguments.event, orgID=local.orgID, useMID=local.useMID)>
				
				<cfif local.result.success eq false>
                    <cfoutput>
                        <div class="tsAppBodyText">
                            <b>An error occurred while setting up your membership.  We are unable to process your membership online at this time.</b>
                            <br>
                            Please contact customer support for assistance.
                            <br><br>
                            We apologize for the inconvenience. 
                        </div>
                    </cfoutput>
                <cfelseif local.isPaymentRequired>
			
					<!---PAYMENT SECTION START--->
					<cfscript>
						// GATEWAY INFORMATION: --------------------------------------------------------------------------------------------------
						local.profile_1.strPaymentForm = 	application.objPayments.showGatewayInputForm(
																			siteid=local.siteID,
																			profilecode=local.profile_1._profileCode,
																			pmid = local.useMID,
																			showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																			usePopupDIVName='paymentTable'
															);
					</cfscript>
					<script type="text/javascript">
						function checkPaymentMethod() {
							var rdo = $('##payMethCC');
							if (rdo[0].checked) {//credit card
								document.getElementById('CCInfo').style.display = '';
							}  
						}
						
						function _validate() {
							var _CF_this = document.forms["#local.formName#"];
							var arrReq = new Array();
							
							if (!_FB_hasValue(_CF_this['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
							var MethodOfPaymentValue = 'CC';
							
							if( MethodOfPaymentValue == 'CC' )	{
								#local.profile_1.strPaymentForm.jsvalidation#
								var confirmation 	= 0;
								var statement			= _CF_this['confirmationStatement'];
								if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
								if(confirmation == 0) arrReq[arrReq.length] = 'Please accept the Confirmation Statement';
							}
							
							if (arrReq.length > 0) {
								var msg = 'The following fields are required:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								return false;
							}
							return true;
						}

						function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
					</script>
					<cfif len(local.profile_1.strPaymentForm.headCode)>
						<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
					</cfif>
					
					<div id="paymentTable" class="row-fluid">
						<div id="payerrDIV" class="row-fluid" style="display:none;margin:6px 0;"></div>
						<div class="form row-fluid">
							<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="#local.customPage.baseURL#" onSubmit="return _validate();">
								<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
								<cfloop collection="#arguments.event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
										and left(local.key,4) neq "fld_">
										<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
									</cfif>
								</cfloop>
								<div class="row-fluid">
									<div class="CPSection row-fluid">
										 <div class="CPSectionTitle BB row-fluid">*Method of Payment</div>
										 <div class="row-fluid P">
											<div class="row-fluid">Please select your preferred method of payment from the options below.</div>
											<div class="row-fluid">
												<div class="control-group">
													<div class="controls span2">
														<label class="radio"><input value="CC" class="tsAppBodyText optionsRadio " name="payMeth" id="payMethCC" type="radio" onClick="javascript:{checkPaymentMethod();}">Credit Card</label>
													</div>
												</div>
											</div>
										</div>
									</div>
									<!--- CREDIT CARD INFO: ----------------------------------------------------------------------------------- --->
									<div id="CCInfo" style="display:none;" class="CPSection row-fluid">
										<div class="CPSectionTitle row-fluid">Credit Card Information</div>
										<div class="PL PR frmText paymentGateway BT BB row-fluid">
											<cfif len(local.profile_1.strPaymentForm.inputForm)>
												<div>#local.profile_1.strPaymentForm.inputForm#</div>
											</cfif>
										</div>
										
										<div class="P row-fluid">
											<div class="PB row-fluid">* Please confirm the statement below:</div>
											<div class="row-fluid">
												<div class="control-group">
													<div class="controls">
														<label class="checkbox"><input name="confirmationStatement" id="confirmationStatement"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  />I confirm that I have full authority to make payment from the above credit card account for my contribution.</label>
													</div>
												</div>
											</div>
										</div>
										
										<div class="P row-fluid"><button type="submit" class="tsAppBodyButton btn btn-default" name="btnSubmit">AUTHORIZE</button></div>
									</div>
									
									<!--- CHECK INFORMATION: ---------------------------------------------------------------------------------- --->
									<div id="CheckInfo" style="display:none;" class="CPSection row-fluid">
										<div class="CPSectionTitle row-fluid">Check Information</div>
										<div class="P row-fluid">
											
													Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
													<strong>Consumer Attorneys of Los Angeles</strong><br />
													800 W 6th St. Suite 700<br />
													Los Angeles, CA 90017
													
										</div>
										<div class="P">
											<button type="submit" class="tsAppBodyButton btn btn-default" name="btnSubmit">CONTINUE</button><br />
											<br />
											<strong>NOTE:</strong> Your membership will not be active until payment is received.
										</div>
									</div>
									
								</div>
								<cfinclude template="/model/cfformprotect/cffp.cfm" />
							</cfform>
						</div>
					</div>
					<!---PAYMENT SECTION END--->
				</cfif>
			</cfcase>
			
			<!--- PROCESS: ====================================================================================================================================== --->
			<cfcase value="2">
				<cfif NOT local.objCffp.testSubmission(form) and not len(trim(event.getValue('confirmationStatement','')))>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>
				
				<cfif NOT local.isPaymentRequired>
					<cfset local.result = saveMemberData(event=arguments.event, orgID=local.orgID, useMID=local.useMID)>
					
					<cfif local.result.success eq false>
						<cfoutput>
							<div class="tsAppBodyText">
								<b>An error occurred while setting up your membership.  We are unable to process your membership online at this time.</b>
								<br>
								Please contact customer support for assistance.
								<br><br>
								We apologize for the inconvenience. 
							</div>
						</cfoutput>
					</cfif>
				</cfif>
				
				
				
				<cfsavecontent variable="local.name">
					#event.getValue('firstName','')# #event.getValue('lastName','')#
				</cfsavecontent>
				
				<cfset local.genderName = "">
				<cfif len(event.getTrimValue('gender'))>
					<cfloop array="#local.qryGender.columnValueArr#" index="local.qryGender">
						<cfif local.qryGender.valueid eq event.getTrimValue('gender')>
							<cfset local.genderName = local.qryGender.columnValueString>
							<cfbreak> 
						</cfif>
					</cfloop>
				</cfif>
				<cfset local.useMID = event.getTrimValue('memberID')>
				<cfset local.memberNumber = event.getTrimValue('memberNumber')>
						
				<cfset local.listServeNames = event.getValue('listServeNames', '')>
				<cfif len(local.listServeNames)>
					<cfif listFind(local.listServeNames, 'CAALA-LEGALSTAFF')>
						<cfset local.qryHistoryNewLawyer = application.objCustomPageUtils.mh_getCategory(siteID=local.siteID, parentCode='JoinList', subName='CAALA-LEGALSTAFF')>
						<cfset application.objCustomPageUtils.mh_addHistory(memberID=local.useMID, categoryID=local.qryHistoryNewLawyer.categoryID, subCategoryID=local.qryHistoryNewLawyer.subCategoryID, description="", enteredByMemberID=local.useMID, newAccountsOnly=false)>
					</cfif>
					<cfif listFind(local.listServeNames, 'CAALA-WOMEN')>
						<cfset local.qryHistoryWomen = application.objCustomPageUtils.mh_getCategory(siteID=local.siteID, parentCode='JoinList', subName='CAALA-WOMEN')>
						<cfset application.objCustomPageUtils.mh_addHistory(memberID=local.useMID, categoryID=local.qryHistoryWomen.categoryID,subCategoryID=local.qryHistoryWomen.subCategoryID, description="", enteredByMemberID=local.useMID, newAccountsOnly=false)>
					</cfif>
				</cfif>
				<cfset local.listServeCertAgreePrefFieldSet = application.objCustomPageUtils.renderFieldSet(uid=local.listServeCertAgreePrefFieldSetUID, mode="confirmation", strData=arguments.event.getCollection())>

				<cfsavecontent variable="local.invoice">
					#local.pageCSS#
					<div class="row-fluid">
						<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
						<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage table">
						
						<tr class="msgHeader"><td colspan="2" class="b">Personal Information</td></tr>
						<!-- @membernumber@ -->
						<tr class="frmRow1"><td class="frmText b" width="50%">Title:</td><td class="frmText">#event.getValue('title','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">First Name:</td><td class="frmText">#event.getValue('firstName')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Middle Initial:</td><td class="frmText">#event.getValue('middleName')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Last Name:</td><td class="frmText">#event.getValue('lastName')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Firm Name:</td><td class="frmText">#event.getValue('firmName')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Email:</td><td class="frmText">#event.getValue('email')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Address 1:</td><td class="frmText">#event.getValue('address1')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">State:</td><td class="frmText">#event.getValue('state')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Phone:</td><td class="frmText">#event.getValue('phone')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Website:</td><td class="frmText">#event.getValue('website')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Referred By:</td><td class="frmText">#event.getValue('referredBy','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Gender:</td><td class="frmText">#local.genderName#&nbsp;</td></tr>					
						<tr class="msgHeader"><td colspan="2" class="b">Membership Dues</td></tr>
						<tr class="frmRow1"><td class="frmText b">#local.membershipCategory#</td><td class="frmText">#dollarFormat(local.membershipDues)#&nbsp;</td></tr>
						<cfif local.membershipCategory eq local.strDues.paralegal.txt>
							<tr class="frmRow2"><td class="frmText b">Law Firm/Office:</td><td class="frmText">#event.getValue('paralegalLawFirm')#&nbsp;</td></tr>
						<cfelseif local.membershipCategory eq local.strDues.student.txt>
							<tr class="frmRow2"><td class="frmText b">Law School:</td><td class="frmText">#event.getValue('lawSchool')#&nbsp;</td></tr>
							<tr class="frmRow2"><td class="frmText b">Expected Graduation Date:</td><td class="frmText">#event.getValue('graduationdate')#&nbsp;</td></tr>
						</cfif>
						<cfif len(event.getValue('listServeNames',''))>
						<tr class="frmRow1"><td class="frmText b">List Serves for Legal Staff Members:</td><td class="frmText">#event.getValue('listServeNames','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText" colspan="2">#local.customPageFields.ListServeAgreement#</td></tr>
						<tr class="frmRow2"><td class="frmText" colspan="2">#local.listServeCertAgreePrefFieldSet.fieldSetContent#</td></tr>
						</cfif>
						<cfif local.isPaymentRequired>
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2">PAYMENT INFORMATION</td></tr>
						<tr><td class="frmText b frmRow1">Payment Type: </td><td class="frmText">
							<cfif event.getValue('payMeth','CC') EQ 'CC'>
									Credit Card
									<cfset arguments.event.setValue('p_#local.profile_1._profileID#_mppid',int(val(arguments.event.getValue('p_#local.profile_1._profileID#_mppid',0)))) />
									<cfif arguments.event.getValue('p_#local.profile_1._profileID#_mppid') gt 0>
										<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
												mppid     = arguments.event.getValue('p_#local.profile_1._profileID#_mppid'),
												memberID  = val(local.useMID),
												profileID = local.profile_1._profileID) />
										- #local.qrySavedInfoOnFile.detail#
									</cfif>
							<cfelse>
								Check by mail
							</cfif>
						</td></tr>
						<tr class="frmRow2"><td class="frmText b">Payment Amount: </td><td class="frmText">#dollarFormat(local.totalAmount)#</td></tr>
						</cfif>
						</table>
					</div>	
				</cfsavecontent>

				<cfset local.invoiceForMember = replace(local.invoice,"<!-- @membernumber@ -->","")>
				<cfset local.invoiceForStaff = replace(local.invoice,'<!-- @membernumber@ -->','<tr class="frmRow1"><td class="frmText b">MemberNumber:</td><td class="frmText"><a href="#arguments.event.getValue("mc_siteinfo.scheme")#://#arguments.event.getValue("mc_siteinfo.mainhostname")#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#val(local.useMID)#">#local.memberNumber#</a>&nbsp;</td></tr>')>

				<cftry>	

					<!--- create pdf and put on member's record --->
					<cfset local.uid = createuuid()>
					<cfset local.currentDate = dateTimeFormat(now())>
					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.sitecode)>
					<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
						<cfoutput>
							<html>
							<body>
								<p>Here are the details of your application:</p>
								#local.invoiceForMember#
							</body>
							</html>
						</cfoutput>
					</cfdocument>
					<cfset local.strPDF = structNew()>
					<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
					<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(local.currentDate,'m-d-yyyy')#.pdf">
					<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
					<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(local.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
					<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
					<cfset application.objCustomPageUtils.mem_storeMembershipApplication(memberID=local.useMID, strPDF=local.strPDF, siteID=local.siteID)>
					<cfcatch type="Any">
						<cfset local.tmpCatch = { type="", message="Unable to add document to member record.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
						<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
					</cfcatch>
				</cftry>
			
				<!---send emails--->
				<!--- email member ---------------------------------------------------------------------------------------------- --->
				<cfset local.emailSentToUser = TRUE />
				<cfif isDefined("local.invoice") AND isDefined("local.invoiceForStaff")>
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<p>Thank you for submitting your application! Please print this page - it is your receipt.</p>	
							<hr />
							<cfif len(trim(local.customPageFields.confirmationEmailTxt))>
								#local.customPageFields.confirmationEmailTxt#	
								<hr />
							</cfif>
							#local.invoice#	
						</cfoutput>
					</cfsavecontent>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.memberEmail.from },
						emailto=[{ name="", email=local.memberEmail.to }],
						emailreplyto= local.ORGEmail.to,
						emailsubject=local.memberEmail.SUBJECT,
						emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
						emailhtmlcontent=local.mailContent,
						siteID=local.siteID,
						memberID=val(local.useMID),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					  )>

					<cfset local.emailSentToUser = local.responseStruct.success />
					
					<cfsavecontent variable="local.mailContent">
						<cfoutput>
							<cfif NOT local.emailSentToUser>
								<p style="color:red;">We were not able to send #local.name# an e-mail confirmation.</p>
							</cfif>
							#local.invoiceForStaff#
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						local.arrEmailTo = [];
						local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
						local.toEmailArr = listToArray(local.ORGEmail.to,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					
						local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.SUBJECT,
							emailtitle="#event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
							emailhtmlcontent=local.mailContent,
							siteID=local.siteID,
							memberID=event.getValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						);
					</cfscript>
				</cfif>
				<!--- set invoice to session and redierct to confirmation--->
				<cfset session.invoice = local.invoiceForMember>
				<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
			</cfcase>
			
			<!--- MESSAGE AFTER THE POST: ======================================================================================================================= --->
			<cfcase value="99">
				<!--- output to screen --->
				<div class="HeaderText">Thank you for submitting your application!</div>
				<br/>
				<cfif isDefined("session.invoice")>
					<div>A confirmation has been sent to the e-mail address on file. If you would like you could also print the page out as a receipt.</div>
					<hr />
					<cfif len(trim(local.customPageFields.screenConfirmationTxt))>
						#local.customPageFields.screenConfirmationTxt#	
						<hr />
					</cfif>
					<div class="BodyText">
						#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
					</div>
				</cfif>
				<cfset structDelete(session,"invoice")>
			</cfcase>
			
			<!--- SPAM MESSAGE: ================================================================================================================================= --->
			<cfcase value="100">
				<div>
					Error! you Can't Post Here.
				</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>

<!--- CUSTOM FUNCTIONS ====================================================================== --->
<cffunction name="saveMemberData" access="public" returntype="struct" output="false">
    <cfargument name="event" type="any" required="true">
	<cfargument name="orgID" type="numeric" required="true">
	<cfargument name="useMID" type="numeric" required="true" default=0>
    <cfset var local = {} />
    <cfset local.result = { success = false, message = "", memberID = 0, memberNumber = "" }>
	
	<cfset local.qryStates	= application.objCommon.getStates()>

    <!--- Get State ID --->
    <cfquery name="local.qryGetStateID" dbtype="query">
        SELECT stateID
        FROM [local].qryStates
        WHERE StateCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('state')#">
    </cfquery>
	<cfset local.qryLawStudentID = application.objCustomPageUtils.mem_getCustomFieldData(orgID=arguments.orgID, columnName="Law Student ID")>

    <cfset local.recordUpdated = false>

    <cftry>
        <cfscript>
            local.newSaveMemberID = 0;
            local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1));

            if (isArray(local.newMemIdArr) AND listFind(arrayToList(local.newMemIdArr), arguments.useMID)) {
                local.newSaveMemberID = arguments.useMID;
            }

            // Save member info
            local.strResult = application.objCustomPageUtils.mem_saveMemberInfo(rc=arguments.event.getCollection(), memberID=local.newSaveMemberID);
            local.newSaveMemberID = local.strResult.memberID;

            local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.newSaveMemberID);
            local.objSaveMember.setDemo(
                prefix=arguments.event.getValue('title',''),
                firstName=arguments.event.getValue('firstName',''),
                middleName=arguments.event.getValue('middleName',''),
                lastName=arguments.event.getValue('lastName',''),
                company=arguments.event.getValue('firmName','')
            );
            local.objSaveMember.setAddress(
                type='Office Address',
                address1=arguments.event.getValue('address1',''),
                city=arguments.event.getValue('city',''),
                stateID=local.qryGetStateID.stateID,
                postalCode=arguments.event.getValue('zip','')
            );
            local.objSaveMember.setPhone(addressType='Office Address', type='Phone', value=arguments.event.getValue('phone',''));
            local.objSaveMember.setRecordType(recordType='Individual');
            local.objSaveMember.setMemberType(memberType='User');
            local.objSaveMember.setMemberStatus(memberStatus='A');

            if (len(arguments.event.getValue('email'))) {
                local.objSaveMember.setEmail(type='Work Email', value=arguments.event.getValue('email'));
            }

            if (isValid("regex", event.getValue('website',''), application.regEx.url)) {
                local.objSaveMember.setWebsite(type='Website', value=event.getValue('website',''));
            }

            if (len(arguments.event.getTrimValue('gender'))) {
                local.objSaveMember.setCustomField(field='Gender', valueId=arguments.event.getTrimValue('gender',''));
            }

            if (len(arguments.event.getTrimValue('referredBy'))) {
                local.objSaveMember.setCustomField(field='Referred By', value=arguments.event.getValue('referredBy'));
            }

            if (event.getValue('memberCategory',0) eq '1') {
                local.contactTypeColumnValue = 'Legal Staff/Paralegal';
            } else if (event.getValue('memberCategory',0) eq '2') {
                local.contactTypeColumnValue = 'Law Student';
            }
            local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeColumnValue);

            if (event.getValue('memberCategory',0) eq '2') {
                local.objSaveMember.setCustomField(field='Law School', value=event.getValue('lawSchool',''));
                local.objSaveMember.setCustomField(field='Graduation Date', value=event.getValue('graduationdate',''));
            }

            local.strResult = local.objSaveMember.saveData();
        </cfscript>

        <cfif local.strResult.success>
            <cfset local.recordUpdated = true>
            <cfset local.result.success = true>
            <cfset local.result.memberID = local.strResult.memberID>
            <cfset local.result.memberNumber = local.strResult.memberNumber>

            <cfset arguments.event.setValue('memberID', local.strResult.memberID)>
            <cfset arguments.event.setValue('memberNumber', local.strResult.memberNumber)>

            <cfset local.fileField = "md_#local.qryLawStudentID.COLUMNID#">
            <cfif NOT structIsEmpty(local.qryLawStudentID) AND structKeyExists(form, local.fileField) AND len(trim(form[local.fileField]))>
                <cfset arguments.event.setValue('fileToUpload','md_#local.qryLawStudentID.COLUMNID#')>
                <cfset application.objCustomPageUtils.saveMemberCustomSingleDocument(local.qryLawStudentID.COLUMNID, local.strResult.memberID)>
            </cfif>
        <cfelse>
            <cfthrow message="Unable to save member.">
        </cfif>

        <cfcatch type="Any">
            <cfset application.objError.sendError(cfcatch=cfcatch)>
            <cfset local.result.success = false>
            <cfset local.result.message = "An error occurred while saving the member.">
        </cfcatch>
    </cftry>

    <cfreturn local.result>
</cffunction>

<cffunction name="getMemberAdditionalData" access="public" returntype="struct">
	<cfargument name="orgID" type="numeric" required="yes">
	<cfargument name="memberID" type="numeric" required="yes">

	<cfset var local = structNew()>
	<cfset local.objMember 								= CreateObject("component","model.admin.members.members") />
	<cfset local.xmlAdditionalData_Member = local.objMember.getMemberAdditionalData(memberid=arguments.memberid) />
	<cfset local.xmlAdditionalData 				= application.objCustomPageUtils.mem_getOrgAdditionalDataColumns(arguments.orgID) />
	<cfset local.returnStruct = StructNew()>
	<cfloop array="#local.xmlAdditionalData.data.XMlChildren#" index="local.column">
		<cfset local.memberColDataActualValue = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@actualColumnValue)")>
		<cfset local.memberColDataValue 			= XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@columnValue)")>
		<cfset local.memberColDataValueID 		= XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@valueID)")>
		<cfset local.columnName 							= local.column.xmlAttributes.columnName />
		<cfset local.returnStruct[local.columnName] = local.memberColDataValue>
	</cfloop>
	<cfreturn local.returnStruct />
</cffunction>

<cffunction name="getMemberData" access="private" returntype="struct">
	<cfargument name="orgID" type="numeric" required="true">
	<cfargument name="siteID" type="numeric" required="true">
	<cfargument name="memberID" type="numeric" required="true">
	<cfset var local = structNew() />
	<cfset local.memberData = structNew()>
	<cfset local.qryOrgAddresses = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgEmails = application.objOrgInfo.getOrgEmailTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgWebsites = application.objOrgInfo.getOrgWebsiteTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgPhones = application.objOrgInfo.getOrgPhoneTypes(orgID=arguments.orgID)>

	<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
		select o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
		from dbo.organizations as o
		where o.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
	</cfquery>
	
	<cfscript>
		local.returnStruct 		= structNew();
		
		local.objMember 		= CreateObject("component","model.admin.members.members");
		local.memberData.qryMember	=  application.objMember.getMemberInfo(int(val(arguments.memberID)),arguments.orgID);
		local.memberData.qryMemberEmails	=  application.objMember.getMemberEmails(memberID=int(val(arguments.memberID)),orgID=arguments.orgID);
		local.memberData.qryMemberWebsites	=  application.objMember.getMemberWebsites(memberID=int(val(arguments.memberID)),orgID=arguments.orgID);
		local.memberData.qryMemberAddresses	=  local.objMember.getMember_addresses(memberID=int(val(arguments.memberID)),orgID=arguments.orgID);

		local.customMemberData	= getMemberAdditionalData(orgID=arguments.orgID,memberID=arguments.memberID);
	</cfscript>
	
	<cfsavecontent variable="local.memberNamePrinted">
		<cfoutput>
		<cfif local.qryOrgMemberFields.hasprefix is 1 and len(local.memberData.qryMember.prefix)>#local.memberData.qryMember.prefix#</cfif> 
		#local.memberData.qryMember.firstname# 
		<cfif local.qryOrgMemberFields.hasmiddlename is 1 and len(local.memberData.qryMember.middlename)>#local.memberData.qryMember.middlename#</cfif> 
		#local.memberData.qryMember.lastname# 
		<cfif local.qryOrgMemberFields.hassuffix is 1 and len(local.memberData.qryMember.suffix)>#local.memberData.qryMember.suffix#</cfif> 
		<cfif local.qryOrgMemberFields.hasprofessionalsuffix is 1 and len(local.memberData.qryMember.professionalsuffix)>#local.memberData.qryMember.professionalsuffix#</cfif>
		</cfoutput>
	</cfsavecontent>
	
	<cfsavecontent variable="local.memberName">
		<cfoutput>
		#local.memberData.qryMember.firstname# #local.memberData.qryMember.lastname# 
		</cfoutput>
	</cfsavecontent>
	
	<!--- Emails --->
	<cfsavecontent variable="local.memberEmails">
		<cfoutput>
			<cfloop query="local.qryOrgEmails">
				<cfset local.tmpEmailTypeID = local.qryOrgEmails.emailTypeID>
				<cfquery name="local.qryEmailInfo" dbtype="query">
					select email
					from [local].memberData.qryMemberEmails
					where emailTypeID = #local.tmpEmailTypeID#
				</cfquery>		
				#local.qryOrgEmails.emailType#: <cfif len(local.qryEmailInfo.email)>#local.qryEmailInfo.email#<cfelse><span class="dim"><i>not specified</i></span></cfif><br/>
			</cfloop>
		</cfoutput>
	</cfsavecontent>

	<!--- Websites --->
	<cfsavecontent variable="local.memberWebsites">
		<cfoutput>
			<cfloop query="local.qryOrgWebsites">
				<cfset local.tmpWebsiteTypeID = local.qryOrgWebsites.WebsiteTypeID>
				<cfquery name="local.qryWebsiteInfo" dbtype="query">
					select Website
					from [local].memberData.qryMemberWebsites
					where WebsiteTypeID = #local.tmpWebsiteTypeID#
				</cfquery>		
				#local.qryOrgWebsites.WebsiteType#: <cfif len(local.qryWebsiteInfo.Website)>#local.qryWebsiteInfo.Website#<cfelse><span class="dim"><i>not specified</i></span></cfif><br/>
			</cfloop>
		</cfoutput>
	</cfsavecontent>
			
	<!--- addresses, phones --->
	<cfloop query="local.qryOrgAddresses">
		<cfset local.tmpAddressTypeID = local.qryOrgAddresses.addressTypeID />
		<cfset local.tmpAddressType 	= local.qryOrgAddresses.addressType />
		<cfset local.hasAttn 					= local.qryOrgAddresses.hasAttn />
		<cfset local.hasAddress2 			= local.qryOrgAddresses.hasAddress2 />
		<cfset local.hasAddress3 			= local.qryOrgAddresses.hasAddress3 />
		<cfset local.hasCounty 				= local.qryOrgAddresses.hasCounty />
		<cfquery name="local.qryAddressInfo" dbtype="query">
			select attn, address1, address2, address3, city, stateCode, postalcode, county, country
			from [local].memberData.qryMemberAddresses
			where addressTypeID = #local.tmpAddressTypeID#
		</cfquery>	
		<cfsavecontent variable="local.thisaddrFull">
			<cfoutput>
				<cfif local.hasAttn and len(local.qryAddressInfo.attn)>#local.qryAddressInfo.attn#<br/></cfif>
				<cfif len(local.qryAddressInfo.address1)>#local.qryAddressInfo.address1#<br/></cfif>
				<cfif local.hasAddress2 and len(local.qryAddressInfo.address2)>#local.qryAddressInfo.address2#<br/></cfif>
				<cfif local.hasAddress3 and len(local.qryAddressInfo.address3)>#local.qryAddressInfo.address3#<br/></cfif>
				<cfif len(local.qryAddressInfo.city)>#local.qryAddressInfo.city#</cfif> 
				<cfif len(local.qryAddressInfo.stateCode)>#local.qryAddressInfo.stateCode#</cfif> 
				<cfif len(local.qryAddressInfo.postalcode)>#local.qryAddressInfo.postalcode#</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfif local.hasCounty and len(local.qryAddressInfo.county)>
			<cfsavecontent variable="local.thisaddrFull">
				<cfoutput><cfif len(local.thisaddrFull)>#local.thisaddrFull#<br/></cfif>#local.qryAddressInfo.county#</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfif len(local.qryAddressInfo.country)>
			<cfsavecontent variable="local.thisaddrFull">
				<cfoutput><cfif len(local.thisaddrFull)>#local.thisaddrFull#<br/></cfif>#local.qryAddressInfo.country#</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfsavecontent variable="local.thisaddrPhonesFull">
			<cfoutput>
				<table>
				<cfloop query="local.qryOrgPhones">
					<cfset local.phoneTypeID = local.qryOrgPhones.phoneTypeID>
					<cfquery name="local.qryPhoneInfo" dbtype="query">
						select phone
						from [local].memberData.qryMemberAddresses
						where addressTypeID = #local.tmpAddressTypeID#
						and phoneTypeID = #local.phoneTypeID#
					</cfquery>	
					<cfif len(local.qryPhoneInfo.phone)>
						<tr valign="top">
							<td class="tsAppBodyText frmText">#local.qryOrgPhones.phoneType#: &nbsp;</td>
							<td class="tsAppBodyText frmText">#local.qryPhoneInfo.phone#</td>
						</tr>
					</cfif>
				</cfloop>
				</table>
			</cfoutput>
		</cfsavecontent>
	</cfloop>
	<cfset local.returnStruct['Email']				= local.memberData.qryMemberEmails.email />
	<cfset local.returnStruct['memberName'] 		= local.memberName />
	<cfset local.returnStruct['memberNamePrinted'] 	= ReReplace(trim(local.memberNamePrinted),"\s{2,}"," ","ALL")>
	<cfset local.returnStruct['company']			= local.memberData.qryMember.company />
	<cfset local.returnStruct['memberEmails'] 		= local.memberEmails />
	<cfset local.returnStruct['memberWebsites'] 	= local.memberWebsites />
	<cfset local.returnStruct['tmpAddressType']		= local.tmpAddressType />
	<cfset local.returnStruct['thisaddrFull'] 		= local.thisaddrFull />
	<cfset local.returnStruct['thisaddrPhonesFull'] = local.thisaddrPhonesFull />
	<cfset local.returnStruct['customData']			= local.customMemberData />
	<cfset local.returnStruct['qryOrgMemberFields']	= local.qryOrgMemberFields />

	<cfreturn local.returnStruct />
</cffunction>