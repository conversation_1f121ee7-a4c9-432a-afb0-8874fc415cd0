<cfoutput>
<h6 class="fw-semibold mb-3">Payment</h6>

<div class="form-floating mb-1">
	<input type="text" class="form-control form-control-sm shadow-sm" name="caseref" id="caseref" value="#local.dataStruct.qryAIExpert.caseReference#" aria-describedby="Case Reference">
	<label for="caseref">Case Reference</label>
</div>
<div class="form-text mb-3"><small>Add reference which will appear on your monthly statement.</small></div>

<cfset local.strPaymentForm = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM").gather(merchantOrgcode='TS', customerid='olddid_#session.cfcuser.memberdata.depomemberdataid#', editMode='frontEndPayment',
			autoShowForm=1, chargeInfo={ "amt":local.strTotals.totalPrice, "acceptApplePay":1, "acceptGooglePay":1 })>
<cfif len(local.strPaymentForm.headcode)>
	#local.strPaymentForm.headcode#
</cfif>
<cfif len(local.strPaymentForm.inputForm)>
	<div id="CIMTable" class="my-3">
		<div>#replaceNoCase(local.strPaymentForm.inputForm,'fld_','p_TS_fld_','ALL')#</div>
	</div>
</cfif>

<cfif len(local.strPaymentForm.jsvalidation)>
	<cfsavecontent variable="local.extrapayJS">
		<cfoutput>
		#replaceNoCase(local.strPaymentForm.jsvalidation,'fld_','p_TS_fld_','ALL')#
		</cfoutput>
	</cfsavecontent>
</cfif>

<cfset local.strSiteInfo = application.objSiteInfo.getSiteInfo("TS")>
<cfset local.strOrgIdentityInfo = CreateObject("component","model.admin.organization.organization").getOrgIdentityDetailsStruct(orgID=local.strSiteInfo.orgID, orgIdentityID=local.strSiteInfo.defaultOrgIdentityID)>
<cfset local.qrySecureData = createObject("component","model.admin.custom.mc.mc.PlatformSettings").getPlatformSettingsContent(contentTitle='Platformwide_Secure_Checkout_Policy')>

<cfset local.arrEcomLinks = []>
<cfif local.qrySecureData.rawContent neq "">
	<cfset local.arrEcomLinks.append('<a href="/?pg=buyNow&viewPolicy=secureCheckout" class="text-muted mt-sm-2" target="_blank">Secure Checkout</a>')>
</cfif>
<cfif StructKeyExists(local.strSiteInfo,"deliveryPolicyURL") AND len(local.strSiteInfo.deliveryPolicyURL)>
	<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=delivery" class="text-muted mt-sm-2" target="_blank">Delivery Policy</a>')>
</cfif>
<cfif StructKeyExists(local.strSiteInfo,"privacyPolicyURL") AND len(local.strSiteInfo.privacyPolicyURL)>
	<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=privacy" class="text-muted mt-sm-2" target="_blank">Privacy Policy</a>')>
</cfif>
<cfif StructKeyExists(local.strSiteInfo,"rrPolicyURL") AND len(local.strSiteInfo.rrPolicyURL)>
	<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=refund" class="text-muted mt-sm-2" target="_blank">Refunds and Returns</a>')>
</cfif>
<cfif StructKeyExists(local.strSiteInfo,"tcURL") AND len(local.strSiteInfo.tcURL)>
	<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=terms" class="text-muted mt-sm-2" target="_blank">Terms and Conditions</a>')>
</cfif>

<div class="text-center mb-5" style="font-size:11px;">
	<cfif arrayLen(local.arrEcomLinks)>
		<div>#arrayToList(local.arrEcomLinks,' | ')#</div>
	</cfif>
	<address class="text-muted mb-0">
		#local.strOrgIdentityInfo.orgname# <span>#local.strOrgIdentityInfo.address1#<cfif len(local.strOrgIdentityInfo.address2)> #local.strOrgIdentityInfo.address2#</cfif><cfif len(local.strOrgIdentityInfo.city)> #local.strOrgIdentityInfo.city#</cfif><cfif len(local.strOrgIdentityInfo.state)>, #local.strOrgIdentityInfo.state#</cfif><cfif len(local.strOrgIdentityInfo.postalcode)> #local.strOrgIdentityInfo.postalcode#</cfif></span><br/>
		#local.strOrgIdentityInfo.phone# <cfif len(local.strOrgIdentityInfo.email)><a href="mailto:#local.strOrgIdentityInfo.email#" class="text-muted">#local.strOrgIdentityInfo.email#</a></cfif>
	</address>
</div>

<div class="d-flex justify-content-between mb-2">
	<span class="fw-semibold">Subtotal</span>
	<span class="text-success">#replace(dollarformat(local.strTotals.subTotal),".00","")#</span>
</div>
<hr class="my-1">

<div class="d-flex justify-content-between mb-2">
	<span class="fw-semibold">Tax</span>
	<span class="text-success">#replace(dollarformat(local.strTotals.totalSalesTax),".00","")#</span>
</div>
<hr class="my-1">

<div class="d-flex justify-content-between mb-3">
	<span class="fw-bold">Total</span>
	<span class="fw-bold fs-5 text-success">#replace(dollarformat(local.strTotals.totalPrice),".00","")#</span>
</div>
<button type="button" id="btnProcessDepoCasePayment" class="btn btn-primary w-100" onclick="PTSAI_processPaymentOfDepoCases();">Pay #replace(dollarformat(local.strTotals.totalPrice),".00","")#</button>
</cfoutput>

<cfsavecontent variable="local.pageHead">
	<cfoutput>
	<script type="text/javascript">
		function hideAlert() { PTSAI_hideAlert('err_frmPurchaseDepoDocs'); }
		function showAlert(msg) {
			PTSAI_showAlert('err_frmPurchaseDepoDocs',msg);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageHead)#">